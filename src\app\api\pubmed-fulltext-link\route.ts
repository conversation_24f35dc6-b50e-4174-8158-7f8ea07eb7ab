import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const pmid = request.nextUrl.searchParams.get('pmid');
  if (!pmid) {
    return NextResponse.json({ error: 'Missing pmid parameter' }, { status: 400 });
  }

  try {
    const res = await fetch(`https://pubmed.ncbi.nlm.nih.gov/${pmid}/`, { next: { revalidate: 0 } });
    if (!res.ok) throw new Error('failed to fetch pubmed page');
    const html = await res.text();

    const divMatch = html.match(/<div[^>]*class=["'][^"']*full-text-links-list[^"']*["'][^>]*>([\s\S]*?)<\/div>/i);
    if (!divMatch) {
      return NextResponse.json({ pdfLink: null, pageLink: null }, { headers: { 'Cache-Control': 'no-store' } });
    }

    const content = divMatch[1];
    let pdfLink: string | null = null;
    let pageLink: string | null = null;

    const anchorRegex = /<a[^>]*href=["']([^"']+)["'][^>]*>/gi;
    let match;
    while ((match = anchorRegex.exec(content))) {
      const link = match[1];
      if (!pageLink) pageLink = link;
      if (link.toLowerCase().includes('.pdf')) {
        pdfLink = link;
        break;
      }
    }

    return NextResponse.json(
      { pdfLink, pageLink },
      { headers: { 'Cache-Control': 'no-store' } }
    );
  } catch (err) {
    console.error('Full text link error', err);
    return NextResponse.json({ pdfLink: null, pageLink: null }, { status: 500 });
  }
}
