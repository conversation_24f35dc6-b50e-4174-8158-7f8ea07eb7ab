"use client";

import React, { useState } from "react";
import { Plus, Download } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { SubpageHeader } from "@/components/SubpageHeader";

interface LabelSection {
  title: string;
  content: string;
}

interface LabelData {
  id: string;
  title: string;
  sections: LabelSection[];
}

export default function DrugLabelComparatorPage() {
  const [ids, setIds] = useState<string[]>([""]);
  const [labels, setLabels] = useState<LabelData[]>([]);
  const [loading, setLoading] = useState(false);

  const handleIdChange = (index: number, value: string) => {
    const newIds = [...ids];
    newIds[index] = value;
    setIds(newIds);
  };

  const addIdField = () => {
    setIds([...ids, ""]);
  };

  const fetchLabels = async () => {
    setLoading(true);
    const results: LabelData[] = [];

    for (const id of ids) {
      if (!id) continue;
      try {
        const res = await fetch(
          `https://dailymed.nlm.nih.gov/dailymed/services/v2/spls/${id}.json`,
        );
        if (!res.ok) continue;
        const data = await res.json();
        const label = Array.isArray(data.data) ? data.data[0] : data.data || data;
        const sections: LabelSection[] = [];

        if (label?.sections && Array.isArray(label.sections)) {
          label.sections.forEach((sec: any) => {
            sections.push({ title: sec.title, content: sec.text || sec.content });
          });
        } else if (label?.document?.sections) {
          const arr = Array.isArray(label.document.sections)
            ? label.document.sections
            : Object.values(label.document.sections);
          arr.forEach((sec: any) => {
            sections.push({ title: sec.title, content: sec.text || sec.content });
          });
        }

        results.push({
          id,
          title: label.title || id,
          sections,
        });
      } catch (e) {
        console.error("Failed to fetch label", id, e);
      }
    }

    setLabels(results);
    setLoading(false);
  };

  const allSectionTitles = Array.from(
    new Set(labels.flatMap((l) => l.sections.map((s) => s.title))),
  );

  const getSectionContent = (label: LabelData, title: string) => {
    const sec = label.sections.find((s) => s.title === title);
    return sec ? sec.content : "";
  };

  const exportCSV = () => {
    let csv = "Section," + labels.map((l) => l.title).join(",") + "\n";
    allSectionTitles.forEach((title) => {
      const row = labels
        .map((l) => {
          const text = getSectionContent(l, title).replace(/\n/g, " ").replace(/"/g, '""');
          return `"${text}"`;
        })
        .join(",");
      csv += `"${title}",${row}\n`;
    });
    const blob = new Blob([csv], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = "labels.csv";
    link.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <SubpageHeader current="drug-label-comparator" />

      <h1 className="text-3xl font-bold mb-6">Drug Label Comparator</h1>

      <div className="space-y-2 mb-4">
        {ids.map((id, idx) => (
          <Input
            key={idx}
            placeholder="Enter DailyMed Set ID"
            value={id}
            onChange={(e) => handleIdChange(idx, e.target.value)}
          />
        ))}
        <Button type="button" variant="secondary" onClick={addIdField}>
          <Plus className="h-4 w-4 mr-1" /> Add another ID
        </Button>
        <Button type="button" onClick={fetchLabels} disabled={loading}>
          {loading ? "Loading..." : "Compare"}
        </Button>
      </div>

      {labels.length > 0 && (
        <div className="mb-4">
          <Button variant="outline" onClick={exportCSV}>
            <Download className="h-4 w-4 mr-1" /> Export CSV
          </Button>
        </div>
      )}

      {labels.length > 0 && (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Section</TableHead>
              {labels.map((l, idx) => (
                <TableHead key={idx}>{l.title}</TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {allSectionTitles.map((title) => (
              <TableRow key={title}>
                <TableCell className="font-medium">{title}</TableCell>
                {labels.map((l, idx) => (
                  <TableCell key={idx} className="prose max-w-md">
                    <div
                      dangerouslySetInnerHTML={{
                        __html: getSectionContent(l, title),
                      }}
                    />
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}
    </div>
  );
}