#!/usr/bin/env python3
"""
CDA-AMC News Scraper
Scrapes https://www.cda-amc.ca/news-and-events/news
and builds either an RSS feed or text report.
"""

import requests
from bs4 import BeautifulSoup
from datetime import datetime
import xml.etree.ElementTree as ET
from urllib.parse import urljoin
import re
import sys
import time

class CDAScraper:
    def __init__(self):
        self.base_url = "https://www.cda-amc.ca"
        self.target_path = "/news-and-events/news"
        self.target_url = urljoin(self.base_url, self.target_path)
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) '
                          'AppleWebKit/537.36 (KHTML, like Gecko) '
                          'Chrome/91.0.4472.124 Safari/537.36'
        })
        self.max_pages = 3  # how many pages of news to crawl

    def fetch_page(self, url):
        """Fetch webpage content with simple retry logic."""
        for attempt in range(3):
            try:
                r = self.session.get(url, timeout=15)
                r.raise_for_status()
                return r.text
            except requests.RequestException as e:
                print(f"[WARN] Attempt {attempt+1} failed for {url}: {e}")
                time.sleep(2)
        return None

    def parse_date(self, date_str):
        """Try multiple date formats, fallback to now."""
        for fmt in (
            "%B %d, %Y",   # e.g. May 6, 2025
            "%b %d, %Y",   # e.g. May 6, 2025
            "%d %B %Y",    # 06 May 2025
            "%d %b %Y",    # 06 May 2025
            "%Y-%m-%d",    # 2025-05-06
            "%d/%m/%Y"     # 06/05/2025
        ):
            try:
                return datetime.strptime(date_str.strip(), fmt)
            except ValueError:
                continue
        return datetime.now()

    def clean_text(self, text):
        """Normalize whitespace and strip cruft."""
        if not text:
            return ""
        txt = re.sub(r'\s+', ' ', text).strip()
        # drop nav terms, copyright lines, etc.
        for pat in [r'\b(Home|News & Events|Subscribe)\b',
                    r'©.*',
                    r'Skip to.*']:
            txt = re.sub(pat, "", txt, flags=re.IGNORECASE).strip()
        return txt

    def extract_updates_targeted(self, html_content):
        """
        Parse each .views-row on the CDA-AMC news listing:
        - title/link    : .views-field-title > a
        - description   : .views-field-body > .field-content
        - date          : .views-field-created time.text
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        updates = []
        seen = set()

        for row in soup.select('div.views-row'):
            # Title + link
            a = row.select_one('div.views-field-title a[href]')
            if not a:
                continue
            href = a['href'].strip()
            full_url = urljoin(self.base_url, href)
            if full_url in seen:
                continue
            seen.add(full_url)
            title = self.clean_text(a.get_text())

            # Description
            desc_el = row.select_one('div.views-field-body .field-content')
            description = self.clean_text(desc_el.get_text()) if desc_el else ""

            # Date
            time_el = row.select_one('div.views-field-created time')
            date_str = time_el.get_text().strip() if time_el else ""

            updates.append({
                'date':        date_str,
                'title':       title,
                'link':        full_url,
                'description': description
            })

        # Sort newest first
        updates.sort(key=lambda u: self.parse_date(u['date']), reverse=True)
        return updates


    def create_rss_xml(self, updates):
        """Build a simple RSS feed from updates."""
        rss = ET.Element('rss', version="2.0",
                         **{'xmlns:atom': "http://www.w3.org/2005/Atom"})
        ch = ET.SubElement(rss, 'channel')
        for tag, txt in [
            ('title', "CDA-AMC News"),
            ('link',  self.target_url),
            ('description', "Latest news from Canada’s Drug Agency"),
            ('language', "en"),
            ('copyright', f"© CDA-AMC {datetime.now().year}")
        ]:
            e = ET.SubElement(ch, tag)
            e.text = txt

        for u in updates:
            item = ET.SubElement(ch, 'item')
            ET.SubElement(item, 'title').text = u['title']
            ET.SubElement(item, 'link').text  = u['link']
            ET.SubElement(item, 'guid').text  = u['link']
            ET.SubElement(item, 'description').text = f"<p>{u['description']}</p>"
            pubdate = self.parse_date(u['date'])\
                          .strftime("%a, %d %b %Y %H:%M:%S +0000")
            ET.SubElement(item, 'pubDate').text = pubdate

        return rss

    def format_as_text(self, updates):
        lines = [
            "CDA-AMC NEWS",
            "="*40,
            f"Scraped from: {self.target_url}",
            f"On: {datetime.now():%Y-%m-%d %H:%M:%S}",
            ""
        ]
        for i, u in enumerate(updates, 1):
            lines += [
                f"{i}. {u['date']}",
                f"   Title: {u['title']}",
                f"   Link: {u['link']}",
                f"   Summary: {u['description']}",
                ""
            ]
        return "\n".join(lines)

    def scrape(self, output_format='xml'):
        all_updates = []
        # handle pagination: page=0 (no param), then page=1,2,...
        for page in range(self.max_pages):
            url = (f"{self.target_url}?page={page}"
                   if page > 0 else self.target_url)
            print(f"[INFO] Fetching {url}")
            html = self.fetch_page(url)
            if not html:
                break
            page_updates = self.extract_updates_targeted(html)
            if not page_updates:
                break
            # stop if no new ones
            new = [u for u in page_updates
                   if u['link'] not in {x['link'] for x in all_updates}]
            if not new:
                break
            all_updates.extend(new)

        if not all_updates:
            print("[WARN] No updates found.")
            return None

        if output_format.lower().startswith('txt'):
            return self.format_as_text(all_updates)
        else:
            rss = self.create_rss_xml(all_updates)
            raw = ET.tostring(rss, 'utf-8')
            # pretty‐print
            from xml.dom import minidom
            pretty = minidom.parseString(raw).toprettyxml(indent="  ")
            # strip XML decl
            return "\n".join(pretty.splitlines()[1:])

def main():
    scraper = CDAScraper()
    fmt = 'xml'
    if len(sys.argv) > 1 and sys.argv[1].lower() in ('txt','text'):
        fmt = 'text'

    out = scraper.scrape(fmt)
    if out:
        print(out)
        fname = f"web_search_and_scraping/output/cda_news.{fmt}"
        with open(fname, 'w', encoding='utf-8') as f:
            if fmt == 'xml':
                f.write('<?xml version="1.0" encoding="utf-8"?>\n')
            f.write(out)
        print(f"[INFO] Saved to {fname}")
    else:
        print("[ERROR] Scrape returned no data.")

if __name__ == "__main__":
    main()
