import { NextRequest, NextResponse } from 'next/server';

function logWithStackTrace(message: string, ...args: any[]) {
  console.log(message, ...args);
  console.log(new Error().stack);
}

export async function GET(request: NextRequest) {
  const pmid = request.nextUrl.searchParams.get('pmid');
  if (!pmid) {
    return NextResponse.json({ error: 'Missing pmid parameter' }, { status: 400 });
  }

  try {
    // Use the PMC ID Converter API which can return the PMCID for a PMID
    const apiUrl = `https://www.ncbi.nlm.nih.gov/pmc/utils/idconv/v1.0/?ids=${pmid}&format=json&versions=yes`;
    const res = await fetch(apiUrl, { next: { revalidate: 0 } });
    if (!res.ok) {
      throw new Error('idconv request failed');
    }
    const data = await res.json();
    const record = data?.records?.[0];
    const pmcid = record?.pmcid as string | undefined;

    if (!pmcid) {
      // No PMC ID found
      return NextResponse.json(
        { pdfUrl: null, pmcid: null },
        { headers: { 'Cache-Control': 'no-store' } }
      );
    }

    // Always use the direct PDF URL format
    const directPdfUrl = `https://www.ncbi.nlm.nih.gov/pmc/articles/${pmcid}/pdf/${pmcid}.pdf`;
    
    logWithStackTrace('Found PMC PDF for', pmid, '->', directPdfUrl);
    return NextResponse.json(
      { pdfUrl: directPdfUrl, pmcid },
      { headers: { 'Cache-Control': 'no-store' } }
    );
  } catch (err) {
    console.error('PMC PDF link error', err);
    return NextResponse.json(
      { pdfUrl: null, pmcid: null },
      { status: 500, headers: { 'Cache-Control': 'no-store' } }
    );
  }
}
