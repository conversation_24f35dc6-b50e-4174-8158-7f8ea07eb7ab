#!/usr/bin/env python3
"""
CMS Newsroom Scraper — Creates RSS feed from CMS newsroom
Renders https://www.cms.gov/about-cms/contact/newsroom
and extracts every .views-row element into an RSS feed.
"""

import time, re, sys
from datetime import datetime
from urllib.parse import urljoin
import xml.etree.ElementTree as ET

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class CMSScraper:
    def __init__(self):
        self.base_url   = "https://www.cms.gov"
        self.target_url = "https://www.cms.gov/about-cms/contact/newsroom"
        
        opts = Options()
        opts.add_argument("--headless=new")
        opts.add_argument("--no-sandbox")
        opts.add_argument("--disable-dev-shm-usage")
        opts.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
        self.driver = webdriver.Chrome(options=opts)

    def clean(self, txt):
        return re.sub(r"\s+", " ", txt).strip()

    def parse_date(self, datetime_str, fallback_text=""):
        """Parse datetime attribute or fallback to text parsing"""
        try:
            # Try parsing ISO datetime first (from datetime attribute)
            if datetime_str and 'T' in datetime_str:
                return datetime.fromisoformat(datetime_str.replace('Z', '+00:00')).replace(tzinfo=None)
        except:
            pass
        
        try:
            # Try parsing text format like "May 27, 2025"
            if fallback_text:
                # Clean up the text and try common formats
                clean_text = re.sub(r'[^\w\s,]', '', fallback_text).strip()
                return datetime.strptime(clean_text, "%b %d, %Y")
        except:
            pass
            
        return datetime.now()

    def scrape(self):
        print(f"[INFO] Loading {self.target_url}")
        self.driver.get(self.target_url)

        # Wait for the content to load
        try:
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "div.view-content"))
            )
            print("[INFO] Content container found")
        except:
            print("[ERROR] Could not find content container")
            self.driver.quit()
            return []

        # Scroll to load any dynamic content
        self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(3)

        # Collect every views-row element
        rows = self.driver.find_elements(By.CSS_SELECTOR, "div.views-row")
        print(f"[INFO] Found {len(rows)} news items")
        
        updates = []
        for row in rows:
            try:
                # Extract date from time element
                time_element = row.find_element(By.CSS_SELECTOR, "time")
                datetime_attr = time_element.get_attribute("datetime") if time_element else ""
                time_text = time_element.text.strip() if time_element else ""
                
                pub_dt = self.parse_date(datetime_attr, time_text)

                # Extract category from badge
                try:
                    badge = row.find_element(By.CSS_SELECTOR, "span.ds-c-badge")
                    category = self.clean(badge.text)
                except:
                    category = "General"

                # Extract title
                try:
                    title_element = row.find_element(By.CSS_SELECTOR, "h3")
                    title = self.clean(title_element.text)
                except:
                    title = "No title"

                # Extract link
                try:
                    link_element = row.find_element(By.CSS_SELECTOR, "a.newsroom-main-view-link")
                    href = link_element.get_attribute("href").strip()
                    
                    # Handle relative URLs
                    if href.startswith('/'):
                        link = urljoin(self.base_url, href)
                    else:
                        link = href
                except:
                    link = self.target_url  # fallback

                # Extract description
                try:
                    desc_element = row.find_element(By.CSS_SELECTOR, "span.newsroom-main-view-body")
                    desc = self.clean(desc_element.text)
                    # Limit description length
                    if len(desc) > 300:
                        desc = desc[:297] + "..."
                except:
                    desc = title

                # Determine if it's highlighted (we can use category or other indicators)
                is_highlighted = category.lower() == "press releases"

                print(f"[DEBUG] Processed: {title[:50]}... | Date: {pub_dt.strftime('%Y-%m-%d')} | Category: {category}")

                updates.append({
                    "title": title,
                    "link": link,
                    "description": desc,
                    "pub_dt": pub_dt,
                    "category": category,
                    "highlighted": is_highlighted
                })

            except Exception as e:
                print(f"[WARN] Error processing row: {e}")
                continue

        self.driver.quit()
        
        # Sort newest first
        updates.sort(key=lambda u: u["pub_dt"], reverse=True)
        print(f"[INFO] Successfully extracted {len(updates)} items")
        return updates

    def create_rss(self, updates):
        rss = ET.Element("rss", version="2.0", **{"xmlns:atom": "http://www.w3.org/2005/Atom"})
        ch = ET.SubElement(rss, "channel")
        
        for tag, txt in [
            ("title", "CMS | Newsroom"),
            ("link", self.target_url),
            ("description", "Latest news and updates from the Centers for Medicare & Medicaid Services (CMS)"),
            ("language", "en-us"),
            ("copyright", f"© CMS {datetime.now():%Y}"),
            ("lastBuildDate", datetime.now().strftime("%a, %d %b %Y %H:%M:%S +0000"))
        ]:
            e = ET.SubElement(ch, tag)
            e.text = txt

        # Add atom:link for RSS best practices
        atom_link = ET.SubElement(ch, "{http://www.w3.org/2005/Atom}link")
        atom_link.set("href", self.target_url)
        atom_link.set("rel", "self")
        atom_link.set("type", "application/rss+xml")

        for u in updates:
            item = ET.SubElement(ch, "item")
            
            # Use title as-is without fire icon
            title_text = u["title"]
            
            ET.SubElement(item, "title").text = title_text
            ET.SubElement(item, "link").text = u["link"]
            ET.SubElement(item, "guid").text = u["link"]
            
            # Enhanced description with category
            desc_html = f"<p>{u['description']}</p>"
            if u.get("category") and u["category"] != "General":
                desc_html += f"<p><strong>Category:</strong> {u['category']}</p>"
            
            ET.SubElement(item, "description").text = desc_html
            
            pub = u["pub_dt"].strftime("%a, %d %b %Y 12:00:00 +0000")
            ET.SubElement(item, "pubDate").text = pub
            
            # Add category as RSS category
            if u.get("category") and u["category"] != "General":
                ET.SubElement(item, "category").text = u["category"]

        return rss

def main():
    fmt = "xml"
    if len(sys.argv) > 1 and sys.argv[1].lower() in ("text", "txt"):
        fmt = "text"

    scraper = CMSScraper()
    
    try:
        updates = scraper.scrape()
        
        if not updates:
            print("[ERROR] No data scraped")
            return

        if fmt == "text":
            out = "\n".join(
                f"{i}. {u['pub_dt'].date()} — {u['title']}\n   {u['link']}\n   Category: {u.get('category', 'General')}"
                for i, u in enumerate(updates, start=1)
            )
        else:
            rss = scraper.create_rss(updates)
            raw = ET.tostring(rss, "utf-8")
            from xml.dom import minidom
            pretty = minidom.parseString(raw).toprettyxml(indent="  ")
            # Drop XML declaration line
            out = "\n".join(pretty.splitlines()[1:])

        if out:
            print(out)
            
            # Create output directory if it doesn't exist
            import os
            os.makedirs("web_search_and_scraping/output", exist_ok=True)
            
            fname = f"web_search_and_scraping/output/cms_newsroom.{fmt}"
            with open(fname, "w", encoding="utf-8") as f:
                if fmt == "xml":
                    f.write('<?xml version="1.0" encoding="utf-8"?>\n')
                f.write(out)
            print(f"[INFO] Saved to {fname}")
        else:
            print("[ERROR] No output generated")
            
    except Exception as e:
        print(f"[ERROR] Scraping failed: {e}")

if __name__ == "__main__":
    main()
