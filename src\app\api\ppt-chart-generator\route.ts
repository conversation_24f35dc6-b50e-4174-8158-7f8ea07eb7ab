import { NextRequest, NextResponse } from "next/server";
import { exec } from "child_process";
import { promisify } from "util";
import fs from "fs";
import path from "path";
import os from "os";

const execAsync = promisify(exec);

export async function POST(req: NextRequest) {
  try {
    const { text, chart } = (await req.json()) as {
      text?: string;
      chart?: any;
    };
    if (!text && !chart) {
      return NextResponse.json({ error: "Missing input" }, { status: 400 });
    }

    const preview = req.nextUrl.searchParams.get("preview") === "1";

    const tmpDir = await fs.promises.mkdtemp(path.join(os.tmpdir(), "ppt-"));
    const scriptPath = path.join(
      process.cwd(),
      "scripts",
      "ppt_chart_generator.py",
    );
    const encoded = text ? Buffer.from(text, "utf8").toString("base64") : "";
    const chartEncoded = chart
      ? Buffer.from(JSON.stringify(chart), "utf8").toString("base64")
      : "";

    let command = `python3 "${scriptPath}" "${tmpDir}" "${encoded}"`;
    if (chartEncoded) {
      command += ` --chart_b64 "${chartEncoded}"`;
    }

    let stdout = "";
    try {
      ({ stdout } = await execAsync(command, {
        maxBuffer: 20 * 1024 * 1024,
        env: { ...process.env, PPTX_DIR: tmpDir },
      }));
    } catch (err: any) {
      console.error("ppt-chart-generator exec error:", err);
      await fs.promises.rm(tmpDir, { recursive: true, force: true });
      return NextResponse.json(
        {
          error:
            "Failed to generate PowerPoint. Ensure dependencies are installed.",
        },
        { status: 500 },
      );
    }

    const trimmed = stdout.trim();
    if (!trimmed || trimmed === "NO_DATA") {
      await fs.promises.rm(tmpDir, { recursive: true, force: true });
      return NextResponse.json({ message: "No plottable data found." });
    }

    let result: { path: string; chart: any };
    try {
      result = JSON.parse(trimmed);
    } catch (err) {
      console.error("ppt-chart-generator parse error:", err);
      await fs.promises.rm(tmpDir, { recursive: true, force: true });
      return NextResponse.json(
        { error: "Failed to parse generator output" },
        { status: 500 },
      );
    }

    const outputPath = result.path;

    try {
      const buffer = await fs.promises.readFile(outputPath);
      const filename = path.basename(outputPath);
      await fs.promises.rm(tmpDir, { recursive: true, force: true });

      if (preview) {
        const b64 = buffer.toString("base64");
        return NextResponse.json({ pptx: b64, chart: result.chart, filename });
      }

      return new NextResponse(buffer, {
        headers: {
          "Content-Type":
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
          "Content-Disposition": `attachment; filename="${filename}"`,
        },
      });
    } catch (err) {
      console.error("ppt-chart-generator file error:", err);
      await fs.promises.rm(tmpDir, { recursive: true, force: true });
      return NextResponse.json(
        { error: "Failed to read generated file" },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error("ppt-chart-generator error:", error);
    return NextResponse.json(
      { error: "Failed to process request" },
      { status: 500 },
    );
  }
}
