import { NextRequest, NextResponse } from 'next/server';

// ClinicalTrials.gov API v2 base URL
const BASE_URL = 'https://clinicaltrials.gov/api/v2/studies';

// Study fields to request from the API
const FIELDS = [
  'protocolSection.identificationModule.nctId',
  'protocolSection.identificationModule.briefTitle',
  'protocolSection.descriptionModule.briefSummary',
  'protocolSection.conditionsModule.conditions',
  'protocolSection.designModule.phases',
  'protocolSection.statusModule.overallStatus',
  'protocolSection.statusModule.lastUpdatePostDateStruct.date',
  'protocolSection.designModule.studyType',
  'protocolSection.designModule.enrollmentInfo.count',
  'protocolSection.designModule.primaryPurpose',
  'protocolSection.designModule.allocation',
  'protocolSection.designModule.interventionModel',
  'protocolSection.designModule.masking',
  'protocolSection.designModule.maskingDescription',
  'protocolSection.designModule.observationalModel',
  'protocolSection.designModule.timePerspective',
  'protocolSection.statusModule.startDateStruct.date',
  'protocolSection.statusModule.completionDateStruct.date',
  'protocolSection.contactsLocationsModule.locations.city',
  'protocolSection.contactsLocationsModule.locations.state',
  'protocolSection.contactsLocationsModule.locations.country',
  'protocolSection.outcomesModule.primaryOutcomes',
  'protocolSection.outcomesModule.secondaryOutcomes',
  'protocolSection.outcomesModule.otherOutcomes',
  'protocolSection.sponsorCollaboratorsModule.leadSponsor.name',
  'protocolSection.sponsorCollaboratorsModule.leadSponsor.class',
  'protocolSection.eligibilityModule.eligibilityCriteria',
  'protocolSection.eligibilityModule.healthyVolunteers',
  'protocolSection.eligibilityModule.sex',
  'protocolSection.eligibilityModule.minimumAge',
  'protocolSection.eligibilityModule.maximumAge',
  'protocolSection.armsInterventionsModule.interventions',
  'protocolSection.referencesModule.references',
  'protocolSection.moreInfoModule.links',
  'hasResults',
];

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get('query');
    const status = searchParams.get('status');
    const phase = searchParams.get('phase');
    const limit = parseInt(searchParams.get('limit') || '20', 10);

    if (!query) {
      return NextResponse.json({ error: 'Query parameter is required' }, { status: 400 });
    }

    const url = new URL(BASE_URL);
    url.searchParams.set('query.term', query);
    url.searchParams.set('fields', FIELDS.join(','));
    url.searchParams.set('pageSize', String(limit));

    if (status) {
      const statusMap: Record<string, string> = {
        Recruiting: 'RECRUITING',
        'Active, not recruiting': 'ACTIVE_NOT_RECRUITING',
        Completed: 'COMPLETED',
      };
      const mapped = statusMap[status] || status;
      url.searchParams.set('filter.overallStatus', mapped);
    }

    if (phase) {
      const phaseMap: Record<string, string> = {
        'Phase 1': 'PHASE1',
        'Phase 2': 'PHASE2',
        'Phase 3': 'PHASE3',
        'Phase 4': 'PHASE4',
      };
      const mapped = phaseMap[phase] || phase;
      url.searchParams.set('filter.advanced', `AREA[Phase]${mapped}`);
    }

    const response = await fetch(url.toString());
    if (!response.ok) {
      return NextResponse.json({ error: 'ClinicalTrials.gov request failed' }, { status: 500 });
    }

    const data = await response.json();
    const trials = data?.studies || [];

    return NextResponse.json({ success: true, trials });
  } catch (error) {
    console.error('ClinicalTrials search error:', error);
    return NextResponse.json({ error: 'Failed to search clinical trials' }, { status: 500 });
  }
}

