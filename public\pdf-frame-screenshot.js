// Listen for messages from the parent window
window.addEventListener('message', async (event) => {
  if (event.data?.type === 'TAKE_SCREENSHOT') {
    try {
      // Use html2canvas to capture the PDF content
      const pdfContainer = document.querySelector('.react-pdf__Document');
      if (!pdfContainer) {
        throw new Error('PDF container not found');
      }
      
      const canvas = await html2canvas(pdfContainer, {
        allowTaint: true,
        useCORS: true,
        scale: 2,
        logging: false,
        backgroundColor: '#ffffff'
      });
      
      // Convert canvas to base64 image
      const imageData = canvas.toDataURL('image/png');
      
      // Send the image data back to the parent window
      window.parent.postMessage({
        type: 'SCREENSHOT_RESULT',
        imageBlob: imageData
      }, '*');
    } catch (error) {
      console.error('Error taking screenshot:', error);
      window.parent.postMessage({
        type: 'SCREENSHOT_RESULT',
        error: error.message || 'Failed to capture screenshot'
      }, '*');
    }
  }
});

console.log('PDF frame screenshot handler initialized');