from dotenv import load_dotenv
import os
import requests

# Load environment variables from .env file
load_dotenv()

# Get API token from environment variables
API_TOKEN = os.getenv('BRIGHT_API_KEY')

# Check if API token is available
if not API_TOKEN:
    raise ValueError("API_TOKEN not found. Please set the 'BRIGHT_API_KEY' environment variable in your .env file.")

print('If you get "ImportError: No module named \'requests\'", install requests: sudo pip install requests')

def filter_dataset():
    url = "https://api.brightdata.com/datasets/filter"
    headers = {
        "Authorization": f"Bearer {API_TOKEN}",  # Use the actual token value, not the string "API_TOKEN"
        "Content-Type": "application/json"
    }
    payload = {
        "dataset_id": "gd_l1viktl72bvl7bjuj0",
        "filter": {"operator":"or","filters":[{"name":"position","value":"health economics","operator":"includes"},{"name":"position","value":"heor","operator":"includes"}]}
    }
    
    print(f"Using API token: {API_TOKEN[:5]}...{API_TOKEN[-5:] if len(API_TOKEN) > 10 else API_TOKEN}")
    
    response = requests.post(url, headers=headers, json=payload)
    if response.ok:
        print("Request succeeded:", response.json())
    else:
        print(f"Request failed: {response.text}")
        print(f"Status code: {response.status_code}")
        print(f"Headers sent: {headers}")

if __name__ == "__main__":
    filter_dataset()
