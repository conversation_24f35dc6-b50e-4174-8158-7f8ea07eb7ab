"use client";

import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Filter } from "lucide-react";
import { SubpageHeader } from "@/components/SubpageHeader";
import { RssFeed } from "@/components/RssFeed";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import Image from 'next/image';
import { useTheme } from "@/contexts/ThemeContext";

// Define feed sources with metadata
const feedSources = [
  {
    id: "ema-medicines",
    url: "https://www.ema.europa.eu/en/human-medicine-new.xml",
    title: "EMA Medicine Updates",
    description: "New medicine information from the European Medicines Agency",
    agency: "EMA",
    category: "medicines",
    country: "EU",
    flagCode: "eu"
  },
  {
    id: "ema-new-human",
    url: "https://www.ema.europa.eu/en/new-human-medicine-new.xml",
    title: "EMA New Human Medicines",
    description: "New human medicine approvals from the European Medicines Agency",
    agency: "EMA",
    category: "medicines",
    country: "EU",
    flagCode: "eu"
  },
  {
    id: "ema-news",
    url: "https://www.ema.europa.eu/en/news.xml",
    title: "EMA News",
    description: "Latest news from the European Medicines Agency",
    agency: "EMA",
    category: "news",
    country: "EU",
    flagCode: "eu"
  },
  {
    "id": "eu-hta-updates",
    "url": "https://health.ec.europa.eu/node/13009/rss_en",
    "title": "Public Health | Latest updates",
    "description": "Public Health | Health technology assessment - Latest updates",
    "agency": "European Commission - Directorate-General for Health and Food Safety",
    "category": "health technology assessment",
    "country": "EU",
    "flagCode": "eu"
  },
  {
    id: "fda-press",
    url: "https://www.fda.gov/about-fda/contact-fda/stay-informed/rss-feeds/press-releases/rss.xml",
    title: "FDA Press Releases",
    description: "Press releases from the US Food and Drug Administration",
    agency: "FDA",
    category: "news",
    country: "USA",
    flagCode: "us"
  },
  {
    id: "fda-drugs",
    url: "https://www.fda.gov/about-fda/contact-fda/stay-informed/rss-feeds/drugs/rss.xml",
    title: "FDA Drug Updates",
    description: "Drug-related updates from the US Food and Drug Administration",
    agency: "FDA",
    category: "medicines",
    country: "USA",
    flagCode: "us"
  },
  {
    id: "fda-biologics",
    url: "https://www.fda.gov/about-fda/contact-fda/stay-informed/rss-feeds/biologics/rss.xml",
    title: "FDA Biologics Updates",
    description: "Biologics-related updates from the US Food and Drug Administration",
    agency: "FDA",
    category: "medicines",
    country: "USA",
    flagCode: "us"
  },
  {
    id: "nice-news",
    url: "https://us8.campaign-archive.com/feed?u=7864f766b10b8edd18f19aa56&id=5c592fb207",
    title: "NICE News",
    description: "Updates from the National Institute for Health and Care Excellence",
    agency: "NICE",
    category: "news",
    country: "UK",
    flagCode: "gb"
  },
  {
    id: "smc-updates",
    url: "/api/smc-feed", // We'll create this endpoint
    title: "SMC Updates",
    description: "Latest updates and decisions from the Scottish Medicines Consortium",
    agency: "SMC",
    category: "decisions",
    country: "UK",
    flagCode: "gb"
  },
  {
    id: "gba-updates",
    url: "https://www.g-ba.de/letzte-aenderungen/?rss=1",
    title: "G-BA Updates",
    description: "Latest changes from the German Federal Joint Committee",
    agency: "G-BA",
    category: "decisions",
    country: "Germany",
    flagCode: "de"
  },
  {
    id: "iqwig-press",
    url: "/api/iqwig-feed",
    title: "IQWiG Press Releases",
    description: "Latest press releases from the Institute for Quality and Efficiency in Health Care (IQWiG)",
    agency: "IQWiG",
    category: "news",
    country: "Germany",
    flagCode: "de"
  },
  {
    id: "has-updates",
    url: "https://www.has-sante.fr/feed/Rss2.jsp?id=p_3375329",
    title: "HAS Updates",
    description: "Updates from the French National Authority for Health",
    agency: "HAS",
    category: "decisions",
    country: "France",
    flagCode: "fr"
  },
  {
    id: "dkma-updates",
    url: "https://laegemiddelstyrelsen.dk/da/Feeds/rss-feed",
    title: "Danish Medicines Agency Updates",
    description: "Latest updates from the Danish Medicines Agency (Lægemiddelstyrelsen)",
    agency: "DKMA",
    category: "news",
    country: "Denmark",
    flagCode: "dk"
  },
  {
    id: "cda-amc-news",
    url: "/api/cda-feed",
    title: "Canada's Drug Agency Updates",
    description: "Latest news and updates from Canada's Drug Agency (CDA-AMC)",
    agency: "CDA-AMC",
    category: "news",
    country: "Canada",
    flagCode: "ca"
  },
  {
    id: "aifa-news",
    url: "/api/aifa-feed",
    title: "AIFA News",
    description: "Latest news and updates from the Italian Medicines Agency (AIFA)",
    agency: "AIFA",
    category: "news",
    country: "Italy",
    flagCode: "it"
  },
  {
    id: "icer-updates",
    url: "/api/icer-feed",
    title: "ICER Updates",
    description: "Latest press releases from the Institute for Clinical and Economic Review",
    agency: "ICER",
    category: "news",
    country: "USA",
    flagCode: "us"
  },
  {
    id: "cms-newsroom",
    url: "/api/cms-feed",
    title: "CMS Newsroom",
    description: "Latest news and updates from the Centers for Medicare & Medicaid Services (CMS)",
    agency: "CMS",
    category: "news",
    country: "USA",
    flagCode: "us"
  }
];

export default function RegulatoryUpdatesPage() {
  const [selectedAgency, setSelectedAgency] = useState<string>("all");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedCountry, setSelectedCountry] = useState<string>("all");
  const { theme } = useTheme();

  // Get unique values for filters
  const agencies = Array.from(new Set(feedSources.map(feed => feed.agency)));
  const categories = Array.from(new Set(feedSources.map(feed => feed.category)));
  const countries = Array.from(new Set(feedSources.map(feed => feed.country)));

  // Filter feeds based on selections
  const getFilteredFeeds = () => {
    return feedSources.filter(feed => 
      (selectedAgency === "all" || feed.agency === selectedAgency) &&
      (selectedCategory === "all" || feed.category === selectedCategory) &&
      (selectedCountry === "all" || feed.country === selectedCountry)
    );
  };

  const filteredFeeds = getFilteredFeeds();

  return (
    <div className="container mx-auto py-8 px-4">
      <SubpageHeader current="reg-hta-updates" />
      
      <h1 className="text-3xl font-bold mb-6">Regulatory & HTA Updates</h1>
      
      <div className="prose max-w-none mb-8 dark:text-gray-200">
        <p>
          Stay informed about the latest updates from regulatory agencies and health technology assessment (HTA) 
          bodies worldwide. This page aggregates official and unofficial RSS feeds from EMA, FDA, NICE, G-BA, HAS, and more.
        </p>
      </div>
      
      <Tabs defaultValue="all-feeds" className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-6">
          <TabsTrigger value="all-feeds">All Feeds</TabsTrigger>
          <TabsTrigger value="by-agency">By Agency</TabsTrigger>
        </TabsList>
        
        <TabsContent value="all-feeds">
          <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg mb-6">
            <h3 className="text-lg font-medium mb-4 flex items-center dark:text-white">
              <Filter className="mr-2 h-4 w-4" />
              Filter Updates
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="text-sm font-medium mb-1 block dark:text-gray-300">Agency</label>
                <Select value={selectedAgency} onValueChange={setSelectedAgency}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select Agency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Agencies</SelectItem>
                    {agencies.map(agency => (
                      <SelectItem key={agency} value={agency}>{agency}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="text-sm font-medium mb-1 block dark:text-gray-300">Category</label>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map(category => (
                      <SelectItem key={category} value={category}>
                        {category.charAt(0).toUpperCase() + category.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="text-sm font-medium mb-1 block dark:text-gray-300">Country/Region</label>
                <Select value={selectedCountry} onValueChange={setSelectedCountry}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select Country" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Countries</SelectItem>
                    {countries.map(country => (
                      <SelectItem key={country} value={country}>{country}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          
          {filteredFeeds.length === 0 ? (
            <div className="text-center py-12 text-muted-foreground">
              No feeds match your current filter criteria. Please adjust your filters.
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-6">
              {filteredFeeds.map(feed => (
                <div key={feed.id} className="border rounded-lg p-4 bg-white dark:bg-gray-800 dark:border-gray-700 shadow-sm">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 rounded-full overflow-hidden flex items-center justify-center border border-gray-200 dark:border-gray-600 mr-3 flex-shrink-0">
                      <Image
                        src={`/flags/${feed.flagCode}.svg`}
                        alt={`${feed.country} flag`}
                        width={40}
                        height={40}
                        className="object-cover w-full h-full"
                      />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold dark:text-white">{feed.title}</h3>
                      <p className="text-sm text-muted-foreground dark:text-gray-300">{feed.description}</p>
                    </div>
                  </div>
                  <div className="mt-2 mb-4 flex flex-wrap gap-2">
                    <Badge variant="outline" className="dark:border-gray-600 dark:text-gray-200">{feed.agency}</Badge>
                    <Badge variant="secondary" className="dark:bg-gray-700 dark:text-gray-200">
                      {feed.category.charAt(0).toUpperCase() + feed.category.slice(1)}
                    </Badge>
                    <Badge variant="outline" className="dark:border-gray-600 dark:text-gray-200">{feed.country}</Badge>
                  </div>
                  <RssFeed 
                    url={feed.url} 
                    compact={true}
                    maxItems={3}
                  />
                </div>
              ))}
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="by-agency">
          <Accordion type="single" collapsible className="w-full">
            {agencies.map(agency => (
              <AccordionItem key={agency} value={agency}>
                <AccordionTrigger className="text-xl font-bold dark:text-white">
                  {agency} Updates
                </AccordionTrigger>
                <AccordionContent>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-4">
                    {feedSources
                      .filter(feed => feed.agency === agency)
                      .map(feed => (
                        <div key={feed.id} className="border rounded-lg p-4 bg-white dark:bg-gray-800 dark:border-gray-700 shadow-sm">
                          <div className="flex items-center mb-4">
                            <div className="w-10 h-10 rounded-full overflow-hidden flex items-center justify-center border border-gray-200 dark:border-gray-600 mr-3 flex-shrink-0">
                              <Image
                                src={`/flags/${feed.flagCode}.svg`}
                                alt={`${feed.country} flag`}
                                width={40}
                                height={40}
                                className="object-cover w-full h-full"
                              />
                            </div>
                            <div>
                              <h3 className="text-xl font-bold dark:text-white">{feed.title}</h3>
                              <p className="text-sm text-muted-foreground dark:text-gray-300">{feed.description}</p>
                            </div>
                          </div>
                          <RssFeed 
                            url={feed.url} 
                            compact={true}
                            maxItems={5}
                          />
                        </div>
                      ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </TabsContent>
      </Tabs>
    </div>
  );
}
