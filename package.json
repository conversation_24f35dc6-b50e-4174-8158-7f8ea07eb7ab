{"name": "nextn", "version": "0.1.0", "private": true, "engines": {"node": "18.18.0"}, "scripts": {"dev": "next dev --turbopack -p 9002", "genkit:dev": "genkit start -- tsx src/ai/dev.ts", "genkit:watch": "genkit start -- tsx --watch src/ai/dev.ts", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit"}, "dependencies": {"@aws-sdk/client-s3": "^3.828.0", "@aws-sdk/s3-request-presigner": "^3.828.0", "@genkit-ai/googleai": "^1.0.4", "@genkit-ai/next": "^1.0.4", "@hookform/resolvers": "^4.1.3", "@pdf-viewer/react": "^1.7.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack-query-firebase/react": "^1.0.5", "@tanstack/react-query": "^5.66.0", "@upstash/redis": "^1.34.9", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "cmdk": "^0.2.1", "date-fns": "^3.6.0", "fast-xml-parser": "^4.3.2", "firebase": "^11.3.0", "genkit": "^1.0.4", "html2canvas": "^1.4.1", "lucide-react": "^0.475.0", "mongodb": "^6.16.0", "next": "^15.2.3", "nodemailer": "^7.0.3", "openai": "^4.98.0", "patch-package": "^8.0.0", "pdf-lib": "^1.17.1", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-diff-viewer-continued": "^3.4.0", "react-dom": "^18.2.0", "react-hook-form": "^7.54.2", "recharts": "^2.15.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^18", "@types/react-dom": "^18", "genkit-cli": "^1.0.4", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}