#!/usr/bin/env python3
"""
ICER Press Release Scraper - Enhanced Version
Scrapes https://icer.org/news-insights/press-releases/
and formats output similar to SMC scraper
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import xml.etree.ElementTree as ET
from datetime import datetime
from urllib.parse import urljoin
import re
import sys

class ICERScraper:
    def __init__(self):
        self.base_url = "https://icer.org"
        self.target_url = "https://icer.org/news-insights/press-releases/"
        
        # Selectors
        self.press_release_container_selector = "article.content-card"
        self.link_selector_inside_container = "div.link-block > a"
        self.title_selector_inside_link = "h3"
        self.date_selector_inside_container = "span.publish-date"
        self.summary_selector_inside_container = "p.excerpt"
        self.load_more_button_css_selector = "a.button.load-more"
        
        self.max_clicks_load_more = 5
        self.driver = None

    def setup_driver(self):
        """Setup Chrome WebDriver with appropriate options."""
        options = webdriver.ChromeOptions()
        options.add_argument("--headless")  # Uncomment for headless mode
        options.add_argument("--disable-gpu")
        options.add_argument("--window-size=1920,1080")
        options.add_argument('user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 WebScraperBot/1.0')
        
        try:
            self.driver = webdriver.Chrome(
                service=ChromeService(ChromeDriverManager().install()), 
                options=options
            )
            return True
        except Exception as e:
            print(f"[ERROR] WebDriver setup failed: {e}")
            return False

    def handle_cookie_consent(self):
        """Handle cookie consent banner if present."""
        try:
            print("[INFO] Checking for cookie consent banner...")
            cookie_accept_button = WebDriverWait(self.driver, 7).until(
                EC.element_to_be_clickable((By.ID, "wt-cli-accept-all-btn"))
            )
            print("[INFO] Cookie consent banner found. Clicking 'Accept All'.")
            cookie_accept_button.click()
            print("[INFO] Cookie consent banner accepted.")
            time.sleep(3)
        except Exception:
            print("[INFO] Cookie consent banner not found or already accepted.")

    def handle_dynamic_content(self):
        """Load more content by clicking 'Load More' button."""
        print("[INFO] Attempting to load dynamic content by clicking 'Load More'...")
        clicks = 0
        
        while clicks < self.max_clicks_load_more:
            try:
                print(f"[INFO] Attempt {clicks+1}: Waiting for 'Load More' button...")
                load_more_button = WebDriverWait(self.driver, 15).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, self.load_more_button_css_selector))
                )
                
                # Scroll button into view
                self.driver.execute_script(
                    "arguments[0].scrollIntoView({block: 'center', inline: 'center'});", 
                    load_more_button
                )
                time.sleep(1)
                
                # Wait for clickability
                WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, self.load_more_button_css_selector))
                )
                
                load_more_button.click()
                print(f"[INFO] Clicked 'Load More' button ({clicks+1}/{self.max_clicks_load_more})")
                time.sleep(5)
                clicks += 1
                
            except Exception as e:
                print(f"[WARN] Could not find or click 'Load More' button after {clicks} attempts: {e}")
                break
                
        if clicks == self.max_clicks_load_more:
            print(f"[INFO] Reached maximum clicks ({self.max_clicks_load_more}) for 'Load More' button.")
        print("[INFO] Finished loading dynamic content.")

    def parse_date(self, date_str):
        """Try multiple date formats, fallback to now."""
        if not date_str or date_str == "N/A":
            return datetime.now()
            
        # Clean the date string
        date_str = re.sub(r'\s+', ' ', date_str).strip()
        
        # Try formats in order of likelihood for ICER site
        # ICER uses MM/DD/YYYY format based on the provided example
        for fmt in ("%m/%d/%Y", "%B %d, %Y", "%b %d, %Y", "%Y-%m-%d", "%d/%m/%Y", "%d %B %Y", "%d %b %Y"):
            try:
                parsed_date = datetime.strptime(date_str, fmt)
                print(f"[DEBUG] Successfully parsed date '{date_str}' using format '{fmt}'")
                return parsed_date
            except ValueError:
                continue
        
        print(f"[WARN] Could not parse date '{date_str}' with any known format")
        return datetime.now()

    def clean_text(self, text):
        """Normalize whitespace and strip UI cruft."""
        if not text:
            return ""
        txt = re.sub(r'\s+', ' ', text).strip()
        # Remove common navigation terms
        for pat in [r'\b(Show All|Next|Last|Register|About us|Contact us|Home)\b',
                    r'skip to (navigation|content)',
                    r'©.*']:
            txt = re.sub(pat, "", txt, flags=re.IGNORECASE)
        return txt.strip()

    def extract_press_releases(self):
        """Extract press release data from the page."""
        print("[INFO] Extracting data from page source...")
        page_source = self.driver.page_source
        soup = BeautifulSoup(page_source, "html.parser")
        press_releases = []

        articles = soup.select(self.press_release_container_selector)
        print(f"[INFO] Found {len(articles)} press release containers")

        if not articles:
            print("[WARN] No articles found. Check selectors and dynamic content loading.")
            return press_releases

        seen = set()
        
        for item_count, item_container in enumerate(articles):
            try:
                # Extract link and title
                link_element = item_container.select_one(self.link_selector_inside_container)
                link_href_raw = ""
                title = ""

                if link_element and link_element.has_attr('href'):
                    link_href_raw = link_element['href']
                    title_element = link_element.select_one(self.title_selector_inside_link)
                    if title_element:
                        title = self.clean_text(title_element.get_text())
                    else:
                        title = self.clean_text(link_element.get_text())
                
                # Build full URL
                link = ""
                if link_href_raw:
                    if link_href_raw.startswith('http'):
                        link = link_href_raw
                    elif link_href_raw.startswith('/'):
                        link = self.base_url.rstrip('/') + link_href_raw
                    else: 
                        link = self.base_url.rstrip('/') + '/' + link_href_raw

                # Extract date
                date_element = item_container.select_one(self.date_selector_inside_container)
                date_str = self.clean_text(date_element.get_text()) if date_element else ""

                # Extract summary/description
                summary_element = item_container.select_one(self.summary_selector_inside_container)
                description = self.clean_text(summary_element.get_text()) if summary_element else title

                # Avoid duplicates
                key = (title, link)
                if title and key not in seen:
                    seen.add(key)
                    press_releases.append({
                        'date': date_str,
                        'title': title,
                        'link': link,
                        'description': description
                    })
                    
            except Exception as e:
                print(f"[WARN] Error extracting details for item {item_count+1}: {e}")
                continue

        # Sort by date (newest first)
        press_releases.sort(key=lambda pr: self.parse_date(pr['date']), reverse=True)
        return press_releases

    def create_rss_xml(self, press_releases):
        """Build a simple RSS feed from press releases."""
        rss = ET.Element('rss', version="2.0", **{'xmlns:atom': "http://www.w3.org/2005/Atom"})
        ch = ET.SubElement(rss, 'channel')
        
        # Channel metadata
        for tag, txt in [
            ('title', "ICER | Press Releases"),
            ('link', self.target_url),
            ('description', "Latest press releases from the Institute for Clinical and Economic Review"),
            ('language', "en"),
            ('copyright', f"© Institute for Clinical and Economic Review {datetime.now().year}")
        ]:
            e = ET.SubElement(ch, tag)
            e.text = txt

        # Items
        for pr in press_releases:
            item = ET.SubElement(ch, 'item')
            ET.SubElement(item, 'title').text = pr['title']
            if pr['link']:
                ET.SubElement(item, 'link').text = pr['link']
                ET.SubElement(item, 'guid').text = pr['link']
            ET.SubElement(item, 'description').text = f"<p>{pr['description']}</p>"
            
            # Format publication date - set to noon UTC to prevent date shift in different timezones
            pub_date = self.parse_date(pr['date']).strftime("%a, %d %b %Y 12:00:00 +0000")
            ET.SubElement(item, 'pubDate').text = pub_date

        return rss

    def format_as_text(self, press_releases):
        """Format press releases as plain text."""
        lines = [
            "INSTITUTE FOR CLINICAL AND ECONOMIC REVIEW - PRESS RELEASES",
            "=" * 60,
            f"Scraped from: {self.target_url}",
            f"On: {datetime.now():%Y-%m-%d %H:%M:%S}",
            ""
        ]
        
        for i, pr in enumerate(press_releases, start=1):
            lines += [
                f"{i}. {pr['date']}",
                f"   Title: {pr['title']}",
                f"   Link: {pr['link'] or '[none]'}",
                f"   Description: {pr['description'][:100]}..." if len(pr['description']) > 100 else f"   Description: {pr['description']}",
                ""
            ]
        return "\n".join(lines)

    def scrape(self, output_format='xml'):
        """Main scraping method."""
        print(f"[INFO] Starting scraper for {self.target_url}")
        
        if not self.setup_driver():
            return None

        try:
            # Navigate to page
            self.driver.get(self.target_url)
            print(f"[INFO] Successfully navigated to {self.target_url}")
            
            # Wait for initial load
            WebDriverWait(self.driver, 20).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            print("[INFO] Initial page body loaded.")
            time.sleep(2)

            # Handle cookie consent
            self.handle_cookie_consent()
            
            # Load dynamic content
            self.handle_dynamic_content()
            
            # Extract data
            press_releases = self.extract_press_releases()
            
            if not press_releases:
                print("[WARN] No press releases extracted")
                return None

            print(f"[INFO] Successfully extracted {len(press_releases)} press releases")
            
            # Format output
            if output_format.lower() == 'xml':
                rss = self.create_rss_xml(press_releases)
                from xml.dom import minidom
                raw = ET.tostring(rss, 'utf-8')
                pretty = minidom.parseString(raw).toprettyxml(indent="  ")
                # Strip XML declaration if you like:
                return "\n".join(pretty.splitlines()[1:])
            else:
                return self.format_as_text(press_releases)

        except Exception as e:
            print(f"[ERROR] An error occurred during scraping: {e}")
            return None
            
        finally:
            if self.driver:
                print("[INFO] Closing WebDriver.")
                self.driver.quit()

def main():
    scraper = ICERScraper()
    fmt = 'xml'
    if len(sys.argv) > 1 and sys.argv[1].lower() in ('txt', 'text'):
        fmt = 'text'

    output = scraper.scrape(fmt)
    if output:
        print(output)
        
        # Save to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"web_search_and_scraping/output/icer_press_releases.{fmt}"
        
        with open(filename, 'w', encoding='utf-8') as f:
            if fmt == 'xml':
                f.write('<?xml version="1.0" encoding="utf-8"?>\n')
            f.write(output)
        print(f"[INFO] Saved to {filename}")
    else:
        print("[ERROR] Scrape returned no data")

if __name__ == "__main__":
    main()
