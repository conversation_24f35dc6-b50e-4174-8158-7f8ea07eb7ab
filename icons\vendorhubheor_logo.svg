<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg viewBox="0 0 600 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#000000;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#000000;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF9900;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Text with gradient - using extra-wide-body font with ultra-thin letters and rounded corners -->
  <text x="50%" y="105" font-family="Arial Rounded MT Bold, Verdana, Geneva, sans-serif" font-size="42" font-weight="100" font-stretch="ultra-expanded" letter-spacing="2" fill="url(#textGradient)" text-anchor="middle">vendor/hub: heor</text>
</svg>