"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { SUBPAGES } from "@/lib/subpages";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { ArrowLeftRight, X } from "lucide-react";
import { Suspense } from "react";

// Content component that uses useSearchParams
function SideBySideContent() {
  const params = useSearchParams();
  const router = useRouter();
  const left = params.get("left") || "";
  const right = params.get("right") || "";

  if (!left && !right) {
    return <div className="p-4">Specify ?left=page&right=page</div>;
  }

  const handleRightChange = (val: string) => {
    router.push(`/side-by-side?left=${left}&right=${val}`);
  };

  const handleSwap = () => {
    router.push(`/side-by-side?left=${right}&right=${left}`);
  };

  const handleClose = () => {
    router.push(`/${left}`);
  };

  return (
    <div className="flex flex-col h-screen">
      <div className="p-2 border-b flex items-center justify-between gap-2">
        <div className="flex items-center gap-2">
          <Select value={right} onValueChange={handleRightChange}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Select page" />
            </SelectTrigger>
            <SelectContent>
              {SUBPAGES.filter((p) => p.value !== left).map((p) => (
                <SelectItem key={p.value} value={p.value}>
                  {p.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {right && (
            <Button variant="secondary" size="sm" onClick={handleClose}>
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
        {left && right && (
          <Button variant="outline" size="sm" onClick={handleSwap}>
            <ArrowLeftRight className="h-4 w-4 mr-1" /> Swap
          </Button>
        )}
      </div>
      <div className="flex flex-1">
        {left && (
          <iframe
            src={`/${left}?embedded=1`}
            className="flex-1"
          />
        )}
        {left && right && (
          <div className="w-1 bg-border dark:bg-gray-700" />
        )}
        {right && (
          <iframe
            src={`/${right}?embedded=1`}
            className="flex-1"
          />
        )}
      </div>
    </div>
  );
}

// Main component with Suspense boundary
export default function SideBySidePage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SideBySideContent />
    </Suspense>
  );
}
