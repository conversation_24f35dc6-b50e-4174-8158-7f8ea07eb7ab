"use client";

import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Loader2, X } from "lucide-react";
import { useClipboard } from "@/contexts/ClipboardContext";
import { PDFDocument } from "pdf-lib";

interface PagePdfViewerProps {
  url: string;
  title: string;
  articleUrl?: string;
  pageInput: number;
  onPageChange: (val: number) => void;
  onSave: () => void;
  onClose: () => void;
}

const PagePdfViewer = React.memo(
  ({ url, title, articleUrl, pageInput, onPageChange, onSave, onClose }: PagePdfViewerProps) => {
    const { addNote } = useClipboard();
    const [viewerKey, setViewerKey] = useState(0);
    const [saved, setSaved] = useState(false);
    const [copied, setCopied] = useState(false);
    const [copying, setCopying] = useState(false);
    const [pageCount, setPageCount] = useState<number | null>(null);

    useEffect(() => {
      const fetchPages = async () => {
        if (!url) {
          setPageCount(null);
          return;
        }
        try {
          const proxied = url.startsWith("/api/")
            ? url
            : `/api/drugs-docs?download=1&url=${encodeURIComponent(url)}`;
          const res = await fetch(proxied);
          if (!res.ok) throw new Error("fetch failed");
          const bytes = await res.arrayBuffer();
          const pdf = await PDFDocument.load(bytes);
          const count = pdf.getPageCount();
          setPageCount(count);
          if (pageInput > count) onPageChange(count);
        } catch (e) {
          console.error("Failed to load PDF", e);
          setPageCount(null);
        }
      };
      fetchPages();
    }, [url]);

    useEffect(() => {
      if (pageCount && pageInput > pageCount) {
        onPageChange(pageCount);
      }
    }, [pageCount]);

    useEffect(() => {
      setViewerKey((k) => k + 1);
      setSaved(false);
      setCopied(false);
    }, [url, pageInput]);

    const handleSave = () => {
      onSave();
      setSaved(true);
      setTimeout(() => setSaved(false), 2000);
    };

    const handleCopy = async () => {
      if (!url) return;
      setCopying(true);
      try {
        const proxied = url.startsWith("/api/")
          ? url
          : `/api/drugs-docs?download=1&url=${encodeURIComponent(url)}`;
        const res = await fetch(proxied);
        if (!res.ok) throw new Error("fetch failed");
        const bytes = await res.arrayBuffer();
        const srcDoc = await PDFDocument.load(bytes);
        const pageIndex = Math.min(Math.max(pageInput - 1, 0), srcDoc.getPageCount() - 1);
        const newDoc = await PDFDocument.create();
        const [copiedPage] = await newDoc.copyPages(srcDoc, [pageIndex]);
        newDoc.addPage(copiedPage);
        const pageBytes = await newDoc.save();
        const blob = new Blob([pageBytes], { type: "application/pdf" });
        const form = new FormData();
        form.append("file", blob, "page.pdf");
        const conv = await fetch("/api/pdf-to-markdown", {
          method: "POST",
          body: form,
        });
        let text = "";
        if (conv.ok) {
          const data = await conv.json();
          text = data.text || "";
        } else {
          console.error("Conversion failed", conv.status);
        }
        addNote(text, articleUrl || url, undefined, title);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (err) {
        console.error("Copy page error", err);
      } finally {
        setCopying(false);
      }
    };

    return (
      <div className="flex flex-col h-full">
        <div className="relative border rounded-md overflow-visible flex-1">
          <Button
            size="icon"
            variant="ghost"
            className="absolute -top-3 -right-3 z-10 h-6 w-6 p-0 bg-white/70"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
          {url ? (
            <iframe
              key={viewerKey}
              src={`/pdf-frame?url=${encodeURIComponent(url)}&page=${pageInput}&title=${encodeURIComponent(title)}${articleUrl ? `&articleUrl=${encodeURIComponent(articleUrl)}` : ''}`}
              className="w-full h-full"
              style={{ display: "block" }}
            />
          ) : (
            <div className="h-full flex items-center justify-center text-gray-500">
              <p>No PDF selected</p>
            </div>
          )}
        </div>
        <div className="mt-2 flex items-center gap-2">
          <Input
            type="number"
            min={1}
            max={pageCount ?? undefined}
            value={pageInput}
            onChange={(e) => {
              const val = parseInt(e.target.value) || 1;
              const clamped = pageCount ? Math.min(val, pageCount) : val;
              onPageChange(clamped);
            }}
            className="w-20"
          />
          <Button
            size="sm"
            onClick={handleSave}
            disabled={!url}
            className={saved ? "bg-green-500 text-white hover:bg-green-600" : ""}
          >
            {saved ? "Saved" : "Save Page"}
          </Button>
          <Button
            size="sm"
            onClick={handleCopy}
            disabled={!url || copying}
            className={copied ? "bg-green-500 text-white hover:bg-green-600" : ""}
          >
            {copying ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : copied ? (
              "Saved"
            ) : (
              "Save Page to Clipboard"
            )}
          </Button>
        </div>
      </div>
    );
  }
);

PagePdfViewer.displayName = "PagePdfViewer";

export default PagePdfViewer;

