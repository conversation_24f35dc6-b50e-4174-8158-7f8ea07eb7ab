
"use client";

import React, { useState, useMemo, useEffect } from "react";
import Link from "next/link";
import { ArrowLeft, Search, Bookmark, BookmarkCheck, GitCompare, Settings, Download, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { ThemeToggle } from "@/components/ThemeToggle";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { useLocalStorage } from "@/hooks/use-local-storage";
import { ResponsiveContainer, BarChart, Bar, CartesianGrid, XAxis, YAxis, Tooltip, Legend } from "recharts";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

interface ClinicalTrial {
  protocolSection?: {
    identificationModule?: {
      nctId?: string;
      briefTitle?: string;
    };
    descriptionModule?: {
      briefSummary?: string;
    };
    conditionsModule?: {
      conditions?: string[];
    };
    statusModule?: {
      overallStatus?: string;
      startDateStruct?: { date?: string };
      completionDateStruct?: { date?: string };
      lastUpdatePostDateStruct?: { date?: string };
    };
    sponsorCollaboratorsModule?: {
      leadSponsor?: {
        name?: string;
        class?: string;
      };
      collaborators?: Array<{
        name?: string;
        class?: string;
      }>;
    };
    eligibilityModule?: {
      eligibilityCriteria?: string;
      healthyVolunteers?: string;
      sex?: string;
      minimumAge?: string;
      maximumAge?: string;
      stdAges?: string[];
    };
    designModule?: {
      phases?: string[];
      studyType?: string;
      enrollmentInfo?: { count?: number };
      interventionModel?: string;
      allocation?: string;
      masking?: string;
      maskingDescription?: string;
      primaryPurpose?: string;
      observationalModel?: string;
      timePerspective?: string;
    };
    armsInterventionsModule?: {
      interventions?: Array<{
        type?: string;
        name?: string;
        description?: string;
      }>;
    };
    outcomesModule?: {
      primaryOutcomes?: Array<{
        measure?: string;
        description?: string;
        timeFrame?: string;
      }>;
      secondaryOutcomes?: Array<{
        measure?: string;
        description?: string;
        timeFrame?: string;
      }>;
      otherOutcomes?: Array<{
        measure?: string;
        description?: string;
        timeFrame?: string;
      }>;
    };
    contactsLocationsModule?: {
      locations?: Array<{
        city?: string;
        state?: string;
        country?: string;
      }>;
    };
    referencesModule?: {
      references?: Array<{
        citation?: string;
        url?: string;
      }>;
    };
    moreInfoModule?: {
      links?: Array<{
        label?: string;
        url?: string;
      }>;
    };
  };
  hasResults?: boolean;
}

// Comparison domain configuration
interface ComparisonDomain {
  id: string;
  label: string;
  description: string;
  defaultEnabled: boolean;
  getValue: (trial: ClinicalTrial) => any;
  formatValue: (value: any) => string;
}

const COMPARISON_DOMAINS: ComparisonDomain[] = [
  {
    id: 'studyStatus',
    label: 'Study Status',
    description: 'Current status of the clinical trial',
    defaultEnabled: true,
    getValue: (trial) => trial.protocolSection?.statusModule?.overallStatus || null,
    formatValue: (value) => value || 'Not specified'
  },
  {
    id: 'studyPhase',
    label: 'Study Phase',
    description: 'Phase of the clinical trial',
    defaultEnabled: true,
    getValue: (trial) => trial.protocolSection?.designModule?.phases?.[0] || null,
    formatValue: (value) => value || 'Not specified'
  },
  {
    id: 'studyDesign',
    label: 'Study Design',
    description: 'Comprehensive design details including type, purpose, allocation, model, and masking',
    defaultEnabled: true,
    getValue: (trial) => {
      const design = trial.protocolSection?.designModule;
      return {
        studyType: design?.studyType,
        primaryPurpose: design?.primaryPurpose,
        interventionModel: design?.interventionModel,
        allocation: design?.allocation,
        masking: design?.masking,
        maskingDescription: design?.maskingDescription,
        observationalModel: design?.observationalModel,
        timePerspective: design?.timePerspective
      };
    },
    formatValue: (value) => {
      if (!value || typeof value !== 'object') return 'Not specified';
      const parts = [];

      // Study Type - always show if available
      if (value.studyType) {
        parts.push(`Study Type: ${value.studyType}`);
      }

      // Primary Purpose
      if (value.primaryPurpose) {
        parts.push(`Primary Purpose: ${value.primaryPurpose}`);
      }

      // Allocation
      if (value.allocation) {
        parts.push(`Allocation: ${value.allocation}`);
      }

      // Intervention Model
      if (value.interventionModel) {
        parts.push(`Intervention Model: ${value.interventionModel}`);
      }

      // Observational Model (for observational studies)
      if (value.observationalModel) {
        parts.push(`Observational Model: ${value.observationalModel}`);
      }

      // Time Perspective (for observational studies)
      if (value.timePerspective) {
        parts.push(`Time Perspective: ${value.timePerspective}`);
      }

      // Masking
      if (value.masking) {
        let maskingText = `Masking: ${value.masking}`;
        if (value.maskingDescription) {
          maskingText += ` (${value.maskingDescription})`;
        }
        parts.push(maskingText);
      }

      return parts.length > 0 ? parts.join('\n') : 'Study Type: ' + (value.studyType || 'Not specified');
    }
  },
  {
    id: 'totalPatients',
    label: 'Total Number of Patients',
    description: 'Planned enrollment count',
    defaultEnabled: true,
    getValue: (trial) => trial.protocolSection?.designModule?.enrollmentInfo?.count || null,
    formatValue: (value) => value ? value.toString() : 'Not specified'
  },
  {
    id: 'studyComparators',
    label: 'Study Comparators',
    description: 'Interventions and comparators used in the study',
    defaultEnabled: true,
    getValue: (trial) => trial.protocolSection?.armsInterventionsModule?.interventions || null,
    formatValue: (value) => {
      if (!value || !Array.isArray(value)) return 'Not specified';
      return value.map(intervention =>
        `${intervention.type || 'Unknown'}: ${intervention.name || 'Unnamed'}`
      ).join('; ');
    }
  },
  {
    id: 'studyDates',
    label: 'Study Start and End',
    description: 'Start and completion dates',
    defaultEnabled: true,
    getValue: (trial) => ({
      start: trial.protocolSection?.statusModule?.startDateStruct?.date,
      completion: trial.protocolSection?.statusModule?.completionDateStruct?.date
    }),
    formatValue: (value) => {
      if (!value || typeof value !== 'object') return 'Not specified';
      const start = value.start || 'Not specified';
      const completion = value.completion || 'Not specified';
      return `Start: ${start}; End: ${completion}`;
    }
  },
  {
    id: 'primaryEndpoints',
    label: 'Primary Endpoints',
    description: 'List of primary outcome measures',
    defaultEnabled: true,
    getValue: (trial) => trial.protocolSection?.outcomesModule?.primaryOutcomes || null,
    formatValue: (value) => {
      if (!value || !Array.isArray(value)) return 'Not specified';
      return value.map(outcome => outcome.measure || 'Unnamed endpoint').join('; ');
    }
  },
  {
    id: 'secondaryEndpoints',
    label: 'Secondary Endpoints',
    description: 'List of secondary outcome measures',
    defaultEnabled: true,
    getValue: (trial) => trial.protocolSection?.outcomesModule?.secondaryOutcomes || null,
    formatValue: (value) => {
      if (!value || !Array.isArray(value)) return 'Not specified';
      return value.map(outcome => outcome.measure || 'Unnamed endpoint').join('; ');
    }
  },
  {
    id: 'conditions',
    label: 'Conditions',
    description: 'Medical conditions being studied',
    defaultEnabled: false,
    getValue: (trial) => trial.protocolSection?.conditionsModule?.conditions || null,
    formatValue: (value) => {
      if (!value || !Array.isArray(value)) return 'Not specified';
      return value.join('; ');
    }
  },
  {
    id: 'locations',
    label: 'Study Locations',
    description: 'Geographic locations where the study is conducted',
    defaultEnabled: false,
    getValue: (trial) => trial.protocolSection?.contactsLocationsModule?.locations || null,
    formatValue: (value) => {
      if (!value || !Array.isArray(value)) return 'Not specified';
      return value.slice(0, 3).map(location =>
        [location.city, location.state, location.country].filter(Boolean).join(', ')
      ).join('; ') + (value.length > 3 ? ` (+${value.length - 3} more)` : '');
    }
  },
  {
    id: 'studySponsor',
    label: 'Study Sponsor',
    description: 'Organization sponsoring the clinical trial',
    defaultEnabled: false,
    getValue: (trial) => trial.protocolSection?.sponsorCollaboratorsModule?.leadSponsor?.name || null,
    formatValue: (value) => value || 'Not specified'
  },
  {
    id: 'lastUpdated',
    label: 'Last Updated',
    description: 'Date when the trial information was last updated',
    defaultEnabled: false,
    getValue: (trial) => trial.protocolSection?.statusModule?.lastUpdatePostDateStruct?.date || null,
    formatValue: (value) => value || 'Not specified'
  },
  {
    id: 'adverseEventsCollected',
    label: 'Adverse Events Collected',
    description: 'Information about adverse event collection and reporting',
    defaultEnabled: false,
    getValue: (trial) => {
      // This information is typically in the protocol or study documents
      // For now, we'll check if there's any safety-related information
      const hasResults = trial.hasResults;
      const outcomesModule = trial.protocolSection?.outcomesModule;
      const hasSafetyOutcomes = outcomesModule?.primaryOutcomes?.some(outcome =>
        outcome.measure?.toLowerCase().includes('safety') ||
        outcome.measure?.toLowerCase().includes('adverse') ||
        outcome.measure?.toLowerCase().includes('tolerability')
      ) || outcomesModule?.secondaryOutcomes?.some(outcome =>
        outcome.measure?.toLowerCase().includes('safety') ||
        outcome.measure?.toLowerCase().includes('adverse') ||
        outcome.measure?.toLowerCase().includes('tolerability')
      );
      return { hasResults, hasSafetyOutcomes };
    },
    formatValue: (value) => {
      if (!value || typeof value !== 'object') return 'Not specified';
      if (value.hasResults) return 'Results available - check study details';
      if (value.hasSafetyOutcomes) return 'Safety outcomes defined';
      return 'Not explicitly specified';
    }
  },
  {
    id: 'diagnosticsUsed',
    label: 'Diagnostics Used',
    description: 'Diagnostic tests, biomarkers, or assessments used in the study',
    defaultEnabled: false,
    getValue: (trial) => {
      // Look for diagnostic-related keywords in outcomes and interventions
      const outcomes = [
        ...(trial.protocolSection?.outcomesModule?.primaryOutcomes || []),
        ...(trial.protocolSection?.outcomesModule?.secondaryOutcomes || [])
      ];
      const diagnosticOutcomes = outcomes.filter(outcome => {
        const measure = outcome.measure?.toLowerCase() || '';
        return measure.includes('biomarker') || measure.includes('diagnostic') ||
               measure.includes('imaging') || measure.includes('laboratory') ||
               measure.includes('test') || measure.includes('assay');
      });
      return diagnosticOutcomes.length > 0 ? diagnosticOutcomes : null;
    },
    formatValue: (value) => {
      if (!value || !Array.isArray(value)) return 'Not specified';
      return value.map(outcome => outcome.measure).join('; ');
    }
  },
  {
    id: 'genericPROs',
    label: 'Generic Patient Reported Outcomes',
    description: 'Generic quality of life and patient-reported outcome measures',
    defaultEnabled: false,
    getValue: (trial) => {
      const outcomes = [
        ...(trial.protocolSection?.outcomesModule?.primaryOutcomes || []),
        ...(trial.protocolSection?.outcomesModule?.secondaryOutcomes || [])
      ];
      const proOutcomes = outcomes.filter(outcome => {
        const measure = outcome.measure?.toLowerCase() || '';
        return measure.includes('quality of life') || measure.includes('qol') ||
               measure.includes('patient reported') || measure.includes('pro') ||
               measure.includes('sf-36') || measure.includes('eq-5d') ||
               measure.includes('promis') || measure.includes('questionnaire');
      });
      return proOutcomes.length > 0 ? proOutcomes : null;
    },
    formatValue: (value) => {
      if (!value || !Array.isArray(value)) return 'Not specified';
      return value.map(outcome => outcome.measure).join('; ');
    }
  },
  {
    id: 'diseaseSpecificPROs',
    label: 'Disease Specific Patient Reported Outcomes',
    description: 'Disease-specific patient-reported outcome measures and scales',
    defaultEnabled: false,
    getValue: (trial) => {
      const outcomes = [
        ...(trial.protocolSection?.outcomesModule?.primaryOutcomes || []),
        ...(trial.protocolSection?.outcomesModule?.secondaryOutcomes || [])
      ];
      const diseaseProOutcomes = outcomes.filter(outcome => {
        const measure = outcome.measure?.toLowerCase() || '';
        // Look for disease-specific scales and assessments
        return measure.includes('scale') || measure.includes('index') ||
               measure.includes('score') || measure.includes('assessment') ||
               measure.includes('inventory') || measure.includes('rating');
      });
      return diseaseProOutcomes.length > 0 ? diseaseProOutcomes : null;
    },
    formatValue: (value) => {
      if (!value || !Array.isArray(value)) return 'Not specified';
      return value.map(outcome => outcome.measure).join('; ');
    }
  },
  {
    id: 'digitalEndpoints',
    label: 'Digital Endpoints Collected',
    description: 'Digital health technologies and remote monitoring endpoints',
    defaultEnabled: false,
    getValue: (trial) => {
      const outcomes = [
        ...(trial.protocolSection?.outcomesModule?.primaryOutcomes || []),
        ...(trial.protocolSection?.outcomesModule?.secondaryOutcomes || [])
      ];
      const digitalOutcomes = outcomes.filter(outcome => {
        const measure = outcome.measure?.toLowerCase() || '';
        return measure.includes('digital') || measure.includes('remote') ||
               measure.includes('wearable') || measure.includes('sensor') ||
               measure.includes('mobile') || measure.includes('app') ||
               measure.includes('electronic') || measure.includes('telemedicine');
      });
      return digitalOutcomes.length > 0 ? digitalOutcomes : null;
    },
    formatValue: (value) => {
      if (!value || !Array.isArray(value)) return 'Not specified';
      return value.map(outcome => outcome.measure).join('; ');
    }
  },
  {
    id: 'healthEconomicEndpoints',
    label: 'Health Economic Endpoints Collected',
    description: 'Health economics and outcomes research endpoints',
    defaultEnabled: false,
    getValue: (trial) => {
      const outcomes = [
        ...(trial.protocolSection?.outcomesModule?.primaryOutcomes || []),
        ...(trial.protocolSection?.outcomesModule?.secondaryOutcomes || [])
      ];
      const heorOutcomes = outcomes.filter(outcome => {
        const measure = outcome.measure?.toLowerCase() || '';
        return measure.includes('cost') || measure.includes('economic') ||
               measure.includes('resource') || measure.includes('utilization') ||
               measure.includes('healthcare use') || measure.includes('burden') ||
               measure.includes('productivity') || measure.includes('work');
      });
      return heorOutcomes.length > 0 ? heorOutcomes : null;
    },
    formatValue: (value) => {
      if (!value || !Array.isArray(value)) return 'Not specified';
      return value.map(outcome => outcome.measure).join('; ');
    }
  },
  {
    id: 'inclusionCriteria',
    label: 'Inclusion Criteria',
    description: 'Key inclusion criteria for study participation',
    defaultEnabled: false,
    getValue: (trial) => trial.protocolSection?.eligibilityModule?.eligibilityCriteria || null,
    formatValue: (value) => {
      if (!value) return 'Not specified';
      // Extract inclusion criteria (usually comes before exclusion criteria)
      const criteria = value.toString().toLowerCase();
      const inclusionIndex = criteria.indexOf('inclusion criteria');
      const exclusionIndex = criteria.indexOf('exclusion criteria');

      if (inclusionIndex !== -1) {
        const startIndex = inclusionIndex + 'inclusion criteria'.length;
        const endIndex = exclusionIndex !== -1 && exclusionIndex > inclusionIndex ? exclusionIndex : criteria.length;
        const inclusionText = value.toString().substring(startIndex, endIndex).trim();
        return inclusionText.substring(0, 500) + (inclusionText.length > 500 ? '...' : '');
      }
      return criteria.substring(0, 300) + (criteria.length > 300 ? '...' : '');
    }
  },
  {
    id: 'exclusionCriteria',
    label: 'Exclusion Criteria',
    description: 'Key exclusion criteria for study participation',
    defaultEnabled: false,
    getValue: (trial) => trial.protocolSection?.eligibilityModule?.eligibilityCriteria || null,
    formatValue: (value) => {
      if (!value) return 'Not specified';
      // Extract exclusion criteria
      const criteria = value.toString().toLowerCase();
      const exclusionIndex = criteria.indexOf('exclusion criteria');

      if (exclusionIndex !== -1) {
        const startIndex = exclusionIndex + 'exclusion criteria'.length;
        const exclusionText = value.toString().substring(startIndex).trim();
        return exclusionText.substring(0, 500) + (exclusionText.length > 500 ? '...' : '');
      }
      return 'See full eligibility criteria';
    }
  },
  {
    id: 'publications',
    label: 'Publications',
    description: 'Published research papers and references related to the study',
    defaultEnabled: false,
    getValue: (trial) => trial.protocolSection?.referencesModule?.references || null,
    formatValue: (value) => {
      if (!value || !Array.isArray(value)) return 'Not specified';
      return value.map(ref => {
        if (ref.url) {
          return `${ref.citation || 'Publication'} (${ref.url})`;
        }
        return ref.citation || 'Publication available';
      }).join('; ');
    }
  }
];

export default function ClinicalTrialsPage() {
  // Add this to prevent hydration mismatches
  const [mounted, setMounted] = useState(false);

  // Your existing state...
  const [query, setQuery] = useState("");
  const [status, setStatus] = useState("");
  const [phase, setPhase] = useState("");
  const [limit, setLimit] = useState("20");
  const [isSearching, setIsSearching] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  interface SearchResult {
    id: number;
    query: string;
    trials: ClinicalTrial[];
  }

  const [searches, setSearches] = useState<SearchResult[]>([]);
  const trials = useMemo(() => searches.flatMap((s) => s.trials), [searches]);
  const [bookmarkedTrials, setBookmarkedTrials] = useLocalStorage<ClinicalTrial[]>(
    "clinical-trials-bookmarks",
    []
  );
  const [sortBy, setSortBy] = useState("default"); // Changed from empty string to "default"
  const [sortOrder, setSortOrder] = useState("asc");
  const [selectedIds, setSelectedIds] = useState<string[]>([]);

  // Comparison feature state
  const [isComparisonOpen, setIsComparisonOpen] = useState(false);
  const [enabledDomains, setEnabledDomains] = useLocalStorage<string[]>(
    "clinical-trials-comparison-domains",
    COMPARISON_DOMAINS.filter(d => d.defaultEnabled).map(d => d.id)
  );

  // IMPORTANT: Always define all useMemo hooks, even if the data isn't used yet
  // This ensures hooks are called in the same order on every render
  const statusData = useMemo(() => {
    if (!mounted) return [];
    const counts: Record<string, number> = {};
    trials.forEach((t) => {
      const st = t.protocolSection?.statusModule?.overallStatus || "Unknown";
      counts[st] = (counts[st] || 0) + 1;
    });
    return Object.entries(counts).map(([name, value]) => ({ name, value }));
  }, [trials, mounted]);

  const phaseData = useMemo(() => {
    if (!mounted) return [];
    const counts: Record<string, number> = {};
    trials.forEach((t) => {
      const ph = t.protocolSection?.designModule?.phases?.[0] || "Unknown";
      counts[ph] = (counts[ph] || 0) + 1;
    });
    return Object.entries(counts).map(([name, value]) => ({ name, value }));
  }, [trials, mounted]);

  const endpointChartData = useMemo(() => {
    if (!mounted) return [];
    return selectedIds.map((id) => {
      const trial = trials.find(
        (t) => t.protocolSection?.identificationModule?.nctId === id
      );
      return {
        name: trial?.protocolSection?.identificationModule?.briefTitle || id,
        primary: trial?.protocolSection?.outcomesModule?.primaryOutcomes?.length || 0,
        secondary: trial?.protocolSection?.outcomesModule?.secondaryOutcomes?.length || 0,
      };
    });
  }, [selectedIds, trials, mounted]);

  // Comparison data
  const selectedTrials = useMemo(() => {
    if (!mounted) return [];
    return selectedIds.map(id =>
      trials.find(t => t.protocolSection?.identificationModule?.nctId === id)
    ).filter(Boolean) as ClinicalTrial[];
  }, [selectedIds, trials, mounted]);

  const comparisonData = useMemo(() => {
    if (!mounted || selectedTrials.length === 0) return [];

    return COMPARISON_DOMAINS
      .filter(domain => enabledDomains.includes(domain.id))
      .map(domain => ({
        domain,
        values: selectedTrials.map(trial => ({
          trial,
          value: domain.getValue(trial),
          formattedValue: domain.formatValue(domain.getValue(trial))
        }))
      }));
  }, [selectedTrials, enabledDomains, mounted]);

  // Set mounted to true after initial render
  useEffect(() => {
    setMounted(true);
  }, []);

  // Only render the full component when mounted
  if (!mounted) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="flex justify-between items-center mb-6">
          <Link href="/">
            <Button variant="ghost" className="pl-0 hover:bg-transparent">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Evicenter
            </Button>
          </Link>
          <ThemeToggle />
        </div>
        <h1 className="text-3xl font-bold mb-6">Clinical Trials Finder</h1>
        <div className="prose max-w-none mb-8 dark:text-gray-200">
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  const sortTrials = (arr: ClinicalTrial[]) => {
    if (!sortBy) return arr;
    const getVal = (t: ClinicalTrial) => {
      switch (sortBy) {
        case "phase":
          return t.protocolSection?.designModule?.phases?.[0] || "";
        case "startDate":
          return t.protocolSection?.statusModule?.startDateStruct?.date || "";
        case "completionDate":
          return (
            t.protocolSection?.statusModule?.completionDateStruct?.date || ""
          );
        case "enrollment":
          return t.protocolSection?.designModule?.enrollmentInfo?.count || 0;
        default:
          return t.protocolSection?.identificationModule?.briefTitle || "";
      }
    };
    const sorted = [...arr].sort((a, b) => {
      const av = getVal(a) as any;
      const bv = getVal(b) as any;
      if (av > bv) return sortOrder === "asc" ? 1 : -1;
      if (av < bv) return sortOrder === "asc" ? -1 : 1;
      return 0;
    });
    return sorted;
  };

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!query.trim()) return;
    setIsSearching(true);
    try {
      const params = new URLSearchParams({ query, limit });
      if (status && status !== "any") {
        params.set("status", status);
      }
      if (phase && phase !== "any") {
        params.set("phase", phase);
      }
      const res = await fetch(`/api/search-clinical-trials?${params.toString()}`);
      const data = await res.json();
      if (!res.ok || data.error) {
        throw new Error(data.error || "Request failed");
      }
      setSearches((prev) => [
        ...prev,
        { id: Date.now(), query, trials: data.trials || [] },
      ]);
      setErrorMessage("");
    } catch (error) {
      console.error("Clinical trials search error", error);
      setErrorMessage("Failed to fetch trials. Please try again.");
    } finally {
      setIsSearching(false);
    }
  };

  const toggleBookmark = (trial: ClinicalTrial) => {
    const id = trial.protocolSection?.identificationModule?.nctId;
    if (!id) return;
    const exists = bookmarkedTrials.some(
      (t) => t.protocolSection?.identificationModule?.nctId === id
    );
    const updated = exists
      ? bookmarkedTrials.filter(
          (t) => t.protocolSection?.identificationModule?.nctId !== id
        )
      : [...bookmarkedTrials, trial];
    setBookmarkedTrials(updated);
  };

  const toggleSelected = (id: string) => {
    setSelectedIds((prev) =>
      prev.includes(id) ? prev.filter((i) => i !== id) : [...prev, id]
    );
  };

  const toggleDomain = (domainId: string) => {
    const newDomains = enabledDomains.includes(domainId)
      ? enabledDomains.filter(id => id !== domainId)
      : [...enabledDomains, domainId];
    setEnabledDomains(newDomains);
  };

  const clearComparison = () => {
    setSelectedIds([]);
  };

  const selectAllFromSearch = (searchId: number) => {
    const search = searches.find(s => s.id === searchId);
    if (!search) return;

    const searchTrialIds = search.trials
      .map(t => t.protocolSection?.identificationModule?.nctId)
      .filter(Boolean) as string[];

    setSelectedIds(prev => {
      const newIds = [...prev];
      searchTrialIds.forEach(id => {
        if (!newIds.includes(id)) {
          newIds.push(id);
        }
      });
      return newIds;
    });
  };

  const deselectAllFromSearch = (searchId: number) => {
    const search = searches.find(s => s.id === searchId);
    if (!search) return;

    const searchTrialIds = search.trials
      .map(t => t.protocolSection?.identificationModule?.nctId)
      .filter(Boolean) as string[];

    setSelectedIds(prev => prev.filter(id => !searchTrialIds.includes(id)));
  };

  const exportComparisonCsv = () => {
    if (selectedTrials.length === 0) return;

    const headers = ['Domain', ...selectedTrials.map(trial =>
      trial.protocolSection?.identificationModule?.briefTitle ||
      trial.protocolSection?.identificationModule?.nctId || 'Unknown'
    )];

    const rows = comparisonData.map(row => [
      row.domain.label,
      ...row.values.map(v => v.formattedValue)
    ]);

    const csvContent = [headers, ...rows]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'clinical-trials-comparison.csv';
    a.click();
    URL.revokeObjectURL(url);
  };

  const removeSearch = (id: number) => {
    const toRemove = searches.find((s) => s.id === id);
    setSearches(searches.filter((s) => s.id !== id));
    if (toRemove) {
      const ids = toRemove.trials
        .map((t) => t.protocolSection?.identificationModule?.nctId)
        .filter(Boolean) as string[];
      setSelectedIds((prev) => prev.filter((sid) => !ids.includes(sid)));
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex justify-between items-center mb-6">
        <Link href="/">
          <Button variant="ghost" className="pl-0 hover:bg-transparent">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Evicenter
          </Button>
        </Link>
        <ThemeToggle />
      </div>

      <h1 className="text-3xl font-bold mb-6">Clinical Trials Finder</h1>

      <div className="prose max-w-none mb-8 dark:text-gray-200">
        <p>
          Search ClinicalTrials.gov for studies relevant to your research. Use optional filters to narrow down results.
          <strong> Select trials using the checkboxes to compare them side by side.</strong>
        </p>
      </div>

      <form onSubmit={handleSearch} className="grid gap-2 mb-8 sm:grid-cols-7">
        <div className="sm:col-span-2">
          <Input
            type="search"
            placeholder="Enter keywords..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
          />
        </div>
        <Select onValueChange={setStatus} value={status}>
          <SelectTrigger>
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="any">Any Status</SelectItem>
            <SelectItem value="Recruiting">Recruiting</SelectItem>
            <SelectItem value="Active, not recruiting">Active, not recruiting</SelectItem>
            <SelectItem value="Completed">Completed</SelectItem>
          </SelectContent>
        </Select>
        <Select onValueChange={setPhase} value={phase}>
          <SelectTrigger>
            <SelectValue placeholder="Phase" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="any">Any Phase</SelectItem>
            <SelectItem value="Phase 1">Phase 1</SelectItem>
            <SelectItem value="Phase 2">Phase 2</SelectItem>
            <SelectItem value="Phase 3">Phase 3</SelectItem>
            <SelectItem value="Phase 4">Phase 4</SelectItem>
          </SelectContent>
        </Select>
        <Input
          type="number"
          min="1"
          placeholder="Limit"
          value={limit}
          onChange={(e) => setLimit(e.target.value)}
        />
        <Select onValueChange={setSortBy} value={sortBy}>
          <SelectTrigger>
            <SelectValue placeholder="Sort By" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="default">Relevance</SelectItem>
            <SelectItem value="phase">Phase</SelectItem>
            <SelectItem value="startDate">Start Date</SelectItem>
            <SelectItem value="completionDate">Completion Date</SelectItem>
            <SelectItem value="enrollment">Enrollment</SelectItem>
          </SelectContent>
        </Select>
        <Select onValueChange={setSortOrder} value={sortOrder}>
          <SelectTrigger>
            <SelectValue placeholder="Order" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="asc">Ascending</SelectItem>
            <SelectItem value="desc">Descending</SelectItem>
          </SelectContent>
        </Select>
        <Button
          type="submit"
          className="bg-amber-400 text-black hover:bg-amber-500"
          disabled={isSearching}
        >
          {isSearching ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black"></div>
          ) : (
            <>
              <Search className="h-4 w-4 mr-2" />
              Search
            </>
          )}
        </Button>
      </form>

      {errorMessage && (
        <div className="mb-4 text-sm text-red-600 dark:text-red-400">
          {errorMessage}
        </div>
      )}

      {/* Comparison Quick Access */}
      {selectedIds.length > 0 && (
        <div className="mb-6 p-4 bg-amber-50 dark:bg-amber-950/20 border border-amber-200 dark:border-amber-800 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <GitCompare className="h-5 w-5 text-amber-600" />
              <span className="font-medium text-amber-800 dark:text-amber-200">
                {selectedIds.length} trial{selectedIds.length > 1 ? 's' : ''} selected for comparison
              </span>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsComparisonOpen(true)}
                className="border-amber-300 text-amber-700 hover:bg-amber-100 dark:border-amber-700 dark:text-amber-300 dark:hover:bg-amber-900/20"
              >
                <Settings className="h-4 w-4 mr-1" />
                Configure
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={clearComparison}
                className="border-amber-300 text-amber-700 hover:bg-amber-100 dark:border-amber-700 dark:text-amber-300 dark:hover:bg-amber-900/20"
              >
                <X className="h-4 w-4 mr-1" />
                Clear
              </Button>
            </div>
          </div>
        </div>
      )}

      {mounted && trials.length > 0 && (
        <div className="mb-8 grid gap-8 sm:grid-cols-2">
          <div>
            <h2 className="text-lg font-semibold mb-2">Status Distribution</h2>
            <ResponsiveContainer width="100%" height={200}>
              <BarChart data={statusData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis allowDecimals={false} />
                <Tooltip />
                <Legend />
                <Bar dataKey="value" fill="#f59e0b" />
              </BarChart>
            </ResponsiveContainer>
          </div>
          <div>
            <h2 className="text-lg font-semibold mb-2">Phase Distribution</h2>
            <ResponsiveContainer width="100%" height={200}>
              <BarChart data={phaseData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis allowDecimals={false} />
                <Tooltip />
                <Legend />
                <Bar dataKey="value" fill="#f59e0b" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      )}

      {bookmarkedTrials.length > 0 && (
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-2">Bookmarked Trials</h2>
          <ul className="list-disc ml-5 space-y-1">
            {bookmarkedTrials.map((t, i) => {
              const id = t.protocolSection?.identificationModule?.nctId;
              const title =
                t.protocolSection?.identificationModule?.briefTitle || id;
              return (
                <li key={id || i}>
                  {id ? (
                    <a
                      href={`https://clinicaltrials.gov/study/${id}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                    >
                      {title}
                    </a>
                  ) : (
                    title
                  )}
                </li>
              );
            })}
          </ul>
        </div>
      )}

      {selectedIds.length > 0 && (
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold">Trial Comparison</h2>
            <div className="flex gap-2">
              <Dialog open={isComparisonOpen} onOpenChange={setIsComparisonOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm" className="gap-1">
                    <Settings className="h-4 w-4" />
                    Configure
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-md">
                  <DialogHeader>
                    <DialogTitle>Comparison Settings</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">Selected Trials ({selectedTrials.length})</h4>
                      <div className="space-y-1 max-h-32 overflow-y-auto">
                        {selectedTrials.map(trial => (
                          <div key={trial.protocolSection?.identificationModule?.nctId} className="text-sm text-muted-foreground">
                            {trial.protocolSection?.identificationModule?.briefTitle ||
                             trial.protocolSection?.identificationModule?.nctId}
                          </div>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2">Comparison Domains</h4>
                      <div className="space-y-2 max-h-64 overflow-y-auto">
                        {COMPARISON_DOMAINS.map(domain => (
                          <div key={domain.id} className="flex items-start space-x-2">
                            <Checkbox
                              id={domain.id}
                              checked={enabledDomains.includes(domain.id)}
                              onCheckedChange={() => toggleDomain(domain.id)}
                            />
                            <div className="grid gap-1.5 leading-none">
                              <label
                                htmlFor={domain.id}
                                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                              >
                                {domain.label}
                              </label>
                              <p className="text-xs text-muted-foreground">
                                {domain.description}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
              <Button variant="outline" size="sm" onClick={exportComparisonCsv} className="gap-1">
                <Download className="h-4 w-4" />
                Export
              </Button>
              <Button variant="outline" size="sm" onClick={clearComparison} className="gap-1">
                <X className="h-4 w-4" />
                Clear
              </Button>
            </div>
          </div>

          {/* Comprehensive Comparison Table */}
          <div className="overflow-x-auto border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-48 font-semibold">Domain</TableHead>
                  {selectedTrials.map(trial => {
                    const nctId = trial.protocolSection?.identificationModule?.nctId;
                    const title = trial.protocolSection?.identificationModule?.briefTitle || 'Untitled Study';

                    return (
                      <TableHead key={nctId} className="min-w-64">
                        <div className="space-y-1">
                          <div className="font-semibold text-sm">
                            {nctId ? (
                              <a
                                href={`https://clinicaltrials.gov/study/${nctId}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300 hover:underline"
                              >
                                {title}
                              </a>
                            ) : (
                              title
                            )}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {nctId}
                          </div>
                        </div>
                      </TableHead>
                    );
                  })}
                </TableRow>
              </TableHeader>
              <TableBody>
                {comparisonData.map(row => (
                  <TableRow key={row.domain.id}>
                    <TableCell className="font-medium bg-muted/30">
                      <div className="space-y-1">
                        <div>{row.domain.label}</div>
                        <div className="text-xs text-muted-foreground">
                          {row.domain.description}
                        </div>
                      </div>
                    </TableCell>
                    {row.values.map((value, index) => (
                      <TableCell key={index} className="align-top">
                        <div className="text-sm whitespace-pre-wrap">
                          {row.domain.id === 'studyDesign' ? (
                            <div className="space-y-1">
                              {value.formattedValue.split('\n').map((line, lineIndex) => {
                                // Check if line contains markdown-style bold formatting
                                if (line.includes('**')) {
                                  const parts = line.split('**');
                                  return (
                                    <div key={lineIndex} className="text-sm">
                                      {parts.map((part, partIndex) =>
                                        partIndex % 2 === 1 ? (
                                          <span key={partIndex} className="font-semibold">{part}</span>
                                        ) : (
                                          <span key={partIndex}>{part}</span>
                                        )
                                      )}
                                    </div>
                                  );
                                }
                                return <div key={lineIndex} className="text-sm">{line}</div>;
                              })}
                            </div>
                          ) : (
                            value.formattedValue
                          )}
                        </div>
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Legacy Endpoint Chart for backward compatibility */}
          <div className="mt-8">
            <h3 className="text-md font-semibold mb-2">Endpoint Count Comparison</h3>
            <ResponsiveContainer width="100%" height={250}>
              <BarChart data={endpointChartData} stackOffset="sign">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis allowDecimals={false} />
                <Tooltip />
                <Legend />
                <Bar dataKey="primary" fill="#f59e0b" name="Primary" />
                <Bar dataKey="secondary" fill="#fb923c" name="Secondary" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      )}

      <div className="space-y-4">
        {searches.length === 0 && !isSearching && (
          <p className="text-sm text-muted-foreground">No trials found.</p>
        )}
        <div
          className="grid gap-4"
          style={{ gridTemplateColumns: `repeat(${searches.length}, minmax(0,1fr))` }}
        >
          {searches.map((search) => {
            const searchTrialIds = search.trials
              .map(t => t.protocolSection?.identificationModule?.nctId)
              .filter(Boolean) as string[];
            const allSelected = searchTrialIds.length > 0 && searchTrialIds.every(id => selectedIds.includes(id));
            const someSelected = searchTrialIds.some(id => selectedIds.includes(id));

            return (
              <div key={search.id} className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <h3 className="font-semibold">{search.query}</h3>
                    <span className="text-sm text-muted-foreground">
                      ({search.trials.length} trials)
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    {searchTrialIds.length > 0 && (
                      <>
                        {allSelected ? (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => deselectAllFromSearch(search.id)}
                            className="text-xs"
                          >
                            Deselect All
                          </Button>
                        ) : (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => selectAllFromSearch(search.id)}
                            className="text-xs"
                          >
                            {someSelected ? 'Select Remaining' : 'Select All'}
                          </Button>
                        )}
                      </>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeSearch(search.id)}
                    >
                      Remove
                    </Button>
                  </div>
                </div>
              {sortTrials(search.trials).map((trial, index) => {
                const id = trial.protocolSection?.identificationModule?.nctId;
                const isBookmarked = bookmarkedTrials.some(
                  (t) => t.protocolSection?.identificationModule?.nctId === id
                );
                const isSelected = id ? selectedIds.includes(id) : false;
                return (
                  <Card key={id || index}>
                    <CardHeader className="flex justify-between items-start">
                      <CardTitle className="text-lg">
                        {trial.protocolSection?.identificationModule?.briefTitle ||
                          "Untitled Study"}
                      </CardTitle>
                      {id && (
                        <div className="flex items-center gap-2">
                          <Checkbox
                            checked={isSelected}
                            onCheckedChange={() => toggleSelected(id)}
                            aria-label="Select for comparison"
                          />
                          <button
                            onClick={() => toggleBookmark(trial)}
                            aria-label="Bookmark"
                          >
                            {isBookmarked ? (
                              <BookmarkCheck className="h-5 w-5 text-amber-400" />
                            ) : (
                              <Bookmark className="h-5 w-5" />
                            )}
                          </button>
                        </div>
                      )}
                    </CardHeader>
                    <CardContent className="space-y-2">
                      {trial.protocolSection?.descriptionModule?.briefSummary && (
                        <p className="text-sm text-muted-foreground">
                          {trial.protocolSection.descriptionModule.briefSummary.substring(
                            0,
                            200
                          )}
                          ...
                        </p>
                      )}
                      <div className="text-sm space-y-1">
                        <div>
                          {trial.protocolSection?.statusModule?.overallStatus && (
                            <span className="mr-4">
                              <span className="font-medium">Status: </span>
                              {trial.protocolSection.statusModule.overallStatus}
                            </span>
                          )}
                          {trial.protocolSection?.designModule?.phases?.[0] && (
                            <span>
                              <span className="font-medium">Phase: </span>
                              {trial.protocolSection.designModule.phases[0]}
                            </span>
                          )}
                        </div>
                        {trial.protocolSection?.designModule?.studyType && (
                          <div>
                            <span className="font-medium">Study Type: </span>
                            {trial.protocolSection.designModule.studyType}
                          </div>
                        )}
                        {trial.protocolSection?.designModule?.enrollmentInfo?.count && (
                          <div>
                            <span className="font-medium">Enrollment: </span>
                            {trial.protocolSection.designModule.enrollmentInfo.count}
                          </div>
                        )}
                        {trial.protocolSection?.statusModule?.startDateStruct?.date && (
                          <div>
                            <span className="font-medium">Start: </span>
                            {trial.protocolSection.statusModule.startDateStruct.date}
                          </div>
                        )}
                        {trial.protocolSection?.statusModule?.completionDateStruct?.date && (
                          <div>
                            <span className="font-medium">Completion: </span>
                            {trial.protocolSection.statusModule.completionDateStruct.date}
                          </div>
                        )}
                        {trial.protocolSection?.contactsLocationsModule?.locations?.length && (
                          <div>
                            <span className="font-medium">Location: </span>
                            {[
                              trial.protocolSection.contactsLocationsModule.locations[0]?.city,
                              trial.protocolSection.contactsLocationsModule.locations[0]?.state,
                              trial.protocolSection.contactsLocationsModule.locations[0]?.country,
                            ]
                              .filter(Boolean)
                              .join(', ')}
                          </div>
                        )}
                      </div>
                      {trial.protocolSection?.conditionsModule?.conditions?.length ? (
                        <div className="text-sm">
                          <span className="font-medium">Conditions: </span>
                          <span className="text-muted-foreground">
                            {trial.protocolSection.conditionsModule.conditions.join(', ')}
                          </span>
                        </div>
                      ) : null}
                      {trial.protocolSection?.outcomesModule?.primaryOutcomes?.length ? (
                        <div className="text-sm">
                          <span className="font-medium">Primary Endpoints:</span>
                          <ul className="list-disc ml-5">
                            {trial.protocolSection.outcomesModule.primaryOutcomes.map(
                              (o, i) => (
                                <li key={i}>
                                  {o.measure}
                                  {o.description && (
                                    <span className="text-muted-foreground ml-1"> - {o.description}</span>
                                  )}
                                  {o.timeFrame && (
                                    <span className="text-muted-foreground ml-1"> ({o.timeFrame})</span>
                                  )}
                                </li>
                              )
                            )}
                          </ul>
                        </div>
                      ) : null}
                      {trial.protocolSection?.outcomesModule?.secondaryOutcomes?.length ? (
                        <div className="text-sm">
                          <span className="font-medium">Secondary Endpoints:</span>
                          <ul className="list-disc ml-5">
                            {trial.protocolSection.outcomesModule.secondaryOutcomes.map(
                              (o, i) => (
                                <li key={i}>
                                  {o.measure}
                                  {o.description && (
                                    <span className="text-muted-foreground ml-1"> - {o.description}</span>
                                  )}
                                  {o.timeFrame && (
                                    <span className="text-muted-foreground ml-1"> ({o.timeFrame})</span>
                                  )}
                                </li>
                              )
                            )}
                          </ul>
                        </div>
                      ) : null}
                      {trial.protocolSection?.referencesModule?.references?.length ? (
                        <div className="text-sm">
                          <span className="font-medium">Publications:</span>
                          <ul className="list-disc ml-5">
                            {trial.protocolSection.referencesModule.references.map((r, i) => (
                              <li key={i}>
                                {r.url ? (
                                  <a
                                    href={r.url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                                  >
                                    {r.citation || r.url}
                                  </a>
                                ) : (
                                  r.citation
                                )}
                              </li>
                            ))}
                          </ul>
                        </div>
                      ) : null}
                      {trial.protocolSection?.moreInfoModule?.links?.length ? (
                        <div className="text-sm">
                          <span className="font-medium">Links:</span>
                          <ul className="list-disc ml-5">
                            {trial.protocolSection.moreInfoModule.links.map((l, i) => (
                              <li key={i}>
                                <a
                                  href={l.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                                >
                                  {l.label || l.url}
                                </a>
                              </li>
                            ))}
                          </ul>
                        </div>
                      ) : null}
                      {id && (
                        <a
                          href={`https://clinicaltrials.gov/study/${id}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300 block"
                        >
                          View on ClinicalTrials.gov ({id})
                        </a>
                      )}
                    </CardContent>
                  </Card>
                );
              })}
            </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}

