"use client";

import React, { useState, useMemo, useEffect } from "react";
import Link from "next/link";
import { ArrowLeft, Search, Bookmark, BookmarkCheck } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { ThemeToggle } from "@/components/ThemeToggle";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { useLocalStorage } from "@/hooks/use-local-storage";
import { ResponsiveContainer, BarChart, Bar, CartesianGrid, XAxis, YAxis, Tooltip, Legend } from "recharts";

interface ClinicalTrial {
  protocolSection?: {
    identificationModule?: {
      nctId?: string;
      briefTitle?: string;
    };
    descriptionModule?: {
      briefSummary?: string;
    };
    conditionsModule?: {
      conditions?: string[];
    };
    statusModule?: {
      overallStatus?: string;
      startDateStruct?: { date?: string };
      completionDateStruct?: { date?: string };
    };
    designModule?: {
      phases?: string[];
      studyType?: string;
      enrollmentInfo?: { count?: number };
    };
    outcomesModule?: {
      primaryOutcomes?: Array<{
        measure?: string;
        description?: string;
        timeFrame?: string;
      }>;
      secondaryOutcomes?: Array<{
        measure?: string;
        description?: string;
        timeFrame?: string;
      }>;
      otherOutcomes?: Array<{
        measure?: string;
        description?: string;
        timeFrame?: string;
      }>;
    };
    contactsLocationsModule?: {
      locations?: Array<{
        city?: string;
        state?: string;
        country?: string;
      }>;
    };
    referencesModule?: {
      references?: Array<{
        citation?: string;
        url?: string;
      }>;
    };
    moreInfoModule?: {
      links?: Array<{
        label?: string;
        url?: string;
      }>;
    };
  };
  hasResults?: boolean;
}

export default function ClinicalTrialsPage() {
  // Add this to prevent hydration mismatches
  const [mounted, setMounted] = useState(false);
  
  // Your existing state...
  const [query, setQuery] = useState("");
  const [status, setStatus] = useState("");
  const [phase, setPhase] = useState("");
  const [limit, setLimit] = useState("20");
  const [isSearching, setIsSearching] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  interface SearchResult {
    id: number;
    query: string;
    trials: ClinicalTrial[];
  }

  const [searches, setSearches] = useState<SearchResult[]>([]);
  const trials = useMemo(() => searches.flatMap((s) => s.trials), [searches]);
  const [bookmarkedTrials, setBookmarkedTrials] = useLocalStorage<ClinicalTrial[]>(
    "clinical-trials-bookmarks",
    []
  );
  const [sortBy, setSortBy] = useState("default"); // Changed from empty string to "default"
  const [sortOrder, setSortOrder] = useState("asc");
  const [selectedIds, setSelectedIds] = useState<string[]>([]);

  // IMPORTANT: Always define all useMemo hooks, even if the data isn't used yet
  // This ensures hooks are called in the same order on every render
  const statusData = useMemo(() => {
    if (!mounted) return [];
    const counts: Record<string, number> = {};
    trials.forEach((t) => {
      const st = t.protocolSection?.statusModule?.overallStatus || "Unknown";
      counts[st] = (counts[st] || 0) + 1;
    });
    return Object.entries(counts).map(([name, value]) => ({ name, value }));
  }, [trials, mounted]);

  const phaseData = useMemo(() => {
    if (!mounted) return [];
    const counts: Record<string, number> = {};
    trials.forEach((t) => {
      const ph = t.protocolSection?.designModule?.phases?.[0] || "Unknown";
      counts[ph] = (counts[ph] || 0) + 1;
    });
    return Object.entries(counts).map(([name, value]) => ({ name, value }));
  }, [trials, mounted]);

  const endpointChartData = useMemo(() => {
    if (!mounted) return [];
    return selectedIds.map((id) => {
      const trial = trials.find(
        (t) => t.protocolSection?.identificationModule?.nctId === id
      );
      return {
        name: trial?.protocolSection?.identificationModule?.briefTitle || id,
        primary: trial?.protocolSection?.outcomesModule?.primaryOutcomes?.length || 0,
        secondary: trial?.protocolSection?.outcomesModule?.secondaryOutcomes?.length || 0,
      };
    });
  }, [selectedIds, trials, mounted]);

  // Set mounted to true after initial render
  useEffect(() => {
    setMounted(true);
  }, []);

  // Only render the full component when mounted
  if (!mounted) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="flex justify-between items-center mb-6">
          <Link href="/">
            <Button variant="ghost" className="pl-0 hover:bg-transparent">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Evicenter
            </Button>
          </Link>
          <ThemeToggle />
        </div>
        <h1 className="text-3xl font-bold mb-6">Clinical Trials Finder</h1>
        <div className="prose max-w-none mb-8 dark:text-gray-200">
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  const sortTrials = (arr: ClinicalTrial[]) => {
    if (!sortBy) return arr;
    const getVal = (t: ClinicalTrial) => {
      switch (sortBy) {
        case "phase":
          return t.protocolSection?.designModule?.phases?.[0] || "";
        case "startDate":
          return t.protocolSection?.statusModule?.startDateStruct?.date || "";
        case "completionDate":
          return (
            t.protocolSection?.statusModule?.completionDateStruct?.date || ""
          );
        case "enrollment":
          return t.protocolSection?.designModule?.enrollmentInfo?.count || 0;
        default:
          return t.protocolSection?.identificationModule?.briefTitle || "";
      }
    };
    const sorted = [...arr].sort((a, b) => {
      const av = getVal(a) as any;
      const bv = getVal(b) as any;
      if (av > bv) return sortOrder === "asc" ? 1 : -1;
      if (av < bv) return sortOrder === "asc" ? -1 : 1;
      return 0;
    });
    return sorted;
  };

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!query.trim()) return;
    setIsSearching(true);
    try {
      const params = new URLSearchParams({ query, limit });
      if (status && status !== "any") {
        params.set("status", status);
      }
      if (phase && phase !== "any") {
        params.set("phase", phase);
      }
      const res = await fetch(`/api/search-clinical-trials?${params.toString()}`);
      const data = await res.json();
      if (!res.ok || data.error) {
        throw new Error(data.error || "Request failed");
      }
      setSearches((prev) => [
        ...prev,
        { id: Date.now(), query, trials: data.trials || [] },
      ]);
      setErrorMessage("");
    } catch (error) {
      console.error("Clinical trials search error", error);
      setErrorMessage("Failed to fetch trials. Please try again.");
    } finally {
      setIsSearching(false);
    }
  };

  const toggleBookmark = (trial: ClinicalTrial) => {
    const id = trial.protocolSection?.identificationModule?.nctId;
    if (!id) return;
    const exists = bookmarkedTrials.some(
      (t) => t.protocolSection?.identificationModule?.nctId === id
    );
    const updated = exists
      ? bookmarkedTrials.filter(
          (t) => t.protocolSection?.identificationModule?.nctId !== id
        )
      : [...bookmarkedTrials, trial];
    setBookmarkedTrials(updated);
  };

  const toggleSelected = (id: string) => {
    setSelectedIds((prev) =>
      prev.includes(id) ? prev.filter((i) => i !== id) : [...prev, id]
    );
  };

  const removeSearch = (id: number) => {
    const toRemove = searches.find((s) => s.id === id);
    setSearches(searches.filter((s) => s.id !== id));
    if (toRemove) {
      const ids = toRemove.trials
        .map((t) => t.protocolSection?.identificationModule?.nctId)
        .filter(Boolean) as string[];
      setSelectedIds((prev) => prev.filter((sid) => !ids.includes(sid)));
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex justify-between items-center mb-6">
        <Link href="/">
          <Button variant="ghost" className="pl-0 hover:bg-transparent">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Evicenter
          </Button>
        </Link>
        <ThemeToggle />
      </div>

      <h1 className="text-3xl font-bold mb-6">Clinical Trials Finder</h1>

      <div className="prose max-w-none mb-8 dark:text-gray-200">
        <p>
          Search ClinicalTrials.gov for studies relevant to your research. Use optional filters to narrow down results.
        </p>
      </div>

      <form onSubmit={handleSearch} className="grid gap-2 mb-8 sm:grid-cols-7">
        <div className="sm:col-span-2">
          <Input
            type="search"
            placeholder="Enter keywords..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
          />
        </div>
        <Select onValueChange={setStatus} value={status}>
          <SelectTrigger>
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="any">Any Status</SelectItem>
            <SelectItem value="Recruiting">Recruiting</SelectItem>
            <SelectItem value="Active, not recruiting">Active, not recruiting</SelectItem>
            <SelectItem value="Completed">Completed</SelectItem>
          </SelectContent>
        </Select>
        <Select onValueChange={setPhase} value={phase}>
          <SelectTrigger>
            <SelectValue placeholder="Phase" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="any">Any Phase</SelectItem>
            <SelectItem value="Phase 1">Phase 1</SelectItem>
            <SelectItem value="Phase 2">Phase 2</SelectItem>
            <SelectItem value="Phase 3">Phase 3</SelectItem>
            <SelectItem value="Phase 4">Phase 4</SelectItem>
          </SelectContent>
        </Select>
        <Input
          type="number"
          min="1"
          placeholder="Limit"
          value={limit}
          onChange={(e) => setLimit(e.target.value)}
        />
        <Select onValueChange={setSortBy} value={sortBy}>
          <SelectTrigger>
            <SelectValue placeholder="Sort By" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="default">Relevance</SelectItem>
            <SelectItem value="phase">Phase</SelectItem>
            <SelectItem value="startDate">Start Date</SelectItem>
            <SelectItem value="completionDate">Completion Date</SelectItem>
            <SelectItem value="enrollment">Enrollment</SelectItem>
          </SelectContent>
        </Select>
        <Select onValueChange={setSortOrder} value={sortOrder}>
          <SelectTrigger>
            <SelectValue placeholder="Order" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="asc">Ascending</SelectItem>
            <SelectItem value="desc">Descending</SelectItem>
          </SelectContent>
        </Select>
        <Button
          type="submit"
          className="bg-amber-400 text-black hover:bg-amber-500"
          disabled={isSearching}
        >
          {isSearching ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black"></div>
          ) : (
            <>
              <Search className="h-4 w-4 mr-2" />
              Search
            </>
          )}
        </Button>
      </form>

      {errorMessage && (
        <div className="mb-4 text-sm text-red-600 dark:text-red-400">
          {errorMessage}
        </div>
      )}

      {mounted && trials.length > 0 && (
        <div className="mb-8 grid gap-8 sm:grid-cols-2">
          <div>
            <h2 className="text-lg font-semibold mb-2">Status Distribution</h2>
            <ResponsiveContainer width="100%" height={200}>
              <BarChart data={statusData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis allowDecimals={false} />
                <Tooltip />
                <Legend />
                <Bar dataKey="value" fill="#f59e0b" />
              </BarChart>
            </ResponsiveContainer>
          </div>
          <div>
            <h2 className="text-lg font-semibold mb-2">Phase Distribution</h2>
            <ResponsiveContainer width="100%" height={200}>
              <BarChart data={phaseData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis allowDecimals={false} />
                <Tooltip />
                <Legend />
                <Bar dataKey="value" fill="#f59e0b" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      )}

      {bookmarkedTrials.length > 0 && (
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-2">Bookmarked Trials</h2>
          <ul className="list-disc ml-5 space-y-1">
            {bookmarkedTrials.map((t, i) => {
              const id = t.protocolSection?.identificationModule?.nctId;
              const title =
                t.protocolSection?.identificationModule?.briefTitle || id;
              return (
                <li key={id || i}>
                  {id ? (
                    <a
                      href={`https://clinicaltrials.gov/study/${id}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                    >
                      {title}
                    </a>
                  ) : (
                    title
                  )}
                </li>
              );
            })}
          </ul>
        </div>
      )}

      {selectedIds.length > 0 && (
        <div className="mb-8">
          <h2 className="text-lg font-semibold mb-2">Endpoint Comparison</h2>
          <ResponsiveContainer width="100%" height={250}>
            <BarChart data={endpointChartData} stackOffset="sign">
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis allowDecimals={false} />
              <Tooltip />
              <Legend />
              <Bar dataKey="primary" fill="#f59e0b" name="Primary" />
              <Bar dataKey="secondary" fill="#fb923c" name="Secondary" />
            </BarChart>
          </ResponsiveContainer>
          <div className="overflow-x-auto mt-4">
            <table className="min-w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="px-2 py-1 text-left">Study</th>
                  <th className="px-2 py-1 text-left">Primary Endpoints</th>
                  <th className="px-2 py-1 text-left">Secondary Endpoints</th>
                </tr>
              </thead>
              <tbody>
                {selectedIds.map((sid) => {
                  const trial = trials.find(
                    (t) => t.protocolSection?.identificationModule?.nctId === sid
                  );
                  if (!trial) return null;
                  return (
                    <tr key={sid} className="border-b hover:bg-muted/30">
                      <td className="px-2 py-1">
                        {trial.protocolSection?.identificationModule?.briefTitle || sid}
                      </td>
                      <td className="px-2 py-1">
                        {trial.protocolSection?.outcomesModule?.primaryOutcomes
                          ?.map((o) => o.measure)
                          .join('; ') || '-'}
                      </td>
                      <td className="px-2 py-1">
                        {trial.protocolSection?.outcomesModule?.secondaryOutcomes
                          ?.map((o) => o.measure)
                          .join('; ') || '-'}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}

      <div className="space-y-4">
        {searches.length === 0 && !isSearching && (
          <p className="text-sm text-muted-foreground">No trials found.</p>
        )}
        <div
          className="grid gap-4"
          style={{ gridTemplateColumns: `repeat(${searches.length}, minmax(0,1fr))` }}
        >
          {searches.map((search) => (
            <div key={search.id} className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold">{search.query}</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeSearch(search.id)}
                >
                  Remove
                </Button>
              </div>
              {sortTrials(search.trials).map((trial, index) => {
                const id = trial.protocolSection?.identificationModule?.nctId;
                const isBookmarked = bookmarkedTrials.some(
                  (t) => t.protocolSection?.identificationModule?.nctId === id
                );
                const isSelected = id ? selectedIds.includes(id) : false;
                return (
                  <Card key={id || index}>
                    <CardHeader className="flex justify-between items-start">
                      <CardTitle className="text-lg">
                        {trial.protocolSection?.identificationModule?.briefTitle ||
                          "Untitled Study"}
                      </CardTitle>
                      {id && (
                        <div className="flex items-center gap-2">
                          <Checkbox
                            checked={isSelected}
                            onCheckedChange={() => toggleSelected(id)}
                            aria-label="Select for comparison"
                          />
                          <button
                            onClick={() => toggleBookmark(trial)}
                            aria-label="Bookmark"
                          >
                            {isBookmarked ? (
                              <BookmarkCheck className="h-5 w-5 text-amber-400" />
                            ) : (
                              <Bookmark className="h-5 w-5" />
                            )}
                          </button>
                        </div>
                      )}
                    </CardHeader>
                    <CardContent className="space-y-2">
                      {trial.protocolSection?.descriptionModule?.briefSummary && (
                        <p className="text-sm text-muted-foreground">
                          {trial.protocolSection.descriptionModule.briefSummary.substring(
                            0,
                            200
                          )}
                          ...
                        </p>
                      )}
                      <div className="text-sm space-y-1">
                        <div>
                          {trial.protocolSection?.statusModule?.overallStatus && (
                            <span className="mr-4">
                              <span className="font-medium">Status: </span>
                              {trial.protocolSection.statusModule.overallStatus}
                            </span>
                          )}
                          {trial.protocolSection?.designModule?.phases?.[0] && (
                            <span>
                              <span className="font-medium">Phase: </span>
                              {trial.protocolSection.designModule.phases[0]}
                            </span>
                          )}
                        </div>
                        {trial.protocolSection?.designModule?.studyType && (
                          <div>
                            <span className="font-medium">Study Type: </span>
                            {trial.protocolSection.designModule.studyType}
                          </div>
                        )}
                        {trial.protocolSection?.designModule?.enrollmentInfo?.count && (
                          <div>
                            <span className="font-medium">Enrollment: </span>
                            {trial.protocolSection.designModule.enrollmentInfo.count}
                          </div>
                        )}
                        {trial.protocolSection?.statusModule?.startDateStruct?.date && (
                          <div>
                            <span className="font-medium">Start: </span>
                            {trial.protocolSection.statusModule.startDateStruct.date}
                          </div>
                        )}
                        {trial.protocolSection?.statusModule?.completionDateStruct?.date && (
                          <div>
                            <span className="font-medium">Completion: </span>
                            {trial.protocolSection.statusModule.completionDateStruct.date}
                          </div>
                        )}
                        {trial.protocolSection?.contactsLocationsModule?.locations?.length && (
                          <div>
                            <span className="font-medium">Location: </span>
                            {[
                              trial.protocolSection.contactsLocationsModule.locations[0]?.city,
                              trial.protocolSection.contactsLocationsModule.locations[0]?.state,
                              trial.protocolSection.contactsLocationsModule.locations[0]?.country,
                            ]
                              .filter(Boolean)
                              .join(', ')}
                          </div>
                        )}
                      </div>
                      {trial.protocolSection?.conditionsModule?.conditions?.length ? (
                        <div className="text-sm">
                          <span className="font-medium">Conditions: </span>
                          <span className="text-muted-foreground">
                            {trial.protocolSection.conditionsModule.conditions.join(', ')}
                          </span>
                        </div>
                      ) : null}
                      {trial.protocolSection?.outcomesModule?.primaryOutcomes?.length ? (
                        <div className="text-sm">
                          <span className="font-medium">Primary Endpoints:</span>
                          <ul className="list-disc ml-5">
                            {trial.protocolSection.outcomesModule.primaryOutcomes.map(
                              (o, i) => (
                                <li key={i}>
                                  {o.measure}
                                  {o.description && (
                                    <span className="text-muted-foreground ml-1"> - {o.description}</span>
                                  )}
                                  {o.timeFrame && (
                                    <span className="text-muted-foreground ml-1"> ({o.timeFrame})</span>
                                  )}
                                </li>
                              )
                            )}
                          </ul>
                        </div>
                      ) : null}
                      {trial.protocolSection?.outcomesModule?.secondaryOutcomes?.length ? (
                        <div className="text-sm">
                          <span className="font-medium">Secondary Endpoints:</span>
                          <ul className="list-disc ml-5">
                            {trial.protocolSection.outcomesModule.secondaryOutcomes.map(
                              (o, i) => (
                                <li key={i}>
                                  {o.measure}
                                  {o.description && (
                                    <span className="text-muted-foreground ml-1"> - {o.description}</span>
                                  )}
                                  {o.timeFrame && (
                                    <span className="text-muted-foreground ml-1"> ({o.timeFrame})</span>
                                  )}
                                </li>
                              )
                            )}
                          </ul>
                        </div>
                      ) : null}
                      {trial.protocolSection?.referencesModule?.references?.length ? (
                        <div className="text-sm">
                          <span className="font-medium">Publications:</span>
                          <ul className="list-disc ml-5">
                            {trial.protocolSection.referencesModule.references.map((r, i) => (
                              <li key={i}>
                                {r.url ? (
                                  <a
                                    href={r.url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                                  >
                                    {r.citation || r.url}
                                  </a>
                                ) : (
                                  r.citation
                                )}
                              </li>
                            ))}
                          </ul>
                        </div>
                      ) : null}
                      {trial.protocolSection?.moreInfoModule?.links?.length ? (
                        <div className="text-sm">
                          <span className="font-medium">Links:</span>
                          <ul className="list-disc ml-5">
                            {trial.protocolSection.moreInfoModule.links.map((l, i) => (
                              <li key={i}>
                                <a
                                  href={l.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                                >
                                  {l.label || l.url}
                                </a>
                              </li>
                            ))}
                          </ul>
                        </div>
                      ) : null}
                      {id && (
                        <a
                          href={`https://clinicaltrials.gov/study/${id}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300 block"
                        >
                          View on ClinicalTrials.gov ({id})
                        </a>
                      )}
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

