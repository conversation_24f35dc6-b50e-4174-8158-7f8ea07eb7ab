
"use client";

import React, { useState, useRef, useCallback, useReducer, useEffect } from 'react';
import { PDFDocument } from 'pdf-lib';
import { Button } from "@/components/ui/button";
import { Upload, Search, X, Layout, Camera, Loader2 } from "lucide-react";
import { SubpageHeader } from "@/components/SubpageHeader";
import { Input } from "@/components/ui/input";
import PdfViewerConfig from "@/components/PdfViewerConfig";
import PdfViewer from "@/components/PdfViewer";
import { useToast } from "@/components/ui/use-toast";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import html2canvas from 'html2canvas';
import axios from 'axios';

// Add a VectorSearchResult interface at the top of the file
interface VectorSearchResult {
  filename: string;
  text_representation: string;
  page_number: number;
  bbox: number[];
  score: number;
  presignedUrl: string | null;
}

// Create an independent PDF viewer container component
const IndependentPdfViewer = React.memo(
  ({
    index,
    url,
    title,
    pageNumber,
    bbox,
    onUpload,
    onClear,
    viewerHeight,
    onSavePage,
  }: {
    index: number;
    url: string;
    title: string;
    pageNumber?: number;
    bbox?: number[];
    onUpload: () => void;
    onClear: () => void;
    viewerHeight: string;
    onSavePage: (page: number) => void;
  }) => {
    const [viewerKey, setViewerKey] = useState(0);
    const [modifiedUrl, setModifiedUrl] = useState<string>('');
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [pageInput, setPageInput] = useState<number>(pageNumber ?? 1);
    const [pageCount, setPageCount] = useState<number | null>(null);

    useEffect(() => {
      setPageInput(pageNumber ?? 1);
    }, [pageNumber]);

    useEffect(() => {
      const fetchPages = async () => {
        if (!url) {
          setPageCount(null);
          return;
        }
        try {
          const proxied = url.startsWith('/api/') ? url : `/api/drugs-docs?download=1&url=${encodeURIComponent(url)}`;
          const res = await fetch(proxied);
          if (!res.ok) throw new Error('fetch failed');
          const bytes = await res.arrayBuffer();
          const pdf = await PDFDocument.load(bytes);
          const count = pdf.getPageCount();
          setPageCount(count);
          if (pageInput > count) setPageInput(count);
        } catch (e) {
          console.error('Failed to load PDF', e);
          setPageCount(null);
        }
      };
      fetchPages();
    }, [url]);

    useEffect(() => {
      if (pageCount && pageInput > pageCount) {
        setPageInput(pageCount);
      }
    }, [pageCount]);

    useEffect(() => {
      setViewerKey((k) => k + 1);
    }, [url, pageNumber, bbox]);

    useEffect(() => {
      if (!url) {
        setModifiedUrl('');
        return;
      }

      setIsLoading(true);

      if (bbox && pageNumber) {
        try {
          const urlObj = new URL(url);
          const pathname = urlObj.pathname;
          const filename = pathname.split('/').pop() || '';
          const proxyUrl = `/api/pdf-proxy?filename=${encodeURIComponent(filename)}&page=${pageNumber}&bbox=${encodeURIComponent(
            JSON.stringify(bbox)
          )}&t=${Date.now()}`;
          setModifiedUrl(proxyUrl);
        } catch (error) {
          console.error('Error creating proxy URL:', error);
          setModifiedUrl(url);
        }
      } else {
        setModifiedUrl(url);
      }

      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 300);

      return () => clearTimeout(timer);
    }, [url, bbox, pageNumber]);

    return (
      <div className={`border rounded-lg p-4 flex flex-col w-full ${viewerHeight}`} style={{ height: viewerHeight }}>
        <div className="flex justify-between items-center mb-4">
          <h3 className="font-medium">Viewer {index + 1}</h3>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={onUpload}>
              <Upload className="h-4 w-4 mr-2" />
              Upload
            </Button>
            {url && (
              <Button variant="ghost" size="sm" onClick={onClear}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>

        {title && (
          <p className="text-sm mb-2 truncate" title={title}>
            {title}
          </p>
        )}

        <div
          id={`pdf-viewer-${index}`}
          className="flex-1 bg-gray-100 dark:bg-gray-800 rounded-md overflow-hidden"
          style={{ minHeight: "0", flex: "1 1 auto" }}
        >
          {url ? (
            isLoading ? (
              <div className="h-full flex items-center justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-500"></div>
              </div>
            ) : (
              <iframe
                key={viewerKey}
                src={`/pdf-frame?url=${encodeURIComponent(modifiedUrl)}&page=${pageNumber ?? 1}&title=${encodeURIComponent(title)}`}
                className="w-full h-full"
                style={{ display: "block" }}
              />
            )
          ) : (
            <div className="h-full flex items-center justify-center text-gray-500">
              <p>No PDF selected</p>
            </div>
          )}
        </div>
        <div className="flex items-center gap-2 mt-2">
          <Input
            type="number"
            min={1}
            max={pageCount ?? undefined}
            value={pageInput}
            onChange={(e) => {
              const val = parseInt(e.target.value) || 1;
              const clamped = pageCount ? Math.min(val, pageCount) : val;
              setPageInput(clamped);
            }}
            className="w-20"
          />
          <Button
            variant="outline"
            size="sm"
            onClick={() => onSavePage(pageInput)}
            disabled={!url}
          >
            Save Page
          </Button>
        </div>
      </div>
    );
  },
  (prevProps, nextProps) => {
    return (
      prevProps.url === nextProps.url &&
      prevProps.title === nextProps.title &&
      prevProps.pageNumber === nextProps.pageNumber &&
      prevProps.bbox === nextProps.bbox &&
      prevProps.viewerHeight === nextProps.viewerHeight
    );
  }
);

// Add display name for better debugging
IndependentPdfViewer.displayName = 'IndependentPdfViewer';

interface ViewerState {
  url: string;
  title: string;
  pageNumber: number;
  bbox?: number[];
}

type ViewerAction =
  | { type: 'set'; index: number; payload: Partial<ViewerState> }
  | { type: 'clear'; index: number };

const initialViewers: ViewerState[] = Array.from({ length: 9 }).map(() => ({
  url: '',
  title: '',
  pageNumber: 1,
  bbox: undefined,
}));

function viewerReducer(state: ViewerState[], action: ViewerAction): ViewerState[] {
  switch (action.type) {
    case 'set':
      return state.map((v, i) =>
        i === action.index ? { ...v, ...action.payload } : v
      );
    case 'clear':
      return state.map((v, i) =>
        i === action.index ? { url: '', title: '', pageNumber: 1, bbox: undefined } : v
      );
    default:
      return state;
  }
}

export default function HTAReportsPage() {
  const { toast } = useToast();
  const [searchInputValue, setSearchInputValue] = useState('');
  const [viewerCount, setViewerCount] = useState(2);
  const [viewers, dispatch] = useReducer(viewerReducer, initialViewers);
  const [isSearching, setIsSearching] = useState(false);
  const [vectorResults, setVectorResults] = useState<VectorSearchResult[]>([]);
  const [savedPages, setSavedPages] = useState<{ id: string; url: string; page: number; title: string }[]>([]);
  const [draggedPageIndex, setDraggedPageIndex] = useState<number | null>(null);
  const fileInputRefs = Array(9).fill(0).map(() => useRef<HTMLInputElement>(null));
  
  // Add these functions to manage saved pages
  const resetSavedPages = () => {
    setSavedPages([]);
    toast({
      title: "Pages reset",
      description: "All saved pages have been cleared",
    });
  };

  const deleteSavedPage = (id: string) => {
    setSavedPages(prev => prev.filter(page => page.id !== id));
    toast({
      title: "Page removed",
      description: "The selected page has been removed",
    });
  };

  const handleDragStart = (index: number) => {
    setDraggedPageIndex(index);
  };

  const handleDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault();
    if (draggedPageIndex === null || draggedPageIndex === index) return;
    
    const newPages = [...savedPages];
    const draggedPage = newPages[draggedPageIndex];
    newPages.splice(draggedPageIndex, 1);
    newPages.splice(index, 0, draggedPage);
    
    setSavedPages(newPages);
    setDraggedPageIndex(index);
  };
  
  // Add this function to load a single result into a specific viewer
  const loadResultIntoViewer = (result: VectorSearchResult, viewerIndex: number) => {
    const { presignedUrl } = result;
    if (!presignedUrl) {
      toast({
        title: "Error loading PDF",
        description: "Could not generate a URL for this document",
        variant: "destructive",
      });
      return;
    }
    // Clear the existing viewer first to avoid shared worker issues
    dispatch({ type: 'clear', index: viewerIndex });

    setTimeout(() => {
      dispatch({
        type: 'set',
        index: viewerIndex,
        payload: {
          url: presignedUrl,
          title: result.filename.replace(/_/g, ' '),
          pageNumber: result.page_number || 1,
          bbox: result.bbox,
        },
      });
    }, 100);
  };
  
  // Calculate grid columns based on viewer count
  const getGridColumns = () => {
    if (viewerCount === 1) return 'grid-cols-1';
    if (viewerCount === 2) return 'grid-cols-1 md:grid-cols-2';
    if (viewerCount <= 4) return 'grid-cols-1 sm:grid-cols-2';
    if (viewerCount <= 6) return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3';
    return 'grid-cols-1 sm:grid-cols-3'; // Force 3 columns for 9 viewers
  };
  
  // Calculate viewer height based on viewer count
  const getViewerHeight = () => {
    // Use fixed heights for all viewers
    if (viewerCount === 1) return 'h-[800px]';
    if (viewerCount === 2) return 'h-[700px]';
    if (viewerCount <= 4) return 'h-[600px]';
    if (viewerCount <= 9) return 'h-[500px]';
    return 'h-[450px]';
  };
  
  // Function to update a single PDF viewer
  const updatePdfViewer = (
    index: number,
    url: string,
    title: string,
    pageNumber: number = 1,
    bbox?: number[]
  ) => {
    dispatch({
      type: 'set',
      index,
      payload: { url, title, pageNumber, bbox },
    });
  };
  
  // Function to clear a single PDF viewer
  const clearPdf = (index: number) => {
    updatePdfViewer(index, '', '', 1, undefined);
  };
  
  // Handle file upload for a specific viewer
  const handleFileUpload = (index: number, e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const fileUrl = URL.createObjectURL(file);
    updatePdfViewer(index, fileUrl, file.name, 1, undefined);

    toast({
      title: "PDF uploaded",
      description: `${file.name} has been loaded into viewer ${index + 1}`,
    });
  };
  
  // Handle search form submission
  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchInputValue.trim()) {
      toast({
        title: "Search query required",
        description: "Please enter a treatment name, indication, or disease state",
        variant: "destructive",
      });
      return;
    }

    setIsSearching(true);
    
    try {
      // Call the API to perform vector search with limit=20
      const response = await axios.get(`/api/search-hta-reports?query=${encodeURIComponent(searchInputValue)}&limit=20`);
      
      console.log('Search API response:', response.data);
      
      if (response.data.results && response.data.results.length > 0) {
        // Log each result to verify page_number and bbox
        response.data.results.forEach((result: VectorSearchResult, index: number) => {
          console.log(`Result ${index + 1}:`, {
            filename: result.filename,
            page_number: result.page_number,
            bbox: result.bbox,
            hasPresignedUrl: !!result.presignedUrl
          });
        });
        
        // Store all results (up to 20)
        setVectorResults(response.data.results);
        
        // Load only the top results into the PDF viewers (up to viewerCount)
        loadResultsIntoPdfViewers(response.data.results.slice(0, viewerCount));
        
        toast({
          title: "Search complete",
          description: `Found ${response.data.results.length} relevant sections`,
        });
      } else {
        toast({
          title: "No results found",
          description: "Try a different search query",
          variant: "destructive",
        });
        setVectorResults([]);
      }
    } catch (error) {
      console.error('Vector search error:', error);
      toast({
        title: "Search failed",
        description: "An error occurred while searching. Please try again.",
        variant: "destructive",
      });
      setVectorResults([]);
    } finally {
      setIsSearching(false);
    }
  };
  
  // Function to load results into PDF viewers
  const loadResultsIntoPdfViewers = (results: VectorSearchResult[]) => {
    // Clear existing viewers first
    for (let i = 0; i < viewerCount; i++) {
      dispatch({ type: 'clear', index: i });
    }

    setTimeout(() => {
    for (let i = 0; i < Math.min(results.length, viewerCount); i++) {
      const result = results[i];
      const { presignedUrl } = result;

      console.log(`Loading result ${i} into viewer:`, {
        filename: result.filename,
        page_number: result.page_number,
        bbox: result.bbox
      });

      if (presignedUrl) {
        dispatch({
          type: 'set',
          index: i,
          payload: {
            url: presignedUrl,
            title: result.filename.replace(/_/g, ' '),
            pageNumber: result.page_number || 1,
            bbox: result.bbox,
          },
        });
      }
    }
    }, 100);
  };
  
  // Take screenshot of a specific viewer
  const takeScreenshot = useCallback(async (index: number) => {
    if (!viewers[index].url) {
      toast({
        title: "No PDF to capture",
        description: "Please select or upload a PDF first",
        variant: "destructive",
      });
      return;
    }

    try {
      toast({
        title: "Capturing screenshot...",
        description: "Please wait while we process your screenshot",
      });

      const viewerElement = document.getElementById(`pdf-viewer-${index}`);
      if (!viewerElement) {
        toast({
          title: "Screenshot failed",
          description: "Could not find the PDF viewer element",
          variant: "destructive",
        });
        return;
      }

      const canvas = await html2canvas(viewerElement, {
        allowTaint: true,
        useCORS: true,
        logging: false,
        scale: 2,
      });

      canvas.toBlob((blob) => {
        if (!blob) {
          toast({
            title: "Screenshot failed",
            description: "Unable to capture screenshot",
            variant: "destructive",
          });
          return;
        }

        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `screenshot-viewer-${index + 1}-${Date.now()}.png`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        toast({
          title: "Screenshot captured",
          description: "Your screenshot has been downloaded",
        });
      }, 'image/png');
    } catch (error) {
      console.error('Screenshot error:', error);
      toast({
        title: "Screenshot failed",
        description: "An error occurred while capturing the screenshot",
        variant: "destructive",
      });
    }
  }, [viewers, toast]);

  const savePageFromViewer = (index: number, page: number) => {
    const viewer = viewers[index];
    if (!viewer.url) {
      toast({
        title: "No PDF loaded",
        description: "Please load a PDF first",
        variant: "destructive",
      });
      return;
    }
    setSavedPages((prev) => [...prev, { 
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      url: viewer.url, 
      page, 
      title: viewer.title 
    }]);
    toast({
      title: "Page saved",
      description: `Saved page ${page} from viewer ${index + 1}`,
    });
  };

  const downloadCombinedPdf = async () => {
    if (savedPages.length === 0) return;
    try {
      const res = await fetch('/api/combine-pdf-pages', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ selections: savedPages }),
      });
      if (!res.ok) {
        toast({ title: 'Download failed', description: 'Server error', variant: 'destructive' });
        return;
      }
      const blob = await res.blob();
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'combined.pdf';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      toast({ title: 'PDF downloaded', description: 'Combined PDF downloaded' });
    } catch (error) {
      console.error('Combine download error:', error);
      toast({ title: 'Download failed', description: 'An error occurred', variant: 'destructive' });
    }
  };
  
  // Render the vector search results
  const renderVectorResults = () => {
    if (vectorResults.length === 0) return null;
    
    return (
      <div className="mb-8">
        <h2 className="text-xl font-bold mb-4">Search Results ({vectorResults.length})</h2>
        <div className="space-y-4">
          {vectorResults.map((result, index) => (
            <div key={index} className="border rounded-lg p-4">
              <h3 className="font-medium mb-2">
                {result.filename.replace(/_/g, ' ')} 
                <span className="ml-2 px-2 py-1 bg-amber-100 dark:bg-amber-900 rounded text-sm">
                  Page {result.page_number || 'Unknown'}
                </span>
              </h3>
              <p className="mb-3 text-sm">{result.text_representation}</p>
              <div className="flex flex-wrap gap-2">
                {Array.from({ length: viewerCount }).map((_, viewerIndex) => (
                  <Button
                    key={viewerIndex}
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      if (result.presignedUrl) {
                        loadResultIntoViewer(result, viewerIndex);
                      } else {
                        toast({
                          title: "Error loading PDF",
                          description: "Could not generate a URL for this document",
                          variant: "destructive",
                        });
                      }
                    }}
                  >
                    Load in Viewer {viewerIndex + 1}
                  </Button>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };
  
  // Render the PDF viewers in rows to ensure consistent height
  const renderPdfViewers = () => {
    const viewerHeight = getViewerHeight();
    
    // Determine how many viewers per row based on the grid layout
    let viewersPerRow = 1;
    if (viewerCount === 2) viewersPerRow = 2;
    else if (viewerCount <= 4) viewersPerRow = 2;
    else if (viewerCount <= 9) viewersPerRow = 3;
    
    // Create rows of viewers
    const rows = [];
    for (let i = 0; i < viewerCount; i += viewersPerRow) {
      const rowViewers = [];
      for (let j = 0; j < viewersPerRow && i + j < viewerCount; j++) {
        const index = i + j;
        rowViewers.push(
          <div key={`viewer-container-${index}`} className="flex-1 min-w-0">
            <IndependentPdfViewer
              key={`pdf-viewer-${index}-${viewers[index].url || 'empty'}`}
              index={index}
              url={viewers[index].url}
              title={viewers[index].title}
              pageNumber={viewers[index].pageNumber}
              bbox={viewers[index].bbox}
              onUpload={() => fileInputRefs[index].current?.click()}
              onClear={() => clearPdf(index)}
              viewerHeight={viewerHeight}
              onSavePage={(page) => savePageFromViewer(index, page)}
            />
          </div>
        );
      }
      
      rows.push(
        <div key={`row-${i}`} className="flex flex-col md:flex-row gap-4 mb-4 w-full">
          {rowViewers}
        </div>
      );
    }
    
    return rows;
  };

  return (
    <PdfViewerConfig>
      <div className="container mx-auto py-8 px-4">
        <SubpageHeader current="hta-reports" />
      
        <h1 className="text-3xl font-bold mb-6">HTA Reports Comparison</h1>
      
        <div className="prose max-w-none mb-8 dark:text-gray-200">
          <p>
            Compare Health Technology Assessment (HTA) reports side by side from different agencies.
            Search for reports by treatment name, indication, or disease state.
          </p>
        </div>

        {/* Combined Layout Controls and Search Form */}
        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          <div className="flex items-center gap-2 sm:w-auto">
            <Layout className="h-5 w-5 text-gray-500" />
            <Select 
              value={viewerCount.toString()} 
              onValueChange={(value) => setViewerCount(parseInt(value))}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Number of viewers" />
              </SelectTrigger>
              <SelectContent>
                {[1, 2, 3, 4, 6, 9].map((num) => (
                  <SelectItem key={num} value={num.toString()}>
                    {num} {num === 1 ? 'Viewer' : 'Viewers'}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <form onSubmit={handleSearch} className="flex-1 flex gap-2">
            <Input
              type="search"
              placeholder="Search by treatment, indication, or disease..."
              value={searchInputValue}
              onChange={(e) => setSearchInputValue(e.target.value)}
              className="flex-1"
            />
            <Button
              type="submit"
              className="bg-amber-400 text-black hover:bg-amber-500"
              disabled={isSearching}
            >
              {isSearching ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Search className="h-4 w-4 mr-2" />
              )}
              Search
            </Button>
          </form>
        </div>

        {/* PDF Viewers */}
        <div className="w-full mb-8">
          {renderPdfViewers()}
        </div>

        {savedPages.length > 0 && (
          <div className="mb-8 border rounded-lg p-4">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold">Saved Pages ({savedPages.length})</h2>
              <Button
                variant="outline"
                size="sm"
                onClick={resetSavedPages}
                className="text-amber-700 border-amber-300 hover:bg-amber-100 hover:text-amber-800 dark:text-amber-300 dark:border-amber-700 dark:hover:bg-amber-900/20"
              >
                Reset All Pages
              </Button>
            </div>
            
            <div className="space-y-2 mb-4">
              {savedPages.map((page, index) => (
                <div 
                  key={page.id} 
                  className="flex items-center justify-between p-2 bg-muted/30 rounded-md"
                  draggable
                  onDragStart={() => handleDragStart(index)}
                  onDragOver={(e) => handleDragOver(e, index)}
                >
                  <div className="flex items-center gap-2">
                    <div className="cursor-move p-1 hover:bg-muted/50 rounded">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <circle cx="9" cy="5" r="1" />
                        <circle cx="9" cy="12" r="1" />
                        <circle cx="9" cy="19" r="1" />
                        <circle cx="15" cy="5" r="1" />
                        <circle cx="15" cy="12" r="1" />
                        <circle cx="15" cy="19" r="1" />
                      </svg>
                    </div>
                    <span>{index + 1}. {page.title || 'Document'} - Page {page.page}</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => deleteSavedPage(page.id)}
                    className="h-8 w-8 p-0 text-amber-700 hover:text-amber-800 hover:bg-amber-100 dark:text-amber-300 dark:hover:bg-amber-900/20"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
            
            <Button onClick={downloadCombinedPdf} className="bg-amber-400 text-black hover:bg-amber-500">
              Download Combined PDF
            </Button>
          </div>
        )}

        {/* Hidden file inputs */}
        {fileInputRefs.map((ref, index) => (
          <input
            key={`file-input-${index}`}
            type="file"
            accept="application/pdf"
            onChange={(e) => handleFileUpload(index, e)}
            className="hidden"
            ref={ref}
          />
        ))}

        {/* Search Results */}
        {renderVectorResults()}
      </div>
    </PdfViewerConfig>
  );
}
