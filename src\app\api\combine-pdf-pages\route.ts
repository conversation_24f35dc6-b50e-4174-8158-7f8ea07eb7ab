import { NextRequest, NextResponse } from 'next/server';
import { PDFDocument } from 'pdf-lib';

export async function POST(req: NextRequest) {
  try {
    const { selections } = await req.json() as { selections: { url: string; page: number }[] };
    if (!Array.isArray(selections) || selections.length === 0) {
      return NextResponse.json({ error: 'No pages provided' }, { status: 400 });
    }

    const mergedPdf = await PDFDocument.create();

    for (const sel of selections) {
      try {
        const res = await fetch(sel.url);
        if (!res.ok) {
          console.error('Failed to fetch', sel.url, res.status);
          continue;
        }
        const bytes = await res.arrayBuffer();
        const srcDoc = await PDFDocument.load(bytes);
        const pageIndex = Math.min(Math.max(0, (sel.page || 1) - 1), srcDoc.getPageCount() - 1);
        const [copied] = await mergedPdf.copyPages(srcDoc, [pageIndex]);
        mergedPdf.addPage(copied);
      } catch (err) {
        console.error('Error processing selection', sel, err);
      }
    }

    const pdfBytes = await mergedPdf.save();

    return new NextResponse(pdfBytes, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'attachment; filename="combined.pdf"'
      }
    });
  } catch (error) {
    console.error('Combine pdf error:', error);
    return NextResponse.json({ error: 'Failed to combine pages' }, { status: 500 });
  }
}
