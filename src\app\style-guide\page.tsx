"use client";

import React from 'react';
import { SubpageHeader } from '@/components/SubpageHeader';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ThemeToggle } from '@/components/ThemeToggle';
import {
  Save,
  Search,
  Moon,
  Sun,
  X,
  Download,
  FileText,
  ExternalLink,
  Loader2,
  Bookmark,
  BookmarkCheck,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from 'lucide-react';

export default function StyleGuidePage() {
  const [selected, setSelected] = React.useState<Record<string, boolean>>({});
  const [showSelectedOnly, setShowSelectedOnly] = React.useState(false);

  const Option = ({ id, children }: { id: string; children: React.ReactNode }) => {
    const isChecked = selected[id] || false;
    if (showSelectedOnly && !isChecked) {
      return null;
    }
    return (
      <div className="space-y-1 text-center">
        <Checkbox
          checked={isChecked}
          onCheckedChange={(c) =>
            setSelected((prev) => ({ ...prev, [id]: c === true }))
          }
        />
        {children}
      </div>
    );
  };

  return (
    <div className="container mx-auto py-8 px-4 space-y-10">
      <SubpageHeader current="style-guide" />
      <h1 className="text-3xl font-bold mb-2">Style Guide</h1>
      <div className="mb-6 flex items-center gap-2">
        <Checkbox
          checked={showSelectedOnly}
          onCheckedChange={(c) => setShowSelectedOnly(c === true)}
        />
        <span className="text-sm">Show selected only</span>
      </div>

      {/* Save Buttons */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Save Button</h2>
        <div className="flex flex-wrap gap-6">
          {/* original examples */}
          <Option id="save-1">
            <Button size="icon">
              <Save className="h-4 w-4" />
            </Button>
            <p className="text-sm text-muted-foreground">Icon only for tight spaces</p>
          </Option>
          <Option id="save-2">
            <Button>
              <Save className="h-4 w-4" />
              <span className="sr-only">Save</span>
            </Button>
            <p className="text-sm text-muted-foreground">Screen-reader text only</p>
          </Option>
          <Option id="save-3">
            <Button className="gap-1">
              <Save className="h-4 w-4" />
              Save
            </Button>
            <p className="text-sm text-muted-foreground">Compact gap</p>
          </Option>
          <Option id="save-4">
            <Button className="gap-3">
              <Save className="h-4 w-4" />
              Save
            </Button>
            <p className="text-sm text-muted-foreground">Spacious gap</p>
          </Option>
          {/* additional examples */}
          <Option id="save-5">
            <Button variant="outline">
              <Save className="h-4 w-4" />
              Save
            </Button>
            <p className="text-sm text-muted-foreground">Outline style for secondary action</p>
          </Option>
          <Option id="save-6">
            <Button variant="secondary">
              <Save className="h-4 w-4" />
              Save
            </Button>
            <p className="text-sm text-muted-foreground">Secondary color to blend with UI</p>
          </Option>
          <Option id="save-7">
            <Button className="flex-row-reverse gap-2">
              <Save className="h-4 w-4" />
              Save
            </Button>
            <p className="text-sm text-muted-foreground">Icon on the right</p>
          </Option>
          <Option id="save-8">
            <Button size="lg">
              <Save className="h-5 w-5" />
              Save
            </Button>
            <p className="text-sm text-muted-foreground">Large button for emphasis</p>
          </Option>
          <Option id="save-9">
            <Button variant="ghost">
              <Save className="h-4 w-4" />
              Save Draft
            </Button>
            <p className="text-sm text-muted-foreground">Ghost variant for minimal distraction</p>
          </Option>
        </div>
      </section>

      {/* Search Buttons */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Search Button</h2>
        <div className="flex flex-wrap gap-6">
          {/* original examples */}
          <Option id="search-1">
            <Button size="icon">
              <Search className="h-4 w-4" />
            </Button>
            <p className="text-sm text-muted-foreground">Icon only</p>
          </Option>
          <Option id="search-2">
            <Button>
              <Search className="h-4 w-4" />
              <span className="sr-only">Search</span>
            </Button>
            <p className="text-sm text-muted-foreground">Screen-reader text</p>
          </Option>
          <Option id="search-3">
            <Button className="gap-1">
              <Search className="h-4 w-4" />
              Search
            </Button>
            <p className="text-sm text-muted-foreground">Tight gap</p>
          </Option>
          <Option id="search-4">
            <Button className="gap-3">
              <Search className="h-4 w-4" />
              Search
            </Button>
            <p className="text-sm text-muted-foreground">Wide gap</p>
          </Option>
          {/* additional examples */}
          <Option id="search-5">
            <Button variant="outline">
              <Search className="h-4 w-4" />
              Search
            </Button>
            <p className="text-sm text-muted-foreground">Outline variant</p>
          </Option>
          <Option id="search-6">
            <Button variant="secondary">
              <Search className="h-4 w-4" />
              Search
            </Button>
            <p className="text-sm text-muted-foreground">Secondary style</p>
          </Option>
          <Option id="search-7">
            <Button className="flex-row-reverse gap-2">
              <Search className="h-4 w-4" />
              Search
            </Button>
            <p className="text-sm text-muted-foreground">Icon after text</p>
          </Option>
          <Option id="search-8">
            <Button size="lg">
              <Search className="h-5 w-5" />
              Search
            </Button>
            <p className="text-sm text-muted-foreground">Large button</p>
          </Option>
          <Option id="search-9">
            <Button variant="ghost">
              <Search className="h-4 w-4" />
              Quick Search
            </Button>
            <p className="text-sm text-muted-foreground">Ghost variant</p>
          </Option>
        </div>
      </section>

      {/* Light / Dark Mode */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Light / Dark Mode</h2>
          <div className="flex flex-wrap gap-6 mb-4">
            <Option id="theme-1">
              <ThemeToggle />
              <p className="text-sm text-muted-foreground">Default toggle</p>
            </Option>
            <Option id="theme-2">
              <Button variant="ghost" size="icon" className="hover:text-amber-500">
                <Moon className="h-4 w-4" />
              </Button>
              <p className="text-sm text-muted-foreground">Moon icon hover highlight</p>
            </Option>
            <Option id="theme-3">
              <Button variant="ghost" size="icon" className="hover:text-amber-500">
                <Sun className="h-4 w-4" />
              </Button>
              <p className="text-sm text-muted-foreground">Sun icon hover highlight</p>
            </Option>
            <Option id="theme-4">
              <Button variant="outline" size="sm">
                <Sun className="h-4 w-4 mr-2" />Light Mode
              </Button>
              <p className="text-sm text-muted-foreground">Outline button with text</p>
            </Option>
            <Option id="theme-5">
              <Button variant="outline" size="sm">
                <Moon className="h-4 w-4 mr-2" />Dark Mode
              </Button>
              <p className="text-sm text-muted-foreground">Outline button for dark mode</p>
            </Option>
            <Option id="theme-6">
              <Button variant="secondary" size="icon">
                <Sun className="h-4 w-4" />
              </Button>
              <p className="text-sm text-muted-foreground">Secondary icon style</p>
            </Option>
            <Option id="theme-7">
              <Button variant="secondary" size="icon">
                <Moon className="h-4 w-4" />
              </Button>
              <p className="text-sm text-muted-foreground">Secondary dark icon</p>
            </Option>
          </div>
        <Option id="theme-8">
          <div className="flex flex-wrap gap-2">
            <div className="w-10 h-10 rounded bg-primary" />
            <div className="w-10 h-10 rounded bg-secondary" />
            <div className="w-10 h-10 rounded bg-accent" />
            <div className="w-10 h-10 rounded bg-muted" />
            <div className="w-10 h-10 rounded bg-destructive" />
            <div className="w-10 h-10 rounded bg-foreground" />
            <div className="w-10 h-10 rounded bg-background" />
          </div>
        </Option>
      </section>

      {/* Color Palettes */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Color Palettes</h2>
        <div className="flex flex-col gap-6">
          <Option id="palette-1">
            <div className="flex gap-2">
              <div className="w-10 h-10 rounded" style={{ backgroundColor: '#FF4500' }} />
              <div className="w-10 h-10 rounded" style={{ backgroundColor: '#FF7E00' }} />
              <div className="w-10 h-10 rounded" style={{ backgroundColor: '#FFBA08' }} />
              <div className="w-10 h-10 rounded" style={{ backgroundColor: '#F72585' }} />
              <div className="w-10 h-10 rounded" style={{ backgroundColor: '#7209B7' }} />
            </div>
            <p className="text-sm text-muted-foreground">
              Warm sunset tones to convey energy and excitement
            </p>
          </Option>
          <Option id="palette-2">
            <div className="flex gap-2">
              <div className="w-10 h-10 rounded" style={{ backgroundColor: '#2B5D34' }} />
              <div className="w-10 h-10 rounded" style={{ backgroundColor: '#588157' }} />
              <div className="w-10 h-10 rounded" style={{ backgroundColor: '#A3B18A' }} />
              <div className="w-10 h-10 rounded" style={{ backgroundColor: '#DAD7CD' }} />
              <div className="w-10 h-10 rounded" style={{ backgroundColor: '#885A2D' }} />
            </div>
            <p className="text-sm text-muted-foreground">
              Forest greens and earth tones for an organic feel
            </p>
          </Option>
          <Option id="palette-3">
            <div className="flex gap-2">
              <div className="w-10 h-10 rounded" style={{ backgroundColor: '#03045E' }} />
              <div className="w-10 h-10 rounded" style={{ backgroundColor: '#0077B6' }} />
              <div className="w-10 h-10 rounded" style={{ backgroundColor: '#00B4D8' }} />
              <div className="w-10 h-10 rounded" style={{ backgroundColor: '#90E0EF' }} />
              <div className="w-10 h-10 rounded" style={{ backgroundColor: '#CAF0F8' }} />
            </div>
            <p className="text-sm text-muted-foreground">
              Ocean blues that evoke calm and reliability
            </p>
          </Option>
          <Option id="palette-4">
            <div className="flex gap-2">
              <div className="w-10 h-10 rounded" style={{ backgroundColor: '#111111' }} />
              <div className="w-10 h-10 rounded" style={{ backgroundColor: '#333333' }} />
              <div className="w-10 h-10 rounded" style={{ backgroundColor: '#555555' }} />
              <div className="w-10 h-10 rounded" style={{ backgroundColor: '#AAAAAA' }} />
              <div className="w-10 h-10 rounded" style={{ backgroundColor: '#EEEEEE' }} />
            </div>
            <p className="text-sm text-muted-foreground">
              Monochrome grayscale for a clean, minimal look
            </p>
          </Option>
          <Option id="palette-5">
            <div className="flex gap-2">
              <div className="w-10 h-10 rounded" style={{ backgroundColor: '#FFD6A5' }} />
              <div className="w-10 h-10 rounded" style={{ backgroundColor: '#FDFFB6' }} />
              <div className="w-10 h-10 rounded" style={{ backgroundColor: '#CAFFBF' }} />
              <div className="w-10 h-10 rounded" style={{ backgroundColor: '#9BF6FF' }} />
              <div className="w-10 h-10 rounded" style={{ backgroundColor: '#A0C4FF' }} />
            </div>
            <p className="text-sm text-muted-foreground">
              Soft pastel hues ideal for light-hearted designs
            </p>
          </Option>
        </div>
      </section>

      {/* Pagination */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Pagination</h2>
          <div className="flex flex-wrap gap-4 items-center">
          {/* original */}
          <Option id="page-1">
            <div className="flex items-center gap-1">
              <Button variant="outline" size="sm">
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm">
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button variant="default" size="sm">1</Button>
              <Button variant="outline" size="sm">2</Button>
              <Button variant="outline" size="sm">3</Button>
              <Button variant="outline" size="sm">
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm">
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-sm text-muted-foreground">Standard pagination</p>
          </Option>
          {/* additional examples */}
          <Option id="page-2">
            <div className="flex items-center gap-1">
              <Button variant="outline" size="sm">Prev</Button>
              <Button variant="outline" size="sm">Next</Button>
            </div>
            <p className="text-sm text-muted-foreground">Minimal with words</p>
          </Option>
          <Option id="page-3">
            <div className="flex items-center gap-1">
              <Button variant="ghost" size="icon">
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <span className="px-2">1 of 10</span>
              <Button variant="ghost" size="icon">
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-sm text-muted-foreground">Simple label format</p>
          </Option>
          <Option id="page-4">
            <div className="flex items-center gap-1">
              <Button variant="secondary" size="sm">1</Button>
              <Button variant="secondary" size="sm">2</Button>
              <Button variant="secondary" size="sm">3</Button>
            </div>
            <p className="text-sm text-muted-foreground">Secondary color scheme</p>
          </Option>
          <Option id="page-5">
            <div className="flex items-center gap-1">
              <Button variant="outline" size="sm">First</Button>
              <Button variant="outline" size="sm">Previous</Button>
              <Button variant="outline" size="sm">Next</Button>
              <Button variant="outline" size="sm">Last</Button>
            </div>
            <p className="text-sm text-muted-foreground">Text-only small buttons</p>
          </Option>
          <Option id="page-6">
            <div className="flex items-center gap-1">
              <Button variant="outline" size="sm">1</Button>
              <span className="px-1">...</span>
              <Button variant="outline" size="sm">10</Button>
            </div>
            <p className="text-sm text-muted-foreground">Truncated for long lists</p>
          </Option>
          </div>
      </section>

      {/* Icons */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Icons</h2>
        <div className="grid grid-cols-3 sm:grid-cols-5 gap-6">
          <Option id="icon-1">
            <X className="h-6 w-6" />
            <p className="text-sm text-muted-foreground">Close icon</p>
          </Option>
          <Option id="icon-2">
            <Download className="h-6 w-6" />
            <p className="text-sm text-muted-foreground">Download</p>
          </Option>
          <Option id="icon-3">
            <FileText className="h-6 w-6" />
            <p className="text-sm text-muted-foreground">View PDF</p>
          </Option>
          <Option id="icon-4">
            <ExternalLink className="h-6 w-6" />
            <p className="text-sm text-muted-foreground">External link</p>
          </Option>
          <Option id="icon-5">
            <Loader2 className="h-6 w-6 animate-spin" />
            <p className="text-sm text-muted-foreground">Loading spinner</p>
          </Option>
          <Option id="icon-6">
            <Bookmark className="h-6 w-6" />
            <p className="text-sm text-muted-foreground">Favorite (outline)</p>
          </Option>
          <Option id="icon-7">
            <BookmarkCheck className="h-6 w-6 text-amber-500" />
            <p className="text-sm text-muted-foreground">Favorite (filled)</p>
          </Option>
          {/* additional icon examples */}
          <Option id="icon-8">
            <X className="h-8 w-8 text-destructive" />
            <p className="text-sm text-muted-foreground">Large destructive</p>
          </Option>
          <Option id="icon-9">
            <Download className="h-8 w-8 text-primary" />
            <p className="text-sm text-muted-foreground">Large brand color</p>
          </Option>
          <Option id="icon-10">
            <FileText className="h-8 w-8 text-secondary" />
            <p className="text-sm text-muted-foreground">Large secondary</p>
          </Option>
          <Option id="icon-11">
            <ExternalLink className="h-8 w-8 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">Muted tone</p>
          </Option>
          <Option id="icon-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="text-sm text-muted-foreground">Large spinner</p>
          </Option>
        </div>
      </section>

      {/* Typography */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Typography</h2>
        <div className="space-y-2">
          <Option id="type-1">
            <p className="text-sm">Small text</p>
          </Option>
          <Option id="type-2">
            <p className="text-base">Base text</p>
          </Option>
          <Option id="type-3">
            <p className="text-lg font-semibold">Large text bold</p>
          </Option>
          <Option id="type-4">
            <p className="italic">Italic style</p>
          </Option>
          <Option id="type-5">
            <p className="text-xl">Extra large</p>
          </Option>
          <Option id="type-6">
            <p className="text-2xl font-semibold">Heading style</p>
          </Option>
          <Option id="type-7">
            <p className="text-muted-foreground">Muted color</p>
          </Option>
          <Option id="type-8">
            <p className="uppercase tracking-wide text-sm">Uppercase spaced</p>
          </Option>
          <Option id="type-9">
            <p className="font-mono">Monospace</p>
          </Option>
        </div>
      </section>

      {/* Load More Button and Dropdown */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Load More &amp; Results</h2>
        <div className="flex flex-wrap gap-6 items-center">
          <Option id="load-1">
            <Button>Load More</Button>
            <p className="text-sm text-muted-foreground">Default style</p>
          </Option>
          <Option id="load-2">
            <Button variant="outline">Load More</Button>
            <p className="text-sm text-muted-foreground">Outline variant</p>
          </Option>
          <Option id="load-3">
            <Button variant="secondary">Load More</Button>
            <p className="text-sm text-muted-foreground">Secondary style</p>
          </Option>
          <Option id="load-4">
            <Button size="sm">Load More</Button>
            <p className="text-sm text-muted-foreground">Small size</p>
          </Option>
          <Option id="load-5">
            <Button size="lg">Load More</Button>
            <p className="text-sm text-muted-foreground">Large emphasis</p>
          </Option>
        </div>
        <div className="flex flex-wrap gap-4 items-center">
          <Option id="results-select">
            <Select>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="20 results" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="20">20 results</SelectItem>
                <SelectItem value="40">40 results</SelectItem>
                <SelectItem value="60">60 results</SelectItem>
                <SelectItem value="80">80 results</SelectItem>
                <SelectItem value="100">100 results</SelectItem>
              </SelectContent>
            </Select>
          </Option>
        </div>
      </section>

      {/* Other Controls */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Other Controls</h2>
        <div className="flex flex-wrap gap-6 items-center">
          <Option id="misc-1">
            <Checkbox />
            <p className="text-sm text-muted-foreground">Example checkbox</p>
          </Option>
        </div>
      </section>
    </div>
  );
}
