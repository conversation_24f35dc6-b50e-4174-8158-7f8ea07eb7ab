from pymongo import MongoClient

def simple_copy_collection(source_uri, source_db, source_col, 
                          target_uri, target_db, target_col):
    """
    Simple function to copy all documents from one collection to another.
    
    Args:
        source_uri (str): MongoDB connection string for source
        source_db (str): Source database name
        source_col (str): Source collection name
        target_uri (str): MongoDB connection string for target
        target_db (str): Target database name
        target_col (str): Target collection name
    """
    
    # Connect to databases
    source_client = MongoClient(source_uri)
    target_client = MongoClient(target_uri)
    
    # Get collections
    source_collection = source_client[source_db][source_col]
    target_collection = target_client[target_db][target_col]
    
    # Copy documents
    documents = list(source_collection.find())
    
    if documents:
        result = target_collection.insert_many(documents)
        print(f"Copied {len(result.inserted_ids)} documents")
    else:
        print("No documents found to copy")
    
    # Close connections
    source_client.close()
    target_client.close()

# Example usage
source_uri = "mongodb+srv://atlas2:<EMAIL>/atlas1?retryWrites=true&w=majority"
target_uri = "mongodb+srv://justinyu:<EMAIL>/"

simple_copy_collection(
    source_uri, "atlas1-test020422", "hta_reports_uk_nice",
    target_uri, "evicenter", "nice_reports"
)