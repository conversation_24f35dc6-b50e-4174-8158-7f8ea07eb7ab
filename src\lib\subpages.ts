export interface PageOption {
  label: string;
  value: string;
}

export const SUBPAGES: PageOption[] = [
  { label: "PubMed Explorer", value: "pubmed-explorer" },
  { label: "Europe PMC Explorer", value: "europe-pmc-explorer" },
  { label: "Drugs@FDA Search", value: "drug-docs" },
  { label: "Drug Label Comparator", value: "drug-label-comparator" },
  { label: "Clinical Trials", value: "clinical-trials" },
  { label: "EMA Medicines Explorer", value: "ema-medicines-explorer" },
  { label: "EMA RWD Catalogue", value: "ema-rwd-catalogue" },
  { label: "PROs Finder", value: "pros-finder" },
  { label: "HTA Reports", value: "hta-reports" },
  { label: "PPT Chart Generator", value: "ppt-chart-generator" },
  { label: "Featured Vendors", value: "featured-vendors" },
  { label: "Reg HTA Updates", value: "reg-hta-updates" },
  { label: "PubMed Sheets", value: "pubmed-sheets" },
  { label: "Document Comparator", value: "document-comparator" },
  { label: "Policies", value: "policies" },
  { label: "Help & Contact", value: "help-contact" },
  { label: "Advertise", value: "advertise" },
  { label: "About", value: "about" },
  { label: "Style Guide", value: "style-guide" },
  { label: "Agent Workflows", value: "agent-workflows" },
  { label: "Workflow Designer", value: "workflow-designer" },
];
