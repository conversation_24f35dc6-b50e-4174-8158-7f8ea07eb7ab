import csv
import json

def csv_to_json(csv_file_path, json_file_path=None, indent=2):
    """
    Convert CSV file to JSON format, preserving all values exactly as they appear in the CSV.
    
    Args:
        csv_file_path (str): Path to the input CSV file
        json_file_path (str): Path to the output JSON file (optional)
        indent (int): JSON indentation level
    
    Returns:
        list: The converted data as a list of dictionaries
    """
    try:
        with open(csv_file_path, 'r', encoding='utf-8') as csv_file:
            # Use csv.Sniffer to detect delimiter
            sample = csv_file.read(1024)
            csv_file.seek(0)
            sniffer = csv.Sniffer()
            delimiter = sniffer.sniff(sample).delimiter
            
            # Read CSV with detected delimiter
            csv_reader = csv.DictReader(csv_file, delimiter=delimiter)
            
            # Convert each row, preserving original values
            json_data = []
            for row in csv_reader:
                converted_row = {}
                for key, value in row.items():
                    # Clean up the key (remove extra whitespace) but preserve value exactly
                    clean_key = key.strip() if key else key
                    # Preserve the value exactly as it is, only convert None to empty string
                    converted_row[clean_key] = value if value is not None else ""
                json_data.append(converted_row)
        
        # Write to JSON file if path provided
        if json_file_path:
            with open(json_file_path, 'w', encoding='utf-8') as json_file:
                json.dump(json_data, json_file, indent=indent, ensure_ascii=False)
            print(f"Successfully converted {csv_file_path} to {json_file_path}")
        
        return json_data
        
    except FileNotFoundError:
        print(f"Error: File '{csv_file_path}' not found.")
        return None
    except Exception as e:
        print(f"Error converting CSV to JSON: {str(e)}")
        return None

def main():
    """
    Main function with hardcoded file paths.
    """
    # Hardcode your file paths here
    csv_file = "ema-rwd-catalogue-export-data.csv"  # Change this to your CSV file path
    json_file = "ema-rwd-catalogue-export-data.json"  # Change this to your desired JSON output path
    
    # Convert CSV to JSON
    result = csv_to_json(csv_file, json_file)
    
    if result:
        print(f"Conversion completed successfully!")
        print(f"Converted {len(result)} rows from CSV to JSON")
    else:
        print("Conversion failed!")

if __name__ == "__main__":
    main()