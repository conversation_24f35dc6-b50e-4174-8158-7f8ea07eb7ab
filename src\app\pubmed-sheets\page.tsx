"use client";

import React, { useMemo, useState, useEffect, useRef } from "react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Loader2,
  Copy,
  Search,
  FileText,
  FilePlus2,
  FileCheck2,
  BarChart3,
  <PERSON><PERSON><PERSON> as LineChartIcon,
  GitCompare,
  Download,
} from "lucide-react";
import { SubpageHeader } from "@/components/SubpageHeader";
import { useLocalStorage } from "@/hooks/use-local-storage";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { Bookmark, BookmarkCheck, X, ExternalLink } from "lucide-react";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  Di<PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
} from "@/components/ui/dialog";
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import {
  BarChart as ReBarChart,
  LineChart as ReLineChart,
  Line,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
} from "recharts";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface PubMedResult {
  id: string;
  title: string;
  pubdate?: string;
  journal?: string;
  authors?: string;
  affiliations?: string;
  sponsor?: string;
  abstract?: string;
  [key: string]: any;
}

interface SavedSearch {
  query: string;
  startYear?: string;
  endYear?: string;
}

interface CustomSheet {
  id: string;
  name: string;
  type: "custom";
  columns: string[];
  rows: Record<string, string>[];
}

interface SearchSheet {
  id: string;
  name: string;
  type: "search";
  query: string;
  startYear?: string;
  endYear?: string;
}

interface SheetData {
  results: PubMedResult[];
  totalCount: number | null;
  displayPage: number;
  pageSize: number;
}

type Sheet = CustomSheet | SearchSheet;

export default function PubMedSheetPage() {
  const [query, setQuery] = useState("");
  const [displayPage, setDisplayPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [selected, setSelected] = useState<string[]>([]);
  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [rowColors, setRowColors] = useState<Record<number, string>>({});
  const rowColorPalette = [
    '#fde68a',
    '#fecaca',
    '#a7f3d0',
    '#bfdbfe',
    '#ddd6fe',
    '#f9a8d4',
  ];
  const [lastRowSelected, setLastRowSelected] = useState<number | null>(null);
  const [prompt, setPrompt] = useState("");
  const [aiResponse, setAiResponse] = useState("");
  const [startYear, setStartYear] = useState("");
  const [endYear, setEndYear] = useState("");
  const [sheets, setSheets] = useLocalStorage<Sheet[]>("pubmedSheets", [
    { id: "sheet-1", name: "Sheet 1", type: "search", query: "" },
  ]);
  const [activeSheetId, setActiveSheetId] = useState("");
  const activeSheet = useMemo(
    () => sheets.find((s) => s.id === activeSheetId) || sheets[0],
    [sheets, activeSheetId],
  );
  const [newColumnName, setNewColumnName] = useState("");
  const [sheetData, setSheetData] = useLocalStorage<Record<string, SheetData>>("pubmedSheetData", {});
  const [hiddenRows, setHiddenRows] = useState<Record<string, number[]>>({});
  const [hiddenCols, setHiddenCols] = useState<Record<string, string[]>>({});
  const [columnMenu, setColumnMenu] = useState<{
    sheetId: string;
    index: number;
    x: number;
    y: number;
  } | null>(null);
  useEffect(() => {
    if (sheets.length > 0 && !activeSheetId) {
      setActiveSheetId(sheets[0].id);
    }
  }, [sheets, activeSheetId]);

  useEffect(() => {
    if (activeSheet?.type === "search") {
      setQuery(activeSheet.query);
      setStartYear(activeSheet.startYear || "");
      setEndYear(activeSheet.endYear || "");
    }
    const data = sheetData[activeSheet?.id || ""];
    if (data) {
      setAllResults(data.results);
      setFilteredResultsState(data.results);
      setTotalCount(data.totalCount);
      setDisplayPage(data.displayPage);
      setPageSize(data.pageSize);
    } else {
      setAllResults([]);
      setFilteredResultsState([]);
      setTotalCount(null);
      setDisplayPage(1);
      setPageSize(20);
    }
  }, [activeSheet?.id]);

  useEffect(() => {
    if (!activeSheet) return;
    if (!(activeSheet.id in sheetFilters)) {
      setSheetFilters({ ...sheetFilters, [activeSheet.id]: [] });
    }
  }, [activeSheet?.id]);

  const updateSheet = (sheet: Sheet) => {
    setSheets(sheets.map((s) => (s.id === sheet.id ? sheet : s)));
  };

  const renameSheet = (id: string) => {
    const sheet = sheets.find((s) => s.id === id);
    if (!sheet) return;
    const name = window.prompt("Rename sheet", sheet.name);
    if (name && name.trim()) {
      updateSheet({ ...sheet, name: name.trim() });
    }
  };

  const deleteSheet = (id: string) => {
    if (sheets.length === 1) return;
    const idx = sheets.findIndex((s) => s.id === id);
    if (idx === -1) return;
    const newSheets = sheets.filter((s) => s.id !== id);
    setSheets(newSheets);
    if (activeSheetId === id) {
      setActiveSheetId(newSheets[0].id);
    }
  };

  const [draggedSheetIndex, setDraggedSheetIndex] = useState<number | null>(null);
  const [sheetMenu, setSheetMenu] = useState<{
    id: string;
    x: number;
    y: number;
  } | null>(null);
  const handleSheetDragStart = (idx: number) => setDraggedSheetIndex(idx);
  const handleSheetDragOver = (e: React.DragEvent<HTMLButtonElement>, idx: number) => {
    e.preventDefault();
    if (draggedSheetIndex === null || draggedSheetIndex === idx) return;
    const newSheets = [...sheets];
    const dragged = newSheets[draggedSheetIndex];
    newSheets.splice(draggedSheetIndex, 1);
    newSheets.splice(idx, 0, dragged);
    setSheets(newSheets);
    setDraggedSheetIndex(idx);
  };

  useEffect(() => {
    const close = () => setSheetMenu(null);
    window.addEventListener('click', close);
    return () => window.removeEventListener('click', close);
  }, []);

  useEffect(() => {
    const close = () => setColumnMenu(null);
    window.addEventListener('click', close);
    return () => window.removeEventListener('click', close);
  }, []);

  useEffect(() => {
    const close = () => setTargetSheetMenu(null);
    window.addEventListener('click', close);
    return () => window.removeEventListener('click', close);
  }, []);

  const addSearchSheet = () => {
    const id = Date.now().toString();
    const count = sheets.filter((s) => s.type === "search").length + 1;
    const newSheet: SearchSheet = { id, name: `Search ${count}`, type: "search", query: "" };
    setSheets([...sheets, newSheet]);
    setActiveSheetId(id);
  };

  const addCustomSheet = () => {
    const id = Date.now().toString();
    const count = sheets.filter((s) => s.type === "custom").length + 1;
    const newSheet: CustomSheet = { id, name: `Custom ${count}`, type: "custom", columns: ["title"], rows: [] };
    setSheets([...sheets, newSheet]);
    setActiveSheetId(id);
  };
  const columnOptions = [
    "title",
    "journal",
    "pubdate",
    "authors",
    "affiliations",
    "sponsor",
    "abstract",
  ];

interface KeywordFilter {
  mode: "include" | "exclude";
  keywords: string[];
  columns: string[];
}

const [sheetFilters, setSheetFilters] = useLocalStorage<Record<string, KeywordFilter[]>>(
  "pubmedSheetFilters",
  {},
);
const filters: KeywordFilter[] = sheetFilters[activeSheet?.id || ""] || [];
const updateFilters = (fs: KeywordFilter[]) => {
  if (!activeSheet) return;
  setSheetFilters({ ...sheetFilters, [activeSheet.id]: fs });
};
  const [newMode, setNewMode] = useState<"include" | "exclude">("include");
  const [keywordInput, setKeywordInput] = useState("");
  const [newColumns, setNewColumns] = useState<string[]>([]);
  const [chartType, setChartType] = useState<"bar" | "line">("bar");
  const [chartHeight, setChartHeight] = useLocalStorage<number>(
    "pubmedChartHeight",
    200,
  );
  const [chartWidthUser, setChartWidthUser] = useLocalStorage<number>(
    "pubmedChartWidth",
    300,
  );
  const [maxChartHeight, setMaxChartHeight] = useState(300);
  const [tableWidth, setTableWidth] = useState(0);
  const tableRef = useRef<HTMLDivElement>(null);
  const customTableRef = useRef<HTMLDivElement>(null);
  const [customColWidths, setCustomColWidths] = useState<Record<string, number>>({});
  const [searchColWidths, setSearchColWidths] = useState<Record<string, number>>({});
  const [optSuggestions, setOptSuggestions] = useState<string[]>([]);
  const [meshSuggestions, setMeshSuggestions] = useState<string[]>([]);
  const [draggedFilterIndex, setDraggedFilterIndex] = useState<number | null>(null);
  const [draggedRowIndex, setDraggedRowIndex] = useState<number | null>(null);
  const [draggedColIndex, setDraggedColIndex] = useState<number | null>(null);
  const [targetSheetMenu, setTargetSheetMenu] = useState<{
    id: string;
    x: number;
    y: number;
  } | null>(null);
  const [loading, setLoading] = useState(false);
  const [genLoading, setGenLoading] = useState(false);
  const [meshLoading, setMeshLoading] = useState(false);
  const [exportingSaved, setExportingSaved] = useState(false);
  const [exportingAll, setExportingAll] = useState(false);
  const [filteredCount, setFilteredCount] = useState<number | null>(null);
  const [savedSearches, setSavedSearches] = useLocalStorage<SavedSearch[]>(
    "pubmedSheetSavedSearches",
    [],
  );
  const [totalCount, setTotalCount] = useState<number | null>(null);
  const [expandedIds, setExpandedIds] = useState<string[]>([]);
  const [bookmarks, setBookmarks] = useLocalStorage<PubMedResult[]>(
    "pubmedSheetBookmarks",
    [],
  );
  const [interactedIds, setInteractedIds] = useLocalStorage<string[]>(
    "pubmedSheetInteractions",
    [],
  );
  const [compareIds, setCompareIds] = useState<string[]>([]);
  const [expandedCompareIds, setExpandedCompareIds] = useState<string[]>([]);
  const [comparisonSummary, setComparisonSummary] = useState<string>("");
  const [compareLoading, setCompareLoading] = useState(false);
  const comparisonRef = useRef<HTMLDivElement | null>(null);
  const [singleResponses, setSingleResponses] = useState<Record<string, string>>({});
  const [promptArticle, setPromptArticle] = useState<PubMedResult | null>(null);
  const [promptValue, setPromptValue] = useState("");
  const [recentPrompts, setRecentPrompts] = useLocalStorage<string[]>(
    "pubmedRecentPrompts",
    [],
  );
  const [promptLoading, setPromptLoading] = useState(false);
  const [rowPromptIdx, setRowPromptIdx] = useState<number | null>(null);
  const [rowPromptValue, setRowPromptValue] = useState("");
  const [rowPromptCols, setRowPromptCols] = useState<string[]>([]);
  const [rowPromptOutput, setRowPromptOutput] = useState("ai_output");
  const [rowPromptLoading, setRowPromptLoading] = useState(false);

  const resizeInfo = useRef<{ col: string; custom: boolean } | null>(null);
  const startX = useRef(0);
  const startWidth = useRef(0);

  const onMouseMove = (e: MouseEvent) => {
    const info = resizeInfo.current;
    if (!info) return;
    const widths = info.custom ? customColWidths : searchColWidths;
    const container = info.custom ? customTableRef.current : tableRef.current;
    if (!container) return;
    const otherWidth = Object.entries(widths).reduce(
      (acc, [k, w]) => acc + (k === info.col ? 0 : w),
      0,
    );
    const maxWidth = Math.max(64, container.clientWidth - otherWidth);
    let newWidth = startWidth.current + (e.clientX - startX.current);
    if (newWidth < 64) newWidth = 64;
    if (newWidth > maxWidth) newWidth = maxWidth;
    if (info.custom) {
      setCustomColWidths({ ...widths, [info.col]: newWidth });
    } else {
      setSearchColWidths({ ...widths, [info.col]: newWidth });
    }
  };

  const stopResize = () => {
    resizeInfo.current = null;
    document.removeEventListener('mousemove', onMouseMove);
    document.removeEventListener('mouseup', stopResize);
  };

  const startResize = (col: string, custom: boolean) => (e: React.MouseEvent) => {
    const widths = custom ? customColWidths : searchColWidths;
    startX.current = e.clientX;
    startWidth.current = widths[col] ?? 0;
    resizeInfo.current = { col, custom };
    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', stopResize);
  };
  const [pdfLinks, setPdfLinks] = useState<Record<string, string | null>>({});
  const [pubmedLinks, setPubmedLinks] = useState<Record<string, { pdf: string | null; link: string | null }>>({});
  const [pdfLoadingRows, setPdfLoadingRows] = useState<number[]>([]);
  const [pdfDialogIdx, setPdfDialogIdx] = useState<number | null>(null);
  const [aiOutputVisible, setAiOutputVisible] = useState(true);
  const [allResults, setAllResults] = useState<PubMedResult[]>([]);
  const [filteredResultsState, setFilteredResultsState] = useState<PubMedResult[]>([]);
  const [filterLoading, setFilterLoading] = useState(false);
  const [columnVisibility, setColumnVisibility] = useState({
    title: true,
    journal: true,
    pubdate: true,
    authors: true,
    affiliations: false,
    sponsor: false,
    abstract: true,
    aiOutput: true,
  });
  const showSingleOutput = Object.keys(singleResponses).length > 0;
  const showAiColumn = showSingleOutput &&
    aiOutputVisible &&
    columnVisibility.aiOutput;
  const { toast } = useToast();
  const scrollPosRef = useRef(0);

  const fetchResults = async (q: string) => {
    try {
      setLoading(true);
      const ps = 100;
      let page = 1;
      let all: PubMedResult[] = [];
      let count = 0;
      while (true) {
        const params = new URLSearchParams({
          query: q,
          page: String(page),
          pageSize: String(ps),
          ...(startYear ? { startYear } : {}),
          ...(endYear ? { endYear } : {}),
        });
        const res = await fetch(`/api/pubmed-search?${params.toString()}`);
        if (!res.ok) throw new Error('search failed');
        const data = await res.json();
        count = data.count;
        all = [...all, ...data.results];
        if (all.length >= count || data.results.length === 0) break;
        page += 1;
      }
      setAllResults(all);
      setDisplayPage(1);
      setPageSize(20);
      setTotalCount(count);
      setLoading(false);
      if (activeSheet) {
        setSheetData({
          ...sheetData,
          [activeSheet.id]: {
            results: all,
            totalCount: count,
            displayPage: 1,
            pageSize: 20,
          },
        });
      }
      fetch("/api/pubmed-save", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ query: q, results: all }),
      }).catch((err) => console.error('save error', err));
    } catch (err) {
      toast({ title: "Search failed" });
      setLoading(false);
    }
  };

  const searchWith = async (q: string) => {
    scrollPosRef.current = window.scrollY;
    setQuery(q);
    setSelected([]);
    setTotalCount(null);
    setOptSuggestions([]);
    setMeshSuggestions([]);
    setFilteredCount(null);
    setAllResults([]);
    setFilteredResultsState([]);
    setPageSize(20);
    setDisplayPage(1);
    if (activeSheet) {
      setSheetData({
        ...sheetData,
        [activeSheet.id]: {
          results: [],
          totalCount: null,
          displayPage: 1,
          pageSize: 20,
        },
      });
    }
    await fetchResults(q);
    window.scrollTo({ top: scrollPosRef.current });
    if (activeSheet && activeSheet.type === 'search') {
      updateSheet({
        ...activeSheet,
        query: q,
        startYear: startYear || undefined,
        endYear: endYear || undefined,
      });
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (!query.trim()) return;
    searchWith(query);
  };



  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setDisplayPage(1);
    if (activeSheet) {
      setSheetData({
        ...sheetData,
        [activeSheet.id]: {
          ...(sheetData[activeSheet.id] || { results: allResults, totalCount, displayPage: 1, pageSize: size }),
          displayPage: 1,
          pageSize: size,
        },
      });
    }
  };

  const changePage = (p: number) => {
    if (p < 1) return;
    setDisplayPage(p);
    if (activeSheet) {
      setSheetData({
        ...sheetData,
        [activeSheet.id]: {
          ...(sheetData[activeSheet.id] || { results: allResults, totalCount, displayPage: p, pageSize }),
          displayPage: p,
          pageSize,
        },
      });
    }
  };

  const visibleResults = useMemo(() => {
    const start = (displayPage - 1) * pageSize;
    return filteredResultsState.slice(start, start + pageSize);
  }, [filteredResultsState, displayPage, pageSize]);

  const compareItems = useMemo(
    () => allResults.filter((r) => compareIds.includes(r.id)),
    [allResults, compareIds],
  );

  useEffect(() => {
    const updateDims = () => {
      setMaxChartHeight(Math.floor(window.innerHeight / 2));
      if (tableRef.current) {
        setTableWidth(tableRef.current.clientWidth);
      }
    };
    updateDims();
    window.addEventListener('resize', updateDims);
    return () => window.removeEventListener('resize', updateDims);
  }, [visibleResults, columnVisibility, showAiColumn]);

  useEffect(() => {
    if (activeSheet?.type === 'custom' && customTableRef.current) {
      const headers = customTableRef.current.querySelectorAll<HTMLTableCellElement>('th[data-col-name]');
      const w: Record<string, number> = {};
      headers.forEach((th) => {
        const name = th.getAttribute('data-col-name');
        if (name) w[name] = th.clientWidth;
      });
      setCustomColWidths(w);
    }
  }, [activeSheet?.id, activeSheet?.type === 'custom' ? (activeSheet as CustomSheet).columns : []]);

  useEffect(() => {
    if (tableRef.current) {
      const headers = tableRef.current.querySelectorAll<HTMLTableCellElement>('th[data-col-name]');
      const w: Record<string, number> = {};
      headers.forEach((th) => {
        const name = th.getAttribute('data-col-name');
        if (name) w[name] = th.clientWidth;
      });
      setSearchColWidths(w);
    }
  }, [columnVisibility, showAiColumn, tableWidth]);

  // The API provides direct links already
  const ensureDirectPdfUrl = (url: string | null): string | null => url;


  useEffect(() => {
    if (visibleResults.length === 0) {
      setPdfLinks({});
      setPubmedLinks({});
      return;
    }

    let cancelled = false;
    const loadLinks = async () => {
      const pmcLinks: Record<string, string | null> = {};
      const pmLinks: Record<string, { pdf: string | null; link: string | null }> = {};
      
      // Process in smaller batches to avoid overwhelming the API
      const batchSize = 10;
      for (let i = 0; i < visibleResults.length; i += batchSize) {
        const batch = visibleResults.slice(i, i + batchSize);
        
        await Promise.all(
          batch.map(async (r) => {
            try {
              const res = await fetch(`/api/pmc-pdf-link?pmid=${r.id}`);
              if (!res.ok) throw new Error('Failed to fetch PDF link');

              const data = await res.json();
              if (data.pdfUrl) {
                pmcLinks[r.id] = ensureDirectPdfUrl(data.pdfUrl);
              } else {
                pmcLinks[r.id] = null;
              }
            } catch (error) {
              console.error(`Error fetching PDF link for PMID ${r.id}:`, error);
              pmcLinks[r.id] = null;
            }

            try {
              const res2 = await fetch(`/api/pubmed-fulltext-link?pmid=${r.id}`);
              if (!res2.ok) throw new Error('Failed to fetch full text link');
              const data2 = await res2.json();
              pmLinks[r.id] = {
                pdf: data2.pdfLink || null,
                link: data2.pageLink || null,
              };
            } catch (error) {
              console.error(`Error fetching full text link for PMID ${r.id}:`, error);
              pmLinks[r.id] = { pdf: null, link: null };
            }
          })
        );

        if (!cancelled) {
          setPdfLinks(prev => ({ ...prev, ...pmcLinks }));
          setPubmedLinks(prev => ({ ...prev, ...pmLinks }));
        }
        
        // Small delay between batches
        if (i + batchSize < visibleResults.length) {
          await new Promise(resolve => setTimeout(resolve, 300));
        }
      }
    };
    
    loadLinks();
    
    return () => {
      cancelled = true;
    };
  }, [visibleResults]);

  const saveCurrentSearch = () => {
    if (!query.trim()) return;
    const newSearch: SavedSearch = {
      query: query.trim(),
      startYear: startYear || undefined,
      endYear: endYear || undefined,
    };
    const exists = savedSearches.some(
      (s) =>
        s.query === newSearch.query &&
        (s.startYear || "") === (newSearch.startYear || "") &&
        (s.endYear || "") === (newSearch.endYear || ""),
    );
    if (exists) return;
    setSavedSearches([...savedSearches, newSearch]);
    toast({ title: "Search saved" });
  };

  const loadSavedSearch = (s: SavedSearch) => {
    setQuery(s.query);
    setStartYear(s.startYear || "");
    setEndYear(s.endYear || "");
    searchWith(s.query);
  };

  const removeSavedSearch = (idx: number) => {
    setSavedSearches(savedSearches.filter((_, i) => i !== idx));
  };

  const generateQueries = async () => {
    if (!query.trim()) return;
    setGenLoading(true);
    const res = await fetch("/api/chat", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        messages: [
          {
            role: "user",
            content: `Suggest 3 alternative PubMed search queries that could improve results for: ${query}.\nReturn only the queries separated by new lines without numbering or explanation.`,
          },
        ],
      }),
    });
    if (res.ok) {
      const data = await res.json();
      const lines = data.content
        .split("\n")
        .map((l: string) => l.trim())
        .filter(Boolean);
      setOptSuggestions(lines);
    }
    setGenLoading(false);
  };

  const generateMeshQueries = async () => {
    if (!query.trim()) return;
    setMeshLoading(true);
    const res = await fetch("/api/chat", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        messages: [
          {
            role: "user",
            content: `Suggest 3 PubMed search queries in MeSH format that could improve results for: ${query}.\nReturn only the queries separated by new lines without numbering or explanation.`,
          },
        ],
      }),
    });
    if (res.ok) {
      const data = await res.json();
      const lines = data.content
        .split("\n")
        .map((l: string) => l.trim())
        .filter(Boolean);
      setMeshSuggestions(lines);
    }
    setMeshLoading(false);
  };

  const runPrompt = async () => {
    if (!prompt.trim() || selected.length === 0) return;
    const articleText = filteredResultsState
      .filter((r) => selected.includes(r.id))
      .map((r) => `Title: ${r.title}\nAbstract: ${r.abstract || ""}`)
      .join("\n\n");
    const res = await fetch("/api/chat", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ messages: [{ role: "user", content: `${prompt}\n\n${articleText}` }] }),
    });
    if (res.ok) {
      const data = await res.json();
      setAiResponse(data.content);
    } else {
      toast({ title: "AI request failed" });
    }
  };

  const openPromptDialog = (article: PubMedResult) => {
    setPromptArticle(article);
    setPromptValue("");
  };

  const submitPrompt = async () => {
    if (!promptArticle || !promptValue.trim()) return;
    setPromptLoading(true);
    const res = await fetch("/api/chat", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        messages: [
          {
            role: "user",
            content: `${promptValue}\n\nTitle: ${promptArticle.title}\nAbstract: ${promptArticle.abstract || ""}`,
          },
        ],
      }),
    });
    if (res.ok) {
      const data = await res.json();
      setSingleResponses((prev) => ({
        ...prev,
        [promptArticle.id]: data.content,
      }));
      const updated = [promptValue, ...recentPrompts.filter((p) => p !== promptValue)];
      setRecentPrompts(updated.slice(0, 3));
    } else {
      toast({ title: "AI request failed" });
    }
    setPromptLoading(false);
    setPromptArticle(null);
    setPromptValue("");
  };

  const openRowPrompt = (index: number) => {
    if (!activeSheet || activeSheet.type !== "custom") return;
    setRowPromptIdx(index);
    setRowPromptValue("");
    setRowPromptCols(activeSheet.columns);
    setRowPromptOutput("ai_output");
  };

  const runRowPrompt = async () => {
    if (
      rowPromptIdx === null ||
      !activeSheet ||
      activeSheet.type !== "custom" ||
      !rowPromptValue.trim()
    )
      return;
    const row = activeSheet.rows[rowPromptIdx];
    const context = rowPromptCols
      .map((c) => `${c}: ${row[c]}`)
      .join("\n");
    setRowPromptLoading(true);
    const res = await fetch("/api/chat", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        messages: [{ role: "user", content: `${rowPromptValue}\n\n${context}` }],
      }),
    });
    if (res.ok) {
      const data = await res.json();
      let col = rowPromptOutput.trim() || "ai_output";
      let columns = activeSheet.columns;
      let rows = activeSheet.rows.map((r) => ({ ...r }));
      if (!columns.includes(col)) {
        columns = [...columns, col];
        rows = rows.map((r) => ({ ...r, [col]: "" }));
      }
      rows[rowPromptIdx] = { ...rows[rowPromptIdx], [col]: data.content };
      updateSheet({ ...activeSheet, columns, rows });
    } else {
      toast({ title: "AI request failed" });
    }
    setRowPromptLoading(false);
    setRowPromptIdx(null);
  };

  const uploadPdf = async (file: File, rowIdx: number) => {
    if (!activeSheet || activeSheet.type !== 'custom') return;
    setPdfLoadingRows((p) => [...p, rowIdx]);
    try {
      const formData = new FormData();
      formData.append('file', file);
      const res = await fetch('/api/pdf-to-markdown', {
        method: 'POST',
        body: formData,
      });
      if (res.ok) {
        const data = await res.json();
        const rows = activeSheet.rows.map((r, i) =>
          i === rowIdx ? { ...r, pdfText: data.text } : r,
        );
        updateSheet({ ...activeSheet, rows });
      } else {
        toast({ title: 'PDF conversion failed' });
      }
    } catch (err) {
      console.error('PDF upload error', err);
      toast({ title: 'PDF conversion failed' });
    } finally {
      setPdfLoadingRows((p) => p.filter((i) => i !== rowIdx));
    }
  };

  const [lastSelected, setLastSelected] = useState<number | null>(null);
  const toggleSelect = (id: string, index: number, range: boolean) => {
    setSelected((prev) => {
      let newSel = [...prev];
      if (range && lastSelected !== null) {
        const [start, end] = [lastSelected, index].sort((a, b) => a - b);
        const ids = filteredResultsState.slice(start, end + 1).map((r) => r.id);
        const allSelected = ids.every((i) => newSel.includes(i));
        if (allSelected) {
          newSel = newSel.filter((i) => !ids.includes(i));
        } else {
          ids.forEach((i) => {
            if (!newSel.includes(i)) newSel.push(i);
          });
        }
      } else {
        newSel = newSel.includes(id)
          ? newSel.filter((p) => p !== id)
          : [...newSel, id];
      }
      return newSel;
    });
    setLastSelected(index);
  };

  const allSelected =
    filteredResultsState.length > 0 &&
    filteredResultsState.every((r) => selected.includes(r.id));

  const toggleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelected(filteredResultsState.map((r) => r.id));
    } else {
      setSelected([]);
    }
  };

  const toggleRowSelect = (index: number, range: boolean) => {
    setSelectedRows((prev) => {
      let newSel = [...prev];
      if (range && lastRowSelected !== null) {
        const [start, end] = [lastRowSelected, index].sort((a, b) => a - b);
        const ids = Array.from({ length: end - start + 1 }, (_, i) => start + i);
        const all = ids.every((i) => newSel.includes(i));
        if (all) {
          newSel = newSel.filter((i) => !ids.includes(i));
        } else {
          ids.forEach((i) => {
            if (!newSel.includes(i)) newSel.push(i);
          });
        }
      } else {
        newSel = newSel.includes(index)
          ? newSel.filter((p) => p !== index)
          : [...newSel, index];
      }
      return newSel;
    });
    setLastRowSelected(index);
  };

  const allRowsSelected =
    activeSheet?.type === 'custom' &&
    activeSheet.rows.length > 0 &&
    activeSheet.rows.every((_, i) => selectedRows.includes(i));

  const toggleRowSelectAll = (checked: boolean) => {
    if (!activeSheet || activeSheet.type !== 'custom') return;
    if (checked) {
      setSelectedRows(activeSheet.rows.map((_, i) => i));
    } else {
      setSelectedRows([]);
    }
  };

  const setColorForSelectedRows = (color: string) => {
    setRowColors(prev => {
      const updated = { ...prev };
      selectedRows.forEach(idx => {
        updated[idx] = color;
      });
      return updated;
    });
  };

  const clearColorForSelectedRows = () => {
    setRowColors(prev => {
      const updated = { ...prev };
      selectedRows.forEach(idx => {
        delete updated[idx];
      });
      return updated;
    });
  };

  const toggleBookmark = (item: PubMedResult) => {
    const exists = bookmarks.some((b) => b.id === item.id);
    const updated = exists ? bookmarks.filter((b) => b.id !== item.id) : [...bookmarks, item];
    setBookmarks(updated);
  };

  const toggleCompare = (id: string) => {
    if (compareIds.includes(id)) {
      setCompareIds(compareIds.filter((c) => c !== id));
    } else {
      setCompareIds([...compareIds, id]);
    }
  };

  const toggleCompareAbstract = (id: string) => {
    if (expandedCompareIds.includes(id)) {
      setExpandedCompareIds(expandedCompareIds.filter((i) => i !== id));
    } else {
      setExpandedCompareIds([...expandedCompareIds, id]);
    }
  };

  const clearComparison = () => {
    setCompareIds([]);
  };

  const scrollToComparison = () => {
    if (comparisonRef.current) {
      comparisonRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  };

  const exportComparisonCsv = () => {
    if (compareIds.length === 0) return;
    const items = allResults.filter((r) => compareIds.includes(r.id));
    const header = ["PMID", "Title", "Publication Date", "Journal", "Authors", "Abstract"];
    const rows = items.map((r) =>
      [
        r.id,
        r.title.replace(/\"/g, '""'),
        r.pubdate || "",
        r.journal || "",
        r.authors ? r.authors.replace(/\"/g, '""') : "",
        r.abstract ? r.abstract.replace(/\"/g, '""') : "",
      ]
        .map((v) => `"${v}"`)
        .join(","),
    );
    const csvContent = [header.join(","), ...rows].join("\n");
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `pubmed-comparison-${Date.now()}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const summarizeComparison = async () => {
    const items = allResults.filter((r) => compareIds.includes(r.id));
    if (items.length < 2) return;
    setCompareLoading(true);
    const text = items
      .slice(0, 5)
      .map((c) => `Title: ${c.title}\n${c.abstract}`)
      .join("\n\n");
    const prompt =
      `Compare these PubMed articles and summarize their key similarities and differences in up to five \u2022 bullet points without numbers.\n\n${text}`;
    try {
      const resp = await fetchChat(prompt);
      setComparisonSummary(resp);
    } finally {
      setCompareLoading(false);
    }
  };

  const markInteracted = (id: string) => {
    if (!interactedIds.includes(id)) {
      setInteractedIds([...interactedIds, id]);
    }
  };

  const toggleAbstract = (id: string) => {
    markInteracted(id);
    setExpandedIds((prev) => (prev.includes(id) ? prev.filter((p) => p !== id) : [...prev, id]));
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      toast({ title: "Copied to clipboard" });
    });
  };

  const toggleColumn = (col: keyof typeof columnVisibility) => {
    setColumnVisibility((prev) => ({ ...prev, [col]: !prev[col] }));
  };

  const simpleMarkdown = (text: string) => {
    return text
      .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.+?)\*/g, '<em>$1</em>')
      .replace(/`(.+?)`/g, '<code>$1</code>')
      .replace(/\n/g, '<br/>');
  };

  const parseKeywords = (text: string): string[] => {
    const result: string[] = [];
    let current = "";
    let inQuotes = false;
    for (let i = 0; i < text.length; i++) {
      const ch = text[i];
      if (ch === '"') {
        inQuotes = !inQuotes;
      } else if (ch === ',' && !inQuotes) {
        if (current.trim()) result.push(current.trim());
        current = "";
      } else {
        current += ch;
      }
    }
    if (current.trim()) result.push(current.trim());
    return result;
  };

  const parseBullets = (text: string) =>
    text
      .split(/\n+/)
      .map((l) => l.replace(/^[^\w]+|^[0-9]+\.?\s+/, "").trim())
      .filter(Boolean);

  const fetchChat = async (prompt: string) => {
    const res = await fetch("/api/chat", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ messages: [{ role: "user", content: prompt }] }),
    });
    if (!res.ok) throw new Error("ChatGPT request failed");
    const data = await res.json();
    return data.content as string;
  };

  const applyFilters = (
    items: PubMedResult[],
    fs: KeywordFilter[],
  ): PubMedResult[] => {
    let filtered = items;
    fs.forEach((f) => {
      const kws = f.keywords.map((k) => k.toLowerCase());
      filtered = filtered.filter((r) => {
        const values = f.columns.map((c) => String(r[c] || '').toLowerCase());
        if (f.mode === 'include') {
          return kws.every((kw) => values.some((v) => v.includes(kw)));
        }
        return !kws.some((kw) => values.some((v) => v.includes(kw)));
      });
    });
    return filtered;
  };

  const addFilter = () => {
    const kws = parseKeywords(keywordInput);
    if (kws.length === 0 || newColumns.length === 0) return;
    updateFilters([...filters, { mode: newMode, keywords: kws, columns: newColumns }]);
    setKeywordInput("");
  };

  const removeFilter = (idx: number) => {
    updateFilters(filters.filter((_, i) => i !== idx));
  };

  const handleFilterDragStart = (index: number) => {
    setDraggedFilterIndex(index);
  };

  const handleFilterDragOver = (
    e: React.DragEvent<HTMLLIElement>,
    index: number,
  ) => {
    e.preventDefault();
    if (draggedFilterIndex === null || draggedFilterIndex === index) return;
    const newFilters = [...filters];
    const dragged = newFilters[draggedFilterIndex];
    newFilters.splice(draggedFilterIndex, 1);
    newFilters.splice(index, 0, dragged);
    updateFilters(newFilters);
    setDraggedFilterIndex(index);
  };

  const handleChartClick = (index: number) => {
    if (index === 0) return;
    const f = filters[index - 1];
    const kw = window.prompt("Edit keywords", f.keywords.join(", "));
    if (kw !== null) {
      const kws = parseKeywords(kw);
      if (kws.length) {
        const updated = [...filters];
        updated[index - 1] = { ...f, keywords: kws };
        updateFilters(updated);
      }
    }
  };

  const { filteredResults, filterCounts } = useMemo(() => {
    let items = allResults;
    const counts: number[] = [items.length];
    filters.forEach((f, idx) => {
      items = applyFilters(items, [f]);
      counts.push(items.length);
    });
    return { filteredResults: items, filterCounts: counts };
  }, [allResults, filters]);

  useEffect(() => {
    setFilterLoading(true);
    const t = setTimeout(() => {
      setFilteredResultsState(filteredResults);
      setFilteredCount(filteredResults.length);
      setDisplayPage(1);
      setFilterLoading(false);
      if (activeSheet) {
        setSheetData({
          ...sheetData,
          [activeSheet.id]: {
            ...(sheetData[activeSheet.id] || { results: allResults, totalCount, displayPage: 1, pageSize }),
            displayPage: 1,
          },
        });
      }
    }, 100);
    return () => clearTimeout(t);
  }, [filteredResults]);

  const chartData = useMemo(() => {
    const data = [{ name: "Start", count: filterCounts[0] || 0 }];
    filters.forEach((f, idx) => {
      const label = `${f.mode === 'include' ? '+' : '-'} ${f.keywords.join(' | ')}`;
      data.push({ name: label, count: filterCounts[idx + 1] || 0 });
    });
    return data;
  }, [filterCounts, filters]);

  const baseChartWidth = Math.max(300, (filters.length + 1) * 120);
  useEffect(() => {
    setChartWidthUser(Math.max(baseChartWidth, chartWidthUser));
  }, [baseChartWidth]);
  const chartWidth = Math.min(chartWidthUser, tableWidth || chartWidthUser);

  const totalPages = Math.ceil(filteredResultsState.length / pageSize);

  useEffect(() => {
    if (displayPage > totalPages) {
      setDisplayPage(totalPages || 1);
    }
  }, [totalPages, displayPage]);

  const paginationPages = useMemo(() => {
    const pages: number[] = [];
    if (totalPages === 0) return pages;
    const start = Math.max(1, displayPage - 2);
    const end = Math.min(totalPages, displayPage + 2);
    if (start > 1) pages.push(1);
    if (start > 2) pages.push(-1);
    for (let i = start; i <= end; i++) pages.push(i);
    if (end < totalPages - 1) pages.push(-1);
    if (end < totalPages) pages.push(totalPages);
    return pages;
  }, [displayPage, totalPages]);

  const downloadCsv = (items: PubMedResult[], fileName: string) => {
    const header = [
      'PMID',
      'Title',
      'Pub Date',
      'Journal',
      'Authors',
      'Affiliations',
      'Sponsor',
      'Abstract',
    ];
    const rows = items.map((r) =>
      [r.id, r.title, r.pubdate || '', r.journal || '', r.authors || '', r.affiliations || '', r.sponsor || '', r.abstract || '']
        .map((v) => `"${String(v).replace(/"/g, '""')}"`)
        .join(','),
    );
    const csvContent = [header.join(','), ...rows].join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const downloadCustomCsv = (
    cols: string[],
    rows: Record<string, string>[],
    fileName: string,
  ) => {
    const header = cols.map((c) => `"${c}"`).join(',');
    const csvRows = rows.map((r) =>
      cols
        .map((c) => `"${String(r[c] || '').replace(/"/g, '""')}"`)
        .join(','),
    );
    const csvContent = [header, ...csvRows].join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const exportSavedCsv = () => {
    if (bookmarks.length === 0) return;
    setExportingSaved(true);
    try {
      downloadCsv(bookmarks, 'pubmed-saved.csv');
      toast({ title: "Exported saved articles" });
    } catch (error) {
      console.error("Export error:", error);
      toast({
        title: "Export failed",
        description: "Could not export saved articles",
        variant: "destructive",
      });
    } finally {
      setExportingSaved(false);
    }
  };

  const exportAllCsv = async () => {
    if (!query.trim()) return;
    setExportingAll(true);

    try {
      // Fetch all results for the current query
      const allItems: PubMedResult[] = [];
      const ps = 100;
      const totalPages = Math.ceil((totalCount || 0) / ps);

      for (let currentPage = 1; currentPage <= totalPages; currentPage++) {
        const params = new URLSearchParams({
          query,
          page: String(currentPage),
          pageSize: String(ps),
          ...(startYear ? { startYear } : {}),
          ...(endYear ? { endYear } : {}),
        });

        const res = await fetch(`/api/pubmed-search?${params.toString()}`);
        if (!res.ok) throw new Error('Failed to fetch additional results');

        const data = await res.json();
        allItems.push(...data.results);

        toast({
          title: `Fetching results (${Math.min(currentPage * ps, totalCount || 0)} of ${totalCount})`,
          duration: 2000,
        });
      }
      
      // Apply filters to all results
      let items = applyFilters(allItems, filters);
      
      // Download the CSV with all results
      if (items.length > 0) {
        downloadCsv(items, `pubmed-results-${Date.now()}.csv`);
        toast({
          title: `Exported ${items.length} articles`,
          description: "CSV download complete",
        });
      }
    } catch (error) {
      console.error("Export error:", error);
      toast({
        title: "Export failed",
        description: "Could not export all results",
        variant: "destructive",
      });
    } finally {
      setExportingAll(false);
    }
  };

  const addColumn = (name: string) => {
    if (!activeSheet || activeSheet.type !== 'custom') return;
    if (activeSheet.columns.includes(name)) return;
    updateSheet({
      ...activeSheet,
      columns: [...activeSheet.columns, name],
      rows: activeSheet.rows.map((r) => ({ ...r, [name]: '' })),
    });
  };

  const insertColumn = (index: number, name: string) => {
    if (!activeSheet || activeSheet.type !== 'custom') return;
    if (activeSheet.columns.includes(name)) return;
    const columns = [...activeSheet.columns];
    columns.splice(index, 0, name);
    const rows = activeSheet.rows.map((r) => {
      const nr: Record<string, string> = {};
      columns.forEach((c) => {
        if (c === name) nr[c] = '';
        else nr[c] = r[c] ?? '';
      });
      if ('pdfText' in r) nr['pdfText'] = r['pdfText'];
      return nr;
    });
    updateSheet({ ...activeSheet, columns, rows });
  };

  const deleteColumn = (index: number) => {
    if (!activeSheet || activeSheet.type !== 'custom') return;
    const col = activeSheet.columns[index];
    const columns = activeSheet.columns.filter((_, i) => i !== index);
    const rows = activeSheet.rows.map((r) => {
      const nr = { ...r };
      delete nr[col];
      return nr;
    });
    updateSheet({ ...activeSheet, columns, rows });
  };

  const duplicateColumn = (index: number, name: string) => {
    if (!activeSheet || activeSheet.type !== 'custom') return;
    const base = activeSheet.columns[index];
    const columns = [...activeSheet.columns];
    columns.splice(index + 1, 0, name);
    const rows = activeSheet.rows.map((r) => ({ ...r, [name]: r[base] }));
    updateSheet({ ...activeSheet, columns, rows });
  };

  const addRow = () => {
    if (!activeSheet || activeSheet.type !== 'custom') return;
    const row: Record<string, string> = {};
    activeSheet.columns.forEach((c) => {
      row[c] = '';
    });
    row['pdfText'] = '';
    updateSheet({ ...activeSheet, rows: [...activeSheet.rows, row] });
  };

  const insertRow = (index: number) => {
    if (!activeSheet || activeSheet.type !== 'custom') return;
    const row: Record<string, string> = {};
    activeSheet.columns.forEach((c) => (row[c] = ''));
    row['pdfText'] = '';
    const rows = [...activeSheet.rows];
    rows.splice(index, 0, row);
    updateSheet({ ...activeSheet, rows });
    setRowColors(prev => {
      const updated: Record<number, string> = {};
      Object.entries(prev).forEach(([i, color]) => {
        const idx = Number(i);
        updated[idx >= index ? idx + 1 : idx] = color;
      });
      return updated;
    });
  };

  const deleteRow = (index: number) => {
    if (!activeSheet || activeSheet.type !== 'custom') return;
    const rows = activeSheet.rows.filter((_, i) => i !== index);
    updateSheet({ ...activeSheet, rows });
    setRowColors(prev => {
      const updated: Record<number, string> = {};
      Object.entries(prev).forEach(([i, color]) => {
        const idx = Number(i);
        if (idx < index) updated[idx] = color;
        else if (idx > index) updated[idx - 1] = color;
      });
      return updated;
    });
  };

  const duplicateRow = (index: number) => {
    if (!activeSheet || activeSheet.type !== 'custom') return;
    const row = { ...activeSheet.rows[index] };
    const rows = [...activeSheet.rows];
    rows.splice(index + 1, 0, row);
    updateSheet({ ...activeSheet, rows });
    setRowColors(prev => {
      const updated: Record<number, string> = {};
      Object.entries(prev).forEach(([i, color]) => {
        const idx = Number(i);
        updated[idx > index ? idx + 1 : idx] = color;
      });
      if (prev[index]) updated[index + 1] = prev[index];
      return updated;
    });
  };

  const handleRowDragStart = (index: number) => {
    setDraggedRowIndex(index);
  };

  const handleRowDragOver = (
    e: React.DragEvent<HTMLTableRowElement>,
    index: number,
  ) => {
    e.preventDefault();
    if (
      draggedRowIndex === null ||
      draggedRowIndex === index ||
      !activeSheet ||
      activeSheet.type !== 'custom'
    )
      return;
    const rows = [...activeSheet.rows];
    const dragged = rows[draggedRowIndex];
    rows.splice(draggedRowIndex, 1);
    rows.splice(index, 0, dragged);
    updateSheet({ ...activeSheet, rows });
    setDraggedRowIndex(index);
  };

  const handleColDragStart = (index: number) => {
    setDraggedColIndex(index);
  };

  const handleColDragOver = (
    e: React.DragEvent<HTMLTableCellElement>,
    index: number,
  ) => {
    e.preventDefault();
    if (
      draggedColIndex === null ||
      draggedColIndex === index ||
      !activeSheet ||
      activeSheet.type !== 'custom'
    )
      return;
    const columns = [...activeSheet.columns];
    const dragged = columns[draggedColIndex];
    columns.splice(draggedColIndex, 1);
    columns.splice(index, 0, dragged);
    const rows = activeSheet.rows.map((r) => {
      const nr: Record<string, string> = {};
      columns.forEach((c) => {
        nr[c] = r[c] ?? '';
      });
      if ('pdfText' in r) nr['pdfText'] = r['pdfText'];
      return nr;
    });
    updateSheet({ ...activeSheet, columns, rows });
    setDraggedColIndex(index);
  };

  const updateCell = (rowIdx: number, col: string, val: string) => {
    if (!activeSheet || activeSheet.type !== 'custom') return;
    const rows = activeSheet.rows.map((r, i) =>
      i === rowIdx ? { ...r, [col]: val } : r,
    );
    updateSheet({ ...activeSheet, rows });
  };

  const exportCurrentCustom = () => {
    if (!activeSheet || activeSheet.type !== 'custom') return;
    if (activeSheet.rows.length === 0) return;
    downloadCustomCsv(
      activeSheet.columns,
      activeSheet.rows,
      `${activeSheet.name.replace(/\s+/g, '_')}.csv`,
    );
    toast({ title: `Exported ${activeSheet.rows.length} rows` });
  };

  const customSheets = useMemo(
    () => sheets.filter((s) => s.type === 'custom') as CustomSheet[],
    [sheets],
  );
  const [targetCustomId, setTargetCustomId] = useState<string>('');
  useEffect(() => {
    if (customSheets.length > 0 && !targetCustomId) {
      setTargetCustomId(customSheets[0].id);
    }
  }, [customSheets, targetCustomId]);

  const addSelectedToCustom = () => {
    const sheet = sheets.find((s) => s.id === targetCustomId) as CustomSheet | undefined;
    if (!sheet || selected.length === 0) return;

    let columns = sheet.columns;
    let rows = sheet.rows;

    if (sheet.rows.length === 0 && columns.length <= 1) {
      columns = columnOptions;
    }

    // Ensure a source column exists and add it to existing rows if missing
    if (!columns.includes('source')) {
      columns = [...columns, 'source'];
      rows = rows.map((r) => ({ ...r, source: '' }));
      if (!hiddenCols[sheet.id]?.includes('source')) {
        setHiddenCols({
          ...hiddenCols,
          [sheet.id]: [...(hiddenCols[sheet.id] || []), 'source'],
        });
      }
    }

    const queryDetails = JSON.stringify({
      query,
      startYear: startYear || undefined,
      endYear: endYear || undefined,
      filters,
      date: new Date().toISOString(),
    });

    const newRows = filteredResultsState
      .filter((r) => selected.includes(r.id))
      .map((r) => {
        const row: Record<string, string> = {};
        columns.forEach((c) => {
          if (c === 'source') {
            row[c] = queryDetails;
          } else {
            row[c] = (r as any)[c] || '';
          }
        });
        row['pdfText'] = '';
        return row;
      });

    const isEmpty = (row: Record<string, string>) => columns.every((c) => !row[c]);
    let updatedRows = [...rows];
    while (updatedRows.length > 0 && isEmpty(updatedRows[updatedRows.length - 1])) {
      updatedRows.pop();
    }

    updateSheet({ ...sheet, columns, rows: [...updatedRows, ...newRows] });
    setSelected([]);
    toast({ title: `Added ${newRows.length} articles to ${sheet.name}` });
  };

  if (activeSheet && activeSheet.type === 'custom') {
    return (
      <div className="container mx-auto py-8 px-4 space-y-4">
        <SubpageHeader current="pubmed-sheets" />
        <h1 className="text-3xl font-bold">PubMed Sheets</h1>
        <Tabs value={activeSheet.id} onValueChange={setActiveSheetId} className="mb-4">
          <TabsList className="gap-1">
            {sheets.map((s, idx) => (
              <TabsTrigger
                key={s.id}
                value={s.id}
                className="capitalize border rounded px-2 py-1"
                draggable
                onDragStart={() => handleSheetDragStart(idx)}
                onDragOver={(e) => handleSheetDragOver(e, idx)}
                onContextMenu={(e) => {
                  e.preventDefault();
                  setSheetMenu({ id: s.id, x: e.clientX, y: e.clientY });
                }}
              >
                {s.name}
              </TabsTrigger>
            ))}
            <Button size="sm" variant="ghost" type="button" onClick={addSearchSheet}>
              + Search
            </Button>
            <Button size="sm" variant="ghost" type="button" onClick={addCustomSheet}>
              + Empty
            </Button>
          </TabsList>
        </Tabs>
        <div className="flex flex-wrap gap-2 items-end">
          <Input
            value={newColumnName}
            onChange={(e) => setNewColumnName(e.target.value)}
            placeholder="Column Name"
            className="w-40 h-8 py-1"
          />
          <Button
            size="sm"
            className="h-8"
            type="button"
            onClick={() => {
              if (newColumnName.trim()) {
                addColumn(newColumnName.trim());
                setNewColumnName('');
              }
            }}
          >
            Add Column
          </Button>
          <Button size="sm" className="h-8" type="button" onClick={addRow}>
            Add Row
          </Button>
          <Button size="sm" className="h-8" type="button" onClick={exportCurrentCustom}>
            Export
          </Button>
          <Button
            size="sm"
            className="h-8"
            type="button"
            onClick={() => {
              setHiddenRows({ ...hiddenRows, [activeSheet.id]: [] });
              setHiddenCols({ ...hiddenCols, [activeSheet.id]: [] });
            }}
          >
            Unhide All
          </Button>
        </div>
        {selectedRows.length > 0 && (
          <div className="flex items-center gap-2 my-2">
            <span className="text-sm">Row Color:</span>
            {rowColorPalette.map((c) => (
              <button
                key={c}
                className="w-5 h-5 rounded border"
                style={{ backgroundColor: c }}
                onClick={() => setColorForSelectedRows(c)}
                aria-label={`color ${c}`}
              />
            ))}
            <button onClick={clearColorForSelectedRows} className="text-sm underline">
              Clear
            </button>
          </div>
        )}
        <div className="overflow-x-auto" ref={customTableRef}>
          <Table className="table-fixed">
            <TableHeader>
              <TableRow>
                <TableHead className="w-16 p-1 pr-2">
                  <Checkbox
                    checked={allRowsSelected}
                    onCheckedChange={(v) => toggleRowSelectAll(!!v)}
                  />
                </TableHead>
                <TableHead className="w-10 p-1">Doc</TableHead>
                {activeSheet.columns.map((c, cidx) => (
                  hiddenCols[activeSheet.id]?.includes(c) ? null : (
                  <TableHead
                    key={c}
                    className="capitalize p-1 relative"
                    style={{ width: customColWidths[c] }}
                    data-col-name={c}
                    onContextMenu={(e) => {
                      e.preventDefault();
                      setColumnMenu({
                        sheetId: activeSheet.id,
                        index: cidx,
                        x: e.clientX,
                        y: e.clientY,
                      });
                    }}
                  >
                    <div
                      className="resizable"
                      onMouseDown={startResize(c, true)}
                    >
                      {c}
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <button className="absolute top-0 right-0 p-1">⋮</button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => { const name = window.prompt('Column name'); if(name) insertColumn(cidx, name); }}>Insert Left</DropdownMenuItem>
                        <DropdownMenuItem onClick={() => { const name = window.prompt('Column name', c + ' copy'); if(name) duplicateColumn(cidx, name); }}>Duplicate</DropdownMenuItem>
                        <DropdownMenuItem onClick={() => deleteColumn(cidx)}>Delete</DropdownMenuItem>
                        <DropdownMenuItem onClick={() => setHiddenCols({
                          ...hiddenCols,
                          [activeSheet.id]: [...(hiddenCols[activeSheet.id] || []), c],
                        })}>Hide</DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableHead>
                  )
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {activeSheet.rows.map((r, idx) => {
                if (hiddenRows[activeSheet.id]?.includes(idx)) return null;
                return (
                  <TableRow
                    key={idx}
                    className="[&>*]:p-1"
                    style={{ backgroundColor: rowColors[idx] }}
                    draggable
                    data-state={selectedRows.includes(idx) ? 'selected' : undefined}
                    onDragStart={() => handleRowDragStart(idx)}
                    onDragOver={(e) => handleRowDragOver(e, idx)}
                  >
                    <TableCell className="w-16 p-1 pr-2">
                      <div className="flex items-center">
                        <Checkbox
                          checked={selectedRows.includes(idx)}
                          onClick={(e) => toggleRowSelect(idx, e.shiftKey)}
                          className="mr-2"
                        />
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <button className="p-0">⋮</button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => insertRow(idx)}>Insert Above</DropdownMenuItem>
                            <DropdownMenuItem onClick={() => duplicateRow(idx)}>Duplicate</DropdownMenuItem>
                            <DropdownMenuItem onClick={() => deleteRow(idx)}>Delete</DropdownMenuItem>
                            <DropdownMenuItem onClick={() => setHiddenRows({
                              ...hiddenRows,
                              [activeSheet.id]: [...(hiddenRows[activeSheet.id] || []), idx],
                            })}>Hide</DropdownMenuItem>
                          </DropdownMenuContent>
                    </DropdownMenu>
                    <button onClick={() => openRowPrompt(idx)} aria-label="ai" className="ml-3">
                      <Sparkles className="h-4 w-4 text-purple-500" />
                    </button>
                  </div>
                </TableCell>
                <TableCell className="w-10 p-1">
                  <div
                    onDragOver={(e) => e.preventDefault()}
                    onDrop={(e) => {
                      e.preventDefault();
                      const file = e.dataTransfer.files?.[0];
                      if (file) uploadPdf(file, idx);
                    }}
                    className="flex items-center justify-center"
                  >
                    {pdfLoadingRows.includes(idx) ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : r.pdfText ? (
                      <button onClick={() => setPdfDialogIdx(idx)} aria-label="view text">
                        <FileCheck2 className="h-4 w-4 text-amber-500" />
                      </button>
                    ) : (
                      <label className="cursor-pointer">
                        <FilePlus2 className="h-4 w-4" />
                        <input
                          type="file"
                          accept="application/pdf"
                          className="hidden"
                          onChange={(e) => {
                            const f = e.target.files?.[0];
                            if (f) uploadPdf(f, idx);
                            e.currentTarget.value = '';
                          }}
                        />
                      </label>
                    )}
                  </div>
                </TableCell>
                {activeSheet.columns.map((c) => {
                  if (hiddenCols[activeSheet.id]?.includes(c)) return null;
                  return (
                        <TableCell key={c} className="min-w-32 p-1">
                          <Input
                            value={r[c]}
                            onChange={(e) => updateCell(idx, c, e.target.value)}
                            className="h-6 px-1"
                          />
                        </TableCell>
                      );
                    })}
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
        <Dialog
          open={rowPromptIdx !== null}
          onOpenChange={(open) => {
            if (!open) {
              setRowPromptIdx(null);
              setRowPromptValue("");
              setRowPromptCols([]);
            }
          }}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Ask AI About This Row</DialogTitle>
            </DialogHeader>
            <div className="space-y-2">
              <div className="flex flex-wrap gap-2">
                {activeSheet.columns.map((c) => (
                  <label key={c} className="flex items-center gap-1">
                    <Checkbox
                      checked={rowPromptCols.includes(c)}
                      onCheckedChange={(v) => {
                        const checked = !!v;
                        setRowPromptCols((prev) =>
                          checked ? [...prev, c] : prev.filter((p) => p !== c),
                        );
                      }}
                    />
                    {c}
                  </label>
                ))}
              </div>
              <Textarea
                value={rowPromptValue}
                onChange={(e) => setRowPromptValue(e.target.value)}
                placeholder="Enter prompt"
              />
              <Input
                value={rowPromptOutput}
                onChange={(e) => setRowPromptOutput(e.target.value)}
                placeholder="Output column"
              />
            </div>
            <DialogFooter className="mt-2">
              <Button onClick={runRowPrompt} disabled={rowPromptLoading}>
                {rowPromptLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  "Run"
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        <Dialog
          open={pdfDialogIdx !== null}
          onOpenChange={(open) => {
            if (!open) setPdfDialogIdx(null);
          }}
        >
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>PDF Text</DialogTitle>
            </DialogHeader>
            <Textarea
              value={pdfDialogIdx !== null ? activeSheet.rows[pdfDialogIdx].pdfText || '' : ''}
              onChange={(e) => {
                if (pdfDialogIdx === null) return;
                const rows = activeSheet.rows.map((r, i) =>
                  i === pdfDialogIdx ? { ...r, pdfText: e.target.value } : r,
                );
                updateSheet({ ...activeSheet, rows });
              }}
              className="min-h-[60vh]"
            />
            <DialogFooter className="mt-2">
              <Button
                variant="destructive"
                onClick={() => {
                  if (pdfDialogIdx === null) return;
                  const rows = activeSheet.rows.map((r, i) =>
                    i === pdfDialogIdx ? { ...r, pdfText: '' } : r,
                  );
                  updateSheet({ ...activeSheet, rows });
                  setPdfDialogIdx(null);
                }}
              >
                Delete
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4 space-y-4">
      <SubpageHeader current="pubmed-sheets" />
      <h1 className="text-3xl font-bold">PubMed Sheets</h1>
      <Tabs value={activeSheet?.id} onValueChange={setActiveSheetId} className="mb-4">
        <TabsList className="gap-1">
          {sheets.map((s, idx) => (
            <TabsTrigger
              key={s.id}
              value={s.id}
              className="capitalize border rounded px-2 py-1"
              draggable
              onDragStart={() => handleSheetDragStart(idx)}
              onDragOver={(e) => handleSheetDragOver(e, idx)}
              onContextMenu={(e) => {
                e.preventDefault();
                setSheetMenu({ id: s.id, x: e.clientX, y: e.clientY });
              }}
            >
              {s.name}
            </TabsTrigger>
          ))}
          <Button size="sm" variant="ghost" type="button" onClick={addSearchSheet}>
            + Search
          </Button>
          <Button size="sm" variant="ghost" type="button" onClick={addCustomSheet}>
            + Empty
          </Button>
        </TabsList>
      </Tabs>
      <form onSubmit={handleSearch} className="flex flex-wrap gap-2 items-end w-full">
        <div className="flex-1 min-w-[300px]">
        <Input
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Search PubMed"
          className="w-full h-9"
        />
        </div>
        <Input
          type="number"
          placeholder="Start Year"
          value={startYear}
          onChange={(e) => setStartYear(e.target.value)}
          className="w-28"
        />
        <Input
          type="number"
          placeholder="End Year"
          value={endYear}
          onChange={(e) => setEndYear(e.target.value)}
          className="w-28"
        />
        <Button
          type="submit"
          disabled={loading}
          className="bg-amber-400 text-black hover:bg-amber-500 w-32 flex justify-center"
        >
          {loading ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black" />
          ) : (
            <>
              <Search className="h-4 w-4 mr-2" /> Search
            </>
          )}
        </Button>
        <Button
          type="button"
          variant="secondary"
          onClick={generateQueries}
          className="gap-1"
          disabled={genLoading}
        >
          {genLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Sparkles className="h-4 w-4" />
          )}
          Generate Additional Queries
        </Button>
        <Button
          type="button"
          variant="secondary"
          onClick={generateMeshQueries}
          className="gap-1"
          disabled={meshLoading}
        >
          {meshLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Sparkles className="h-4 w-4" />
          )}
          Generate MeSH Queries
        </Button>
        <Button type="button" variant="outline" onClick={saveCurrentSearch} className="gap-1">
          <Save className="h-4 w-4" /> Save
        </Button>
      </form>
      {optSuggestions.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {optSuggestions.map((s, idx) => (
            <Button key={`opt-${idx}`} size="sm" onClick={() => searchWith(s)}>
              {s}
            </Button>
          ))}
        </div>
      )}
      {meshSuggestions.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {meshSuggestions.map((s, idx) => (
            <Button key={`mesh-${idx}`} size="sm" onClick={() => searchWith(s)}>
              {s}
            </Button>
          ))}
        </div>
      )}
      {savedSearches.length > 0 && (
        <div className="mb-4">
          <h2 className="text-xl font-bold mb-2">Saved Searches</h2>
          <div className="flex flex-wrap gap-2">
            {savedSearches.map((s, idx) => (
              <div key={idx} className="flex items-center gap-1 border rounded px-2 py-0">
                <Button variant="link" className="p-0 text-sm" onClick={() => loadSavedSearch(s)}>
                  {s.query}
                </Button>
                <button onClick={() => removeSavedSearch(idx)} className="text-muted-foreground hover:text-red-500">
                  <X className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
      <h2 className="text-xl font-bold">Filters</h2>
      <div className="space-y-2">
        <div className="flex flex-wrap gap-2 items-end">
          <Select value={newMode} onValueChange={(v) => setNewMode(v as "include" | "exclude")}>
            <SelectTrigger className="w-[100px] h-9">
              <SelectValue placeholder="Mode" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="include">Include</SelectItem>
              <SelectItem value="exclude">Exclude</SelectItem>
            </SelectContent>
          </Select>
          <div className="flex-1 min-w-[200px]">
          <Input
            className="h-9 w-full"
            placeholder={`Enter words or phrases, separated by a comma, to ${newMode}`}
            value={keywordInput}
            onChange={(e) => setKeywordInput(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                addFilter();
              }
            }}
          />
          </div>
          <div className="flex flex-wrap gap-1">
            {columnOptions.map((col) => (
              <Button
                key={col}
                size="sm"
                variant={newColumns.includes(col) ? 'default' : 'outline'}
                onClick={() =>
                  setNewColumns((prev) =>
                    prev.includes(col)
                      ? prev.filter((c) => c !== col)
                      : [...prev, col],
                  )
                }
              >
                <span className="capitalize">{col === 'pubdate' ? 'Pub Date' : col}</span>
              </Button>
            ))}
          </div>
          <Button type="button" onClick={addFilter} size="sm">
            Add Filter
          </Button>
          {filterLoading && <Loader2 className="h-4 w-4 animate-spin" />}
        </div>
        {filters.length > 0 && (
          <ul className="flex flex-wrap gap-2">
            {filters.map((f, idx) => (
              <li
                key={idx}
                className="border rounded px-2 py-1 flex items-center gap-1 text-sm"
                draggable
                onDragStart={() => handleFilterDragStart(idx)}
                onDragOver={(e) => handleFilterDragOver(e, idx)}
              >
                <span className="cursor-move p-1 hover:bg-muted/50 rounded">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="12"
                    height="12"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <circle cx="9" cy="5" r="1" />
                    <circle cx="9" cy="12" r="1" />
                    <circle cx="9" cy="19" r="1" />
                    <circle cx="15" cy="5" r="1" />
                    <circle cx="15" cy="12" r="1" />
                    <circle cx="15" cy="19" r="1" />
                  </svg>
                </span>
                <span>
                  {idx + 1}. {f.mode === 'include' ? 'Include' : 'Exclude'}: {f.keywords.join(', ')} [{f.columns.join(', ')}]
                </span>
                <button
                  onClick={() => removeFilter(idx)}
                  className="text-muted-foreground hover:text-red-500"
                  aria-label="remove"
                >
                  <X className="h-3 w-3" />
                </button>
              </li>
            ))}
          </ul>
        )}
        {chartData.length > 1 && (
          <div className="space-y-2">
            <div className="flex flex-wrap items-center gap-2">
              <Button
                variant={chartType === 'bar' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setChartType('bar')}
                aria-label="Bar"
              >
                <BarChart3 className="h-4 w-4" />
              </Button>
              <Button
                variant={chartType === 'line' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setChartType('line')}
                aria-label="Line"
              >
                <LineChartIcon className="h-4 w-4" />
              </Button>
              <div className="flex items-center gap-1">
                <span className="text-sm">W</span>
                <input
                  type="range"
                  min={baseChartWidth}
                  max={tableWidth || baseChartWidth}
                  value={chartWidthUser}
                  onChange={(e) => setChartWidthUser(Number(e.target.value))}
                  className="h-2"
                />
              </div>
              <div className="flex items-center gap-1">
                <span className="text-sm">H</span>
                <input
                  type="range"
                  min={100}
                  max={maxChartHeight}
                  value={chartHeight}
                  onChange={(e) => setChartHeight(Number(e.target.value))}
                  className="h-2"
                />
              </div>
            </div>
            {chartType === 'bar' ? (
              <ReBarChart
                width={chartWidth}
                height={chartHeight}
                data={chartData}
                onClick={(state) =>
                  handleChartClick((state as any).activeTooltipIndex ?? 0)
                }
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#8884d8" />
              </ReBarChart>
            ) : (
              <ReLineChart
                width={chartWidth}
                height={chartHeight}
                data={chartData}
                onClick={(state) =>
                  handleChartClick((state as any).activeTooltipIndex ?? 0)
                }
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="count" stroke="#8884d8" />
              </ReLineChart>
            )}
          </div>
        )}
      </div>
      {bookmarks.length > 0 && (
        <div className="space-y-1">
          <h2 className="text-lg font-semibold">Saved Articles</h2>
          <ul className="list-disc pl-0 space-y-1">
            {bookmarks.map((b) => (
              <li key={b.id} className="flex items-center gap-1 text-sm">
                <a
                  href={`https://pubmed.ncbi.nlm.nih.gov/${b.id}/`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="underline"
                >
                  {b.title}
                </a>
                <button
                  onClick={() => toggleBookmark(b)}
                  className="text-muted-foreground hover:text-red-500"
                  aria-label="remove"
                >
                  <X className="h-3 w-3" />
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}
      {totalCount !== null && (
        <p className="text-sm text-muted-foreground mt-4">
          {totalCount} articles found
          {filteredCount !== null && filteredCount !== totalCount && (
            <> — {filteredCount} after filters ({((filteredCount / totalCount) * 100).toFixed(1)}%)</>
          )}
        </p>
      )}

      {compareItems.length > 1 && (
        <div ref={comparisonRef} className="my-8 overflow-auto space-y-2">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold">Comparison</h2>
            <div className="flex gap-2 items-center">
              <Button variant="outline" size="sm" onClick={exportComparisonCsv} className="gap-1">
                <Download className="h-4 w-4" /> Export
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={summarizeComparison}
                disabled={compareLoading}
                className="gap-1"
              >
                {compareLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" /> Loading...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4" /> Summarize
                  </>
                )}
              </Button>
              <Button variant="outline" size="sm" onClick={clearComparison}>
                Clear
              </Button>
            </div>
          </div>
          <table className="min-w-full text-sm border-2 border-gray-300 dark:border-gray-600 rounded-md overflow-hidden">
            <thead className="bg-muted">
              <tr>
                <th className="border border-gray-300 dark:border-gray-600 px-3 py-2 w-32 text-left">Title</th>
                {compareItems.map((c) => (
                  <th key={c.id} className="border border-gray-300 dark:border-gray-600 px-3 py-2 w-60 text-left">
                    <div className="flex items-start justify-between">
                      <span>{c.title}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 px-2"
                        onClick={() => toggleCompare(c.id)}
                        aria-label="Remove from comparison"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-border">
              <tr>
                <td className="border border-gray-300 dark:border-gray-600 px-3 py-2 font-medium w-32">Journal</td>
                {compareItems.map((c) => (
                  <td key={c.id} className="border border-gray-300 dark:border-gray-600 px-3 py-2 w-60">{c.journal}</td>
                ))}
              </tr>
              <tr>
                <td className="border border-gray-300 dark:border-gray-600 px-3 py-2 font-medium w-32">Pub Date</td>
                {compareItems.map((c) => (
                  <td key={c.id} className="border border-gray-300 dark:border-gray-600 px-3 py-2 w-60">{c.pubdate}</td>
                ))}
              </tr>
              <tr>
                <td className="border border-gray-300 dark:border-gray-600 px-3 py-2 font-medium w-32">PMID</td>
                {compareItems.map((c) => (
                  <td key={c.id} className="border border-gray-300 dark:border-gray-600 px-3 py-2 w-60">
                    <a
                      href={`https://pubmed.ncbi.nlm.nih.gov/${c.id}/`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-amber-600 underline"
                    >
                      {c.id}
                    </a>
                    {pdfLinks[c.id] && (
                      <a
                        href={pdfLinks[c.id]!}
                        target="_blank"
                        rel="noopener noreferrer"
                        aria-label="Open PDF"
                        className="text-primary hover:text-primary/80 inline-block ml-1"
                        title="View PDF (PubMed Central)"
                      >
                        <FileText className="h-4 w-4 inline-block align-text-bottom" />
                      </a>
                    )}
                    {!pdfLinks[c.id] && pubmedLinks[c.id]?.pdf && (
                      <a
                        href={pubmedLinks[c.id]!.pdf!}
                        target="_blank"
                        rel="noopener noreferrer"
                        aria-label="Open PDF"
                        className="text-green-700 hover:text-green-800 inline-block ml-1"
                        title="View PDF"
                      >
                        <FileText className="h-4 w-4 inline-block align-text-bottom" />
                      </a>
                    )}
                    {!pdfLinks[c.id] && !pubmedLinks[c.id]?.pdf && pubmedLinks[c.id]?.link && (
                      <a
                        href={pubmedLinks[c.id]!.link!}
                        target="_blank"
                        rel="noopener noreferrer"
                        aria-label="Open link"
                        className="text-muted-foreground hover:text-primary inline-block ml-1"
                        title="Visit full text page"
                      >
                        <ExternalLink className="h-4 w-4 inline-block align-text-bottom" />
                      </a>
                    )}
                  </td>
                ))}
              </tr>
              <tr>
                <td className="border border-gray-300 dark:border-gray-600 px-3 py-2 font-medium w-32">Authors</td>
                {compareItems.map((c) => (
                  <td key={c.id} className="border border-gray-300 dark:border-gray-600 px-3 py-2 w-60">{c.authors}</td>
                ))}
              </tr>
              <tr>
                <td className="border border-gray-300 dark:border-gray-600 px-3 py-2 font-medium w-32">Abstract</td>
                {compareItems.map((c) => (
                  <td key={c.id} className="border border-gray-300 dark:border-gray-600 px-3 py-2 w-60">
                    {c.abstract && c.abstract.length > 250 && !expandedCompareIds.includes(c.id) ? (
                      <>
                        {c.abstract.substring(0, 250)}...
                        <Button variant="link" size="sm" className="h-4 px-1" onClick={() => toggleCompareAbstract(c.id)}>
                          Show More
                        </Button>
                      </>
                    ) : (
                      <>
                        {c.abstract}
                        {c.abstract && c.abstract.length > 250 && (
                          <Button variant="link" size="sm" className="h-4 px-1" onClick={() => toggleCompareAbstract(c.id)}>
                            Show Less
                          </Button>
                        )}
                      </>
                    )}
                  </td>
                ))}
              </tr>
            </tbody>
          </table>
          {comparisonSummary && (
            <div className="mt-4 p-2 rounded-md bg-blue-50 dark:bg-blue-900/40 relative">
              <button
                onClick={() => setComparisonSummary("")}
                className="absolute top-1 right-1 text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-800 rounded-full p-1"
                aria-label="Close comparison summary"
              >
                <X className="h-3 w-3" />
              </button>
              <p className="font-semibold text-blue-700 dark:text-blue-300 flex items-center gap-1 mb-1">
                <Sparkles className="h-4 w-4" /> Comparison Summary
              </p>
              <ul className="list-[circle] pl-4 text-sm text-blue-700 dark:text-blue-300">
                {parseBullets(comparisonSummary).map((b, i) => (
                  <li key={i}>{b}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}


      {filteredResultsState.length > 0 && (
        <div className="space-y-2">
          <div className="flex items-center justify-between gap-2">
          <div className="flex gap-2">
            <Button variant="outline" onClick={exportSavedCsv} className="gap-1" disabled={exportingSaved} size="sm">
              {exportingSaved ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" /> Preparing...
                </>
              ) : (
                  <>Export Saved Articles</>
              )}
            </Button>
            <Button variant="outline" onClick={exportAllCsv} className="gap-1" disabled={exportingAll} size="sm">
              {exportingAll ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" /> Preparing...
                </>
              ) : (
                  <>Export All Articles</>
              )}
            </Button>
          </div>
          <div className="flex gap-2 items-center ml-auto">
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="gap-1" size="sm">Columns</Button>
              </PopoverTrigger>
              <PopoverContent align="end" className="w-48 space-y-2">
                {Object.entries(columnVisibility).map(([key, value]) => (
                  <label key={key} className="flex items-center gap-2 text-sm">
                    <Checkbox
                      checked={value}
                      onCheckedChange={() => toggleColumn(key as keyof typeof columnVisibility)}
                      id={`col-${key}`}
                    />
                    <span className="capitalize">
                      {key === 'pubdate' ? 'Pub Date' : key === 'aiOutput' ? 'AI Output' : key}
                    </span>
                  </label>
                ))}
              </PopoverContent>
            </Popover>
            {customSheets.length > 0 && selected.length > 0 && (
              <>
                <Button variant="outline" onClick={addSelectedToCustom} size="sm">
                  Save selected to
                </Button>
                <Select value={targetCustomId} onValueChange={setTargetCustomId}>
                  <SelectTrigger
                    className="w-[120px] h-9"
                    onContextMenu={(e) => {
                      e.preventDefault();
                      if (targetCustomId) {
                        setTargetSheetMenu({
                          id: targetCustomId,
                          x: e.clientX,
                          y: e.clientY,
                        });
                      }
                    }}
                  >
                    <SelectValue placeholder="Select sheet" />
                  </SelectTrigger>
                  <SelectContent>
                    {customSheets.map((s) => (
                      <SelectItem key={s.id} value={s.id}>
                        {s.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </>
            )}
            {showSingleOutput && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setAiOutputVisible(!aiOutputVisible)}
              >
                {aiOutputVisible ? 'Hide AI Output' : 'Show AI Output'}
              </Button>
            )}
          </div>
        </div>
          <div className="overflow-x-auto" ref={tableRef}>
          <Table className={showAiColumn ? "min-w-[1200px]" : "w-full"}>
            <TableHeader>
              <TableRow className="[&>*]:py-2">
                <TableHead className="w-4 px-2">
                  <Checkbox
                    checked={allSelected}
                    onCheckedChange={(v) => toggleSelectAll(!!v)}
                  />
                </TableHead>
                <TableHead className="w-4 px-2" />
                <TableHead className="w-4 px-2" />
                <TableHead className="w-4 px-2" />
                {columnVisibility.title && (
                  <TableHead
                    className="w-1/4 px-2"
                    style={{ width: searchColWidths.title }}
                    data-col-name="title"
                  >
                    <div className="resizable" onMouseDown={startResize('title', false)}>Title</div>
                  </TableHead>
                )}
                {columnVisibility.journal && (
                  <TableHead
                    className="w-1/6 px-2"
                    style={{ width: searchColWidths.journal }}
                    data-col-name="journal"
                  >
                    <div className="resizable" onMouseDown={startResize('journal', false)}>Journal</div>
                  </TableHead>
                )}
                {columnVisibility.pubdate && (
                  <TableHead
                    className="w-24 px-2"
                    style={{ width: searchColWidths.pubdate }}
                    data-col-name="pubdate"
                  >
                    <div className="resizable" onMouseDown={startResize('pubdate', false)}>Pub Date</div>
                  </TableHead>
                )}
                {columnVisibility.authors && (
                  <TableHead
                    className="w-1/5 px-2"
                    style={{ width: searchColWidths.authors }}
                    data-col-name="authors"
                  >
                    <div className="resizable" onMouseDown={startResize('authors', false)}>Authors</div>
                  </TableHead>
                )}
                {columnVisibility.affiliations && (
                  <TableHead
                    className="w-1/5 px-2"
                    style={{ width: searchColWidths.affiliations }}
                    data-col-name="affiliations"
                  >
                    <div className="resizable" onMouseDown={startResize('affiliations', false)}>Affiliations</div>
                  </TableHead>
                )}
                {columnVisibility.sponsor && (
                  <TableHead
                    className="w-1/5 px-2"
                    style={{ width: searchColWidths.sponsor }}
                    data-col-name="sponsor"
                  >
                    <div className="resizable" onMouseDown={startResize('sponsor', false)}>Sponsor</div>
                  </TableHead>
                )}
                {columnVisibility.abstract && (
                  <TableHead
                    className="w-1/3 px-2"
                    style={{ width: searchColWidths.abstract }}
                    data-col-name="abstract"
                  >
                    <div className="resizable" onMouseDown={startResize('abstract', false)}>Abstract</div>
                  </TableHead>
                )}
                {showAiColumn && (
                  <TableHead
                    className="min-w-[32rem] px-2"
                    style={{ width: searchColWidths.aiOutput }}
                    data-col-name="aiOutput"
                  >
                    <div className="resizable" onMouseDown={startResize('aiOutput', false)}>AI Output</div>
                  </TableHead>
                )}
              </TableRow>
            </TableHeader>
            <TableBody>
              {visibleResults.map((r, idx) => {
                const globalIdx = (displayPage - 1) * pageSize + idx;
                return (
                  <TableRow
                    key={r.id}
                    className={`border-b [&>*]:py-2 ${interactedIds.includes(r.id) ? 'bg-muted' : ''}`}
                  >
                      <TableCell className="px-2">
                        <Checkbox
                          checked={selected.includes(r.id)}
                          onClick={(e) =>
                            toggleSelect(r.id, globalIdx, e.shiftKey)
                          }
                        />
                  </TableCell>
                  <TableCell className="px-2">
                    <button onClick={() => toggleBookmark(r)} aria-label="bookmark">
                      {bookmarks.some((b) => b.id === r.id) ? (
                        <BookmarkCheck className="h-4 w-4 text-amber-500" />
                      ) : (
                        <Bookmark className="h-4 w-4" />
                      )}
                    </button>
                  </TableCell>
                  <TableCell className="px-2">
                    <button onClick={() => toggleCompare(r.id)} aria-label="compare">
                      <GitCompare className={`h-4 w-4 ${compareIds.includes(r.id) ? 'text-amber-500' : ''}`} />
                    </button>
                  </TableCell>
                  <TableCell className="px-2">
                    <button onClick={() => openPromptDialog(r)} aria-label="prompt">
                      <Sparkles className="h-4 w-4 text-purple-500" />
                    </button>
                  </TableCell>
                  {columnVisibility.title && (
                    <TableCell className="prose max-w-xs md:max-w-sm lg:max-w-md px-2 break-words">
                      <a
                        href={`https://pubmed.ncbi.nlm.nih.gov/${r.id}/`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="underline"
                        onClick={() => markInteracted(r.id)}
                      >
                        {r.title}
                      </a>
                      {pdfLinks[r.id] && (
                        <a
                          href={pdfLinks[r.id]!}
                          target="_blank"
                          rel="noopener noreferrer"
                          aria-label="Open PDF"
                          className="text-primary hover:text-primary/80 inline-block ml-1"
                          title="View PDF (PubMed Central)"
                        >
                          <FileText className="h-4 w-4 inline-block align-text-bottom" />
                        </a>
                      )}
                      {!pdfLinks[r.id] && pubmedLinks[r.id]?.pdf && (
                        <a
                          href={pubmedLinks[r.id]!.pdf!}
                          target="_blank"
                          rel="noopener noreferrer"
                          aria-label="Open PDF"
                          className="text-green-700 hover:text-green-800 inline-block ml-1"
                          title="View PDF"
                        >
                          <FileText className="h-4 w-4 inline-block align-text-bottom" />
                        </a>
                      )}
                      {!pdfLinks[r.id] && !pubmedLinks[r.id]?.pdf && pubmedLinks[r.id]?.link && (
                        <a
                          href={pubmedLinks[r.id]!.link!}
                          target="_blank"
                          rel="noopener noreferrer"
                          aria-label="Open link"
                          className="text-muted-foreground hover:text-primary inline-block ml-1"
                          title="Visit full text page"
                        >
                          <ExternalLink className="h-4 w-4 inline-block align-text-bottom" />
                        </a>
                      )}
                    </TableCell>
                  )}
                  {columnVisibility.journal && (
                    <TableCell className="px-2 max-w-xs break-words">{r.journal}</TableCell>
                  )}
                  {columnVisibility.pubdate && (
                    <TableCell className="px-2 whitespace-nowrap">{r.pubdate}</TableCell>
                  )}
                  {columnVisibility.authors && (
                    <TableCell className="px-2 max-w-xs break-words">{r.authors}</TableCell>
                  )}
                  {columnVisibility.affiliations && (
                    <TableCell className="px-2 max-w-xs break-words">{r.affiliations}</TableCell>
                  )}
                  {columnVisibility.sponsor && (
                    <TableCell className="px-2 max-w-xs break-words">{r.sponsor}</TableCell>
                  )}
                  {columnVisibility.abstract && (
                    <TableCell className="px-2 max-w-2xl whitespace-pre-wrap">
                    {r.abstract && r.abstract.length > 250 && !expandedIds.includes(r.id) ? (
                      <>
                        {r.abstract.substring(0, 250)}...
                        <Button
                          variant="link"
                          size="sm"
                          className="h-4 px-1"
                          onClick={() => toggleAbstract(r.id)}
                        >
                          Show More
                        </Button>
                      </>
                    ) : (
                      <>
                        {r.abstract}
                        {r.abstract && r.abstract.length > 250 && (
                          <Button
                            variant="link"
                            size="sm"
                            className="h-4 px-1"
                            onClick={() => toggleAbstract(r.id)}
                          >
                            Show Less
                          </Button>
                        )}
                      </>
                    )}
                  </TableCell>
                  )}
                  {showAiColumn && (
                    <TableCell className="px-2 max-w-3xl whitespace-pre-wrap text-xs flex items-start gap-1">
                      <div className="flex-1" dangerouslySetInnerHTML={{ __html: simpleMarkdown(singleResponses[r.id] || "") }} />
                      {singleResponses[r.id] && (
                        <button
                          onClick={() => copyToClipboard(singleResponses[r.id])}
                          aria-label="Copy AI output"
                          className="text-muted-foreground hover:text-primary"
                        >
                          <Copy className="h-4 w-4" />
                        </button>
                      )}
                    </TableCell>
                  )}
                </TableRow>
                );
              })}
            </TableBody>
          </Table>
          </div>
        </div>
      )}


      {filteredResultsState.length > 0 && (
        <div className="flex items-center justify-between flex-wrap gap-2 mt-2">
          <div className="flex gap-1 flex-wrap mx-auto">
            <Button
              size="sm"
              variant="outline"
              onClick={() => changePage(1)}
              disabled={displayPage === 1}
            >
              «
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => changePage(displayPage - 1)}
              disabled={displayPage === 1}
            >
              ‹
            </Button>
            {paginationPages.map((p, idx) =>
              p === -1 ? (
                <span key={`ellipsis-${idx}`} className="px-2">…</span>
              ) : (
                <Button
                  key={`page-${p}`}
                  size="sm"
                  variant={displayPage === p ? "default" : "outline"}
                  onClick={() => changePage(p)}
                >
                  {p}
                </Button>
              ),
            )}
            <Button
              size="sm"
              variant="outline"
              onClick={() => changePage(displayPage + 1)}
              disabled={displayPage === totalPages}
            >
              ›
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => changePage(totalPages)}
              disabled={displayPage === totalPages}
            >
              »
            </Button>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm">Rows per page:</span>
            <Select
              value={String(pageSize)}
              onValueChange={(v) => handlePageSizeChange(Number(v))}
            >
              <SelectTrigger className="w-[80px] h-9">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {[20, 40, 60, 80, 100].map((n) => (
                  <SelectItem key={n} value={String(n)}>
                    {n}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      )}
      <Dialog
        open={!!promptArticle}
        onOpenChange={(open) => {
          if (!open) {
            setPromptArticle(null);
            setPromptValue("");
          }
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Ask AI About This Article</DialogTitle>
          </DialogHeader>
          <div className="space-y-2">
            <Textarea
              value={promptValue}
              onChange={(e) => setPromptValue(e.target.value)}
              placeholder="Enter prompt"
            />
            {recentPrompts.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {recentPrompts.map((p, idx) => (
                  <Button
                    key={idx}
                    size="sm"
                    variant="secondary"
                    onClick={() => setPromptValue(p)}
                  >
                    {p.length > 60 ? `${p.slice(0, 57)}...` : p}
                  </Button>
                ))}
              </div>
            )}
          </div>
          <DialogFooter className="mt-2">
            <Button onClick={submitPrompt} disabled={promptLoading}>
              {promptLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : "Run"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      {compareItems.length > 1 && (
        <Button
          onClick={scrollToComparison}
          className="fixed bottom-4 right-4 z-50 rounded-full bg-amber-400 text-black hover:bg-amber-500 shadow-md"
          size="sm"
        >
          View Comparison
        </Button>
      )}
      {sheetMenu && (
        <div
          className="fixed z-50 bg-popover border rounded shadow p-1 text-sm"
          style={{ top: sheetMenu.y, left: sheetMenu.x }}
        >
          <button
            className="block w-full text-left px-2 py-1 hover:bg-muted"
            onClick={() => {
              renameSheet(sheetMenu.id);
              setSheetMenu(null);
            }}
          >
            Rename
          </button>
          {sheets.length > 1 && (
            <button
              className="block w-full text-left px-2 py-1 hover:bg-muted"
              onClick={() => {
                deleteSheet(sheetMenu.id);
                setSheetMenu(null);
              }}
            >
              Delete
            </button>
          )}
        </div>
      )}
      {columnMenu && (
        <div
          className="fixed z-50 bg-popover border rounded shadow p-1 text-sm"
          style={{ top: columnMenu.y, left: columnMenu.x }}
        >
          <button
            className="block w-full text-left px-2 py-1 hover:bg-muted"
            onClick={() => {
              const name = window.prompt('Column name');
              if (name) insertColumn(columnMenu.index, name);
              setColumnMenu(null);
            }}
          >
            Insert Left
          </button>
          <button
            className="block w-full text-left px-2 py-1 hover:bg-muted"
            onClick={() => {
              deleteColumn(columnMenu.index);
              setColumnMenu(null);
            }}
          >
            Delete
          </button>
          <button
            className="block w-full text-left px-2 py-1 hover:bg-muted"
            onClick={() => {
              const sheet = sheets.find((s) => s.id === columnMenu.sheetId);
              if (!sheet || sheet.type !== "custom") return;
              const col = sheet.columns[columnMenu.index];
              if (!col) return;
              setHiddenCols({
                ...hiddenCols,
                [columnMenu.sheetId]: [
                  ...(hiddenCols[columnMenu.sheetId] || []),
                  col,
                ],
              });
              setColumnMenu(null);
            }}
          >
            Hide
          </button>
        </div>
      )}
      {targetSheetMenu && (
        <div
          className="fixed z-50 bg-popover border rounded shadow p-1 text-sm"
          style={{ top: targetSheetMenu.y, left: targetSheetMenu.x }}
        >
          <button
            className="block w-full text-left px-2 py-1 hover:bg-muted"
            onClick={() => {
              renameSheet(targetSheetMenu.id);
              setTargetSheetMenu(null);
            }}
          >
            Rename
          </button>
        </div>
      )}
    </div>
  );
}
