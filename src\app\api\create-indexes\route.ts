import { connectToDatabase } from '@/lib/mongodb';
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    const { db } = await connectToDatabase();
    
    // Create indexes for vendor_articles collection
    await db.collection('vendor_articles').createIndex({ vendor: 1 });
    await db.collection('vendor_articles').createIndex({ OriginalAffiliation: 1 });
    await db.collection('vendor_articles').createIndex({ Title: 1 });
    
    // Create text index for full-text search
    await db.collection('vendor_articles').createIndex({
      vendor: "text",
      OriginalAffiliation: "text",
      Title: "text",
      Abstract: "text"
    });
    
    return NextResponse.json({ success: true, message: 'Indexes created successfully' });
  } catch (error) {
    console.error('Error creating indexes:', error);
    return NextResponse.json(
      { error: 'Failed to create indexes' },
      { status: 500 }
    );
  }
}
