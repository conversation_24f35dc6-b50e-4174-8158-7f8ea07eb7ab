import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const id = request.nextUrl.searchParams.get('id');
  const type = request.nextUrl.searchParams.get('type') || 'pmid';

  if (!id) {
    return NextResponse.json({ error: 'id parameter required' }, { status: 400 });
  }

  try {
    const url =
      type === 'work'
        ? `https://api.openalex.org/works/${id}?mailto=<EMAIL>`
        : `https://api.openalex.org/works/pmid:${id}?mailto=<EMAIL>`;
    const res = await fetch(url);
    if (!res.ok) throw new Error('OpenAlex request failed');
    const data = await res.json();
    return NextResponse.json(data, { headers: { 'Cache-Control': 'no-store' } });
  } catch (err) {
    console.error('OpenAlex fetch error', err);
    return NextResponse.json({ error: 'Failed to fetch OpenAlex' }, { status: 500 });
  }
}
