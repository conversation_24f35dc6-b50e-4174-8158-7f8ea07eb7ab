import { NextRequest, NextResponse } from 'next/server'
import { exec } from 'child_process'
import { promisify } from 'util'
import fs from 'fs'
import path from 'path'
import os from 'os'
import crypto from 'crypto'

const execAsync = promisify(exec)

export async function POST(req: NextRequest) {
  try {
    const contentType = req.headers.get('content-type') || ''
    let bytes: ArrayBuffer | null = null

    if (contentType.includes('multipart/form-data')) {
      const formData = await req.formData()
      const file = formData.get('file') as Blob | File | null
      if (!file) {
        return NextResponse.json({ error: 'Missing file' }, { status: 400 })
      }
      bytes = await file.arrayBuffer()
    } else {
      const { url } = await req.json() as { url?: string }
      if (!url) {
        return NextResponse.json({ error: 'Missing url or file' }, { status: 400 })
      }
      const res = await fetch(url)
      if (!res.ok) {
        return NextResponse.json({ error: 'Failed to download PDF' }, { status: res.status })
      }
      bytes = await res.arrayBuffer()
    }

    if (!bytes) {
      return NextResponse.json({ error: 'Failed to read PDF' }, { status: 400 })
    }
    const tmpDir = await fs.promises.mkdtemp(path.join(os.tmpdir(), 'pdf-'))
    const filePath = path.join(tmpDir, `${crypto.randomUUID()}.pdf`)
    await fs.promises.writeFile(filePath, Buffer.from(bytes))

    const scriptPath = path.join(process.cwd(), 'scripts', 'pdf_to_markdown.py')
    let stdout = ''
    try {
      ;({ stdout } = await execAsync(`python "${scriptPath}" "${filePath}"`, {
        maxBuffer: 50 * 1024 * 1024,
      }))
    } catch (err: any) {
      console.error('pdf-to-markdown exec error:', err)
      return NextResponse.json(
        { error: 'Failed to convert PDF. Ensure pymupdf4llm is installed.' },
        { status: 500 },
      )
    }

    await fs.promises.unlink(filePath)
    await fs.promises.rm(tmpDir, { recursive: true, force: true })

    // Create a preview of the converted text (first 500 characters)
    const preview = stdout.substring(0, 500) + (stdout.length > 500 ? '...' : '')

    return NextResponse.json({ 
      text: stdout,
      preview: preview,
      totalLength: stdout.length
    })
  } catch (error) {
    console.error('pdf-to-markdown error:', error)
    return NextResponse.json({ error: 'Failed to convert PDF' }, { status: 500 })
  }
}
