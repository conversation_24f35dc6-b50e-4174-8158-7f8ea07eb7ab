"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";

interface MessageTemplateFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onApplyTemplate: (message: string, subject: string) => void;
}

export function MessageTemplateForm({ open, onOpenChange, onApplyTemplate }: MessageTemplateFormProps) {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    workTitle: "",
    companyName: "",
    vendorContact: "",
    projectType: "",
    asset: "",
    therapeuticArea: "",
    objectives: "",
    geographicalScope: "",
    deliverables: "",
    intendedUse: "",
    timeline: "",
    budgetRange: "",
    timeframe: "",
    fullName: "",
    title: "",
    email: "",
    phone: ""
  });

  // Add these states for custom input options
  const [useCustomProjectType, setUseCustomProjectType] = useState(false);
  const [useCustomBudgetRange, setUseCustomBudgetRange] = useState(false);
  const [useCustomGeoScope, setUseCustomGeoScope] = useState(false);
  const [useCustomIntendedUse, setUseCustomIntendedUse] = useState(false);
  const [useCustomTimeline, setUseCustomTimeline] = useState(false);
  const [useCustomTimeframe, setUseCustomTimeframe] = useState(false);

  // Dispatch custom event when dialog opens/closes
  useEffect(() => {
    // Dispatch custom event for other components to react to dialog state
    const event = new CustomEvent('template-form-state', { 
      detail: { open } 
    });
    window.dispatchEvent(event);
  }, [open]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const generateMessage = () => {
    // Make sure we have a valid project type or use a default
    const projectTypeText = formData.projectType || "Project";
    
    // Create the subject line
    const subject = `Request for Proposal/Information – ${projectTypeText} Support`;
    
    // Message without the subject line at the top
    const message = `Dear ${formData.vendorContact},

I'm a ${formData.workTitle} reaching out on behalf of ${formData.companyName} regarding a potential opportunity to collaborate on an upcoming project focused on ${formData.projectType}.

We are currently looking for support and would like to understand your capabilities, timelines, and approach to supporting this type of work. Specifically, we are looking for assistance with:

Project Overview:
Type: ${formData.projectType}
Asset: ${formData.asset}
Therapeutic Area: ${formData.therapeuticArea}
Objectives: ${formData.objectives}
Geographical scope: ${formData.geographicalScope}
Expected Deliverables: ${formData.deliverables}
Intended use for deliverables: ${formData.intendedUse}
Timeline: ${formData.timeline}
Budget range: ${formData.budgetRange}

If this falls within your scope, we would appreciate a brief capabilities statement and, if possible, a proposal outlining your approach, timeline, team qualifications, and budget estimate by ${formData.timeframe}.

Please let me know if you need a call to align further or if you have any initial questions.

Looking forward to your response.

Best regards,
${formData.fullName}
${formData.title}
${formData.companyName}
${formData.email}
${formData.phone}`;

    return { subject, message };
  };

  const handleApply = () => {
    const { subject, message } = generateMessage();
    
    // Pass both subject and message to the parent component
    onApplyTemplate(message, subject);
    
    onOpenChange(false);
    toast({
      title: "Template applied",
      description: "The message template has been applied to your message."
    });
  };

  const projectTypeOptions = [
    "CEA", "BIM", "SLR", "RWE", "BOI", "PRO", "GVD", "Payer Research", 
    "P&R landscape", "HTA submission", "HTA readiness", "Biostatistics", 
    "Value communications", "Data visualization", "AI solutions", "Policy", 
    "Epidemiology", "Early pipeline assessment", "Dashboard development"
  ];

  const budgetOptions = [
    "TBD", "0-50k", "50-150k", "150-250k", "250-500k", "500k+"
  ];

  const geoScopeOptions = [
    "US", "EU5", "EU", "Global", "Asia", "LATAM", "North America", 
    "UK", "Canada", "Australia", "Japan", "China", "Emerging Markets"
  ];

  const intendedUseOptions = [
    "Internal decision-making", "Payer negotiations", "HTA submissions", 
    "Value communication", "Publication", "Conference presentation", 
    "Market access strategy", "Pricing strategy", "TBD"
  ];

  const timelineOptions = [
    "ASAP", "Within 3 months", 
    "Within 6 months", "Within 12 months"
  ];

  const timeframeOptions = [
    "1 week", "2 weeks", "3 weeks", "1 month", 
    "By end of week", "By end of month", "ASAP"
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Request for Proposal Template</DialogTitle>
        </DialogHeader>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
          {/* <div className="space-y-2">
            <Label htmlFor="vendorContact">Vendor Contact</Label>
            <Input 
              id="vendorContact"
              name="vendorContact" 
              value={formData.vendorContact} 
              onChange={handleChange} 
              placeholder="Vendor contact name"
            />
          </div> */}

          <div className="space-y-2 md:col-span-2">
            <h3 className="text-medium font-medium mt-0">Project Information</h3>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="projectType">Project Type</Label>
            {!useCustomProjectType ? (
              <>
                <Select 
                  onValueChange={(value) => {
                    if (value === "custom") {
                      setUseCustomProjectType(true);
                    } else {
                      handleSelectChange("projectType", value);
                    }
                  }}
                  value={formData.projectType}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select project type" />
                  </SelectTrigger>
                  <SelectContent>
                    {projectTypeOptions.map((type) => (
                      <SelectItem key={type} value={type}>{type}</SelectItem>
                    ))}
                    <SelectItem value="custom">Custom (enter your own)</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground mt-1">
                  <Button 
                    variant="link" 
                    className="h-auto p-0 text-xs" 
                    onClick={() => setUseCustomProjectType(true)}
                  >
                    Need a custom option? Click here
                  </Button>
                </p>
              </>
            ) : (
              <div className="space-y-2">
                <div className="flex gap-2">
                  <Input 
                    id="customProjectType" 
                    value={formData.projectType} 
                    onChange={(e) => handleChange({
                      target: { name: "projectType", value: e.target.value }
                    } as React.ChangeEvent<HTMLInputElement>)} 
                    placeholder="Enter custom project type"
                    className="flex-1"
                  />
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => {
                      setUseCustomProjectType(false);
                      setFormData(prev => ({ ...prev, projectType: "" }));
                    }}
                    className="shrink-0"
                  >
                    Use List
                  </Button>
                </div>
              </div>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="asset">Asset</Label>
            <Input 
              id="asset" 
              name="asset" 
              value={formData.asset} 
              onChange={handleChange} 
              placeholder="e.g., Product X, mRNA in Phase 2"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="therapeuticArea">Therapeutic Area</Label>
            <Input 
              id="therapeuticArea" 
              name="therapeuticArea" 
              value={formData.therapeuticArea} 
              onChange={handleChange} 
              placeholder="e.g., oncology, rare disease"
            />
          </div>
          
          <div className="space-y-2 md:col-span-2">
            <Label htmlFor="objectives">Objectives</Label>
            <Textarea 
              id="objectives" 
              name="objectives" 
              value={formData.objectives} 
              onChange={handleChange} 
              placeholder="e.g., support HTA submissions in EU5 and Canada"
              className="min-h-[80px]"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="geographicalScope">Geographical Scope</Label>
            {!useCustomGeoScope ? (
              <>
                <Select 
                  onValueChange={(value) => {
                    if (value === "custom") {
                      setUseCustomGeoScope(true);
                    } else {
                      handleSelectChange("geographicalScope", value);
                    }
                  }}
                  value={formData.geographicalScope}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select geographical scope" />
                  </SelectTrigger>
                  <SelectContent>
                    {geoScopeOptions.map((option) => (
                      <SelectItem key={option} value={option}>{option}</SelectItem>
                    ))}
                    <SelectItem value="custom">Custom (enter your own)</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground mt-1">
                  <Button 
                    variant="link" 
                    className="h-auto p-0 text-xs" 
                    onClick={() => setUseCustomGeoScope(true)}
                  >
                    Need a custom option? Click here
                  </Button>
                </p>
              </>
            ) : (
              <div className="flex gap-2">
                <Input 
                  id="geographicalScope" 
                  name="geographicalScope" 
                  value={formData.geographicalScope} 
                  onChange={handleChange} 
                  placeholder="e.g., US, EU, Global, Asia"
                  className="flex-1"
                />
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => {
                    setUseCustomGeoScope(false);
                    setFormData(prev => ({ ...prev, geographicalScope: "" }));
                  }}
                  className="shrink-0"
                >
                  Use List
                </Button>
              </div>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="deliverables">Expected Deliverables</Label>
            <Input 
              id="deliverables" 
              name="deliverables" 
              value={formData.deliverables} 
              onChange={handleChange} 
              placeholder="e.g., de novo CEM, technical report"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="intendedUse">Intended Use</Label>
            {!useCustomIntendedUse ? (
              <>
                <Select 
                  onValueChange={(value) => {
                    if (value === "custom") {
                      setUseCustomIntendedUse(true);
                    } else {
                      handleSelectChange("intendedUse", value);
                    }
                  }}
                  value={formData.intendedUse}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select intended use" />
                  </SelectTrigger>
                  <SelectContent>
                    {intendedUseOptions.map((option) => (
                      <SelectItem key={option} value={option}>{option}</SelectItem>
                    ))}
                    <SelectItem value="custom">Custom (enter your own)</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground mt-1">
                  <Button 
                    variant="link" 
                    className="h-auto p-0 text-xs" 
                    onClick={() => setUseCustomIntendedUse(true)}
                  >
                    Need a custom option? Click here
                  </Button>
                </p>
              </>
            ) : (
              <div className="flex gap-2">
                <Input 
                  id="intendedUse" 
                  name="intendedUse" 
                  value={formData.intendedUse} 
                  onChange={handleChange} 
                  placeholder="e.g., internal, with payers, HTA, TBD"
                  className="flex-1"
                />
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => {
                    setUseCustomIntendedUse(false);
                    setFormData(prev => ({ ...prev, intendedUse: "" }));
                  }}
                  className="shrink-0"
                >
                  Use List
                </Button>
              </div>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="timeline">Timeline</Label>
            {!useCustomTimeline ? (
              <>
                <Select 
                  onValueChange={(value) => {
                    if (value === "custom") {
                      setUseCustomTimeline(true);
                    } else {
                      handleSelectChange("timeline", value);
                    }
                  }}
                  value={formData.timeline}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select timeline" />
                  </SelectTrigger>
                  <SelectContent>
                    {timelineOptions.map((option) => (
                      <SelectItem key={option} value={option}>{option}</SelectItem>
                    ))}
                    <SelectItem value="custom">Custom (enter your own)</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground mt-1">
                  <Button 
                    variant="link" 
                    className="h-auto p-0 text-xs" 
                    onClick={() => setUseCustomTimeline(true)}
                  >
                    Need a custom option? Click here
                  </Button>
                </p>
              </>
            ) : (
              <div className="flex gap-2">
                <Input 
                  id="timeline" 
                  name="timeline" 
                  value={formData.timeline} 
                  onChange={handleChange} 
                  placeholder="e.g., Q3 kickoff, deliverables by end of Q4"
                  className="flex-1"
                />
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => {
                    setUseCustomTimeline(false);
                    setFormData(prev => ({ ...prev, timeline: "" }));
                  }}
                  className="shrink-0"
                >
                  Use List
                </Button>
              </div>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="budgetRange">Budget Range</Label>
            {!useCustomBudgetRange ? (
              <>
                <Select 
                  onValueChange={(value) => {
                    if (value === "custom") {
                      setUseCustomBudgetRange(true);
                    } else {
                      handleSelectChange("budgetRange", value);
                    }
                  }}
                  value={formData.budgetRange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select budget range" />
                  </SelectTrigger>
                  <SelectContent>
                    {budgetOptions.map((budget) => (
                      <SelectItem key={budget} value={budget}>{budget}</SelectItem>
                    ))}
                    <SelectItem value="custom">Custom (enter your own)</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground mt-1">
                  <Button 
                    variant="link" 
                    className="h-auto p-0 text-xs" 
                    onClick={() => setUseCustomBudgetRange(true)}
                  >
                    Need a custom option? Click here
                  </Button>
                </p>
              </>
            ) : (
              <div className="space-y-2">
                <div className="flex gap-2">
                  <Input 
                    id="customBudgetRange" 
                    value={formData.budgetRange} 
                    onChange={(e) => handleChange({
                      target: { name: "budgetRange", value: e.target.value }
                    } as React.ChangeEvent<HTMLInputElement>)} 
                    placeholder="Enter custom budget range"
                    className="flex-1"
                  />
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => {
                      setUseCustomBudgetRange(false);
                      setFormData(prev => ({ ...prev, budgetRange: "" }));
                    }}
                    className="shrink-0"
                  >
                    Use List
                  </Button>
                </div>
              </div>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="timeframe">Response Timeframe</Label>
            {!useCustomTimeframe ? (
              <>
                <Select 
                  onValueChange={(value) => {
                    if (value === "custom") {
                      setUseCustomTimeframe(true);
                    } else {
                      handleSelectChange("timeframe", value);
                    }
                  }}
                  value={formData.timeframe}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select response timeframe" />
                  </SelectTrigger>
                  <SelectContent>
                    {timeframeOptions.map((option) => (
                      <SelectItem key={option} value={option}>{option}</SelectItem>
                    ))}
                    <SelectItem value="custom">Custom (enter your own)</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground mt-1">
                  <Button 
                    variant="link" 
                    className="h-auto p-0 text-xs" 
                    onClick={() => setUseCustomTimeframe(true)}
                  >
                    Need a custom option? Click here
                  </Button>
                </p>
              </>
            ) : (
              <div className="flex gap-2">
                <Input 
                  id="timeframe" 
                  name="timeframe" 
                  value={formData.timeframe} 
                  onChange={handleChange} 
                  placeholder="e.g., next Friday, within 2 weeks"
                  className="flex-1"
                />
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => {
                    setUseCustomTimeframe(false);
                    setFormData(prev => ({ ...prev, timeframe: "" }));
                  }}
                  className="shrink-0"
                >
                  Use List
                </Button>
              </div>
            )}
          </div>
          
          <div className="space-y-2 md:col-span-2">
            <h3 className="text-medium font-medium mt-5">Your Contact Information</h3>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="fullName">Full Name</Label>
            <Input 
              id="fullName" 
              name="fullName" 
              value={formData.fullName} 
              onChange={handleChange} 
              placeholder="Your full name"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="title">Title (Full)</Label>
            <Input 
              id="title" 
              name="title" 
              value={formData.title} 
              onChange={handleChange} 
              placeholder="Your job title"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="workTitle">Title (Short Description)</Label>
            <Input 
              id="workTitle" 
              name="workTitle" 
              value={formData.workTitle} 
              onChange={handleChange} 
              placeholder="e.g., HEOR Manager"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="companyName">Company Name</Label>
            <Input 
              id="companyName" 
              name="companyName" 
              value={formData.companyName} 
              onChange={handleChange} 
              placeholder="e.g., Pharma Inc."
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input 
              id="email" 
              name="email" 
              value={formData.email} 
              onChange={handleChange} 
              placeholder="Your email address"
              type="email"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="phone">Phone</Label>
            <Input 
              id="phone" 
              name="phone" 
              value={formData.phone} 
              onChange={handleChange} 
              placeholder="Your phone number"
              type="tel"
            />
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
          <Button onClick={handleApply} className="bg-amber-400 text-black hover:bg-amber-500">
            Apply Template
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
