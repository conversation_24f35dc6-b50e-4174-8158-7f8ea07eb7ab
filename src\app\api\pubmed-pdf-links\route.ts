import { NextRequest, NextResponse } from 'next/server';
import { XMLParser } from 'fast-xml-parser';

export async function GET(request: NextRequest) {
  const pmids = request.nextUrl.searchParams.get('pmids');
  
  if (!pmids) {
    return NextResponse.json({ error: 'Missing pmids parameter' }, { status: 400 });
  }
  
  try {
    const pmidList = pmids.split(',');
    const links: Record<string, string> = {};
    
    // Process in batches of 10 to avoid overloading the API
    for (let i = 0; i < pmidList.length; i += 10) {
      const batch = pmidList.slice(i, i + 10);
      await Promise.all(batch.map(async (pmid) => {
        try {
          // Use ELink to find PMC IDs for PubMed IDs
          const elinkUrl = `https://eutils.ncbi.nlm.nih.gov/entrez/eutils/elink.fcgi?dbfrom=pubmed&db=pmc&id=${pmid}`;
          const response = await fetch(elinkUrl);
          const xmlText = await response.text();
          
          // Parse the XML response
          const parser = new XMLParser({ 
            ignoreAttributes: false,
            isArray: (name) => ['Link'].includes(name)
          });
          const result = parser.parse(xmlText);
          
          // Extract PMC ID from the LinkSetDb
          const linkSet = result.eLinkResult?.LinkSet;
          if (linkSet && linkSet.LinkSetDb && linkSet.LinkSetDb.Link && linkSet.LinkSetDb.Link.length > 0) {
            const pmcId = linkSet.LinkSetDb.Link[0].Id;
            if (pmcId) {
              const pdfUrl = `https://www.ncbi.nlm.nih.gov/pmc/articles/PMC${pmcId}/pdf/`;
              links[pmid] = pdfUrl;
            }
          }
        } catch (error) {
          console.error(`Error checking PMC for PMID ${pmid}:`, error);
        }
      }));
      
      // Add a small delay between batches to be nice to the API
      if (i + 10 < pmidList.length) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }
    
    return NextResponse.json({ links });
  } catch (error) {
    console.error('Error fetching PDF links:', error);
    return NextResponse.json({ error: 'Failed to fetch PDF links' }, { status: 500 });
  }
}
