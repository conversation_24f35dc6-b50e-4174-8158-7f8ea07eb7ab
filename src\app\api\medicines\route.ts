import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET() {
  try {
    // Path to the medicines data file
    const filePath = path.join(process.cwd(), 'medicines_output_medicines_en_fixed.json');
    
    // Read the file
    const fileData = fs.readFileSync(filePath, 'utf8');
    const medicines = JSON.parse(fileData);
    
    // Return the data
    return NextResponse.json(medicines);
  } catch (error) {
    console.error('Error reading medicines data:', error);
    return NextResponse.json(
      { error: 'Failed to load medicines data' },
      { status: 500 }
    );
  }
}