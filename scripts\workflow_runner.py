import json
import sys
from typing import Dict, List

from openai import OpenAI

client = OpenAI()

class Node:
    def __init__(self, node_id: str, name: str, prompt: str, model: str, context: str = ""):
        self.id = node_id
        self.name = name
        self.prompt = prompt
        self.model = model
        self.context = context
        self.next: List[str] = []

    def run(self, input_text: str):
        try:
            completion = client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": self.prompt},
                    {"role": "user", "content": input_text},
                ],
                stream=True,
            )
            output = ""
            for chunk in completion:
                delta = chunk.choices[0].delta
                if delta.content:
                    output += delta.content
                    print(
                        json.dumps({"event": "chunk", "id": self.id, "content": delta.content})
                    )
            tokens = len(output.split())
            print(
                json.dumps(
                    {
                        "event": "done",
                        "id": self.id,
                        "input": input_text,
                        "output": output,
                        "tokens": tokens,
                    }
                )
            )
            return output, tokens
        except Exception as err:
            output = f"{self.prompt} {input_text}".strip()
            tokens = len(output.split())
            print(json.dumps({"event": "error", "id": self.id, "error": str(err)}))
            print(
                json.dumps(
                    {
                        "event": "done",
                        "id": self.id,
                        "input": input_text,
                        "output": output,
                        "tokens": tokens,
                    }
                )
            )
            return output, tokens

def run_workflow(data: Dict):
    nodes: Dict[str, Node] = {
        n['id']: Node(
            n['id'],
            n.get('name', ''),
            n.get('prompt', ''),
            n.get('model', 'gpt-4.1-mini'),
            n.get('context', ''),
        )
        for n in data.get('agents', [])
    }
    for edge in data.get('edges', []):
        src = edge.get('source')
        tgt = edge.get('target')
        if src in nodes and tgt in nodes:
            nodes[src].next.append(tgt)
    if not nodes:
        return {}
    entry = data['agents'][0]['id']
    queue = [(entry, data.get('initial_input', ''))]
    outputs: Dict[str, Dict] = {}
    while queue:
        node_id, inp = queue.pop(0)
        node = nodes[node_id]
        out, tokens = node.run(inp)
        outputs[node_id] = {
            'input': inp,
            'output': out,
            'tokens': tokens,
            'state': {}
        }
        for nxt in node.next:
            queue.append((nxt, out + (" " + node.context if node.context else "")))
    return outputs

if __name__ == '__main__':
    payload = json.load(sys.stdin)
    results = run_workflow(payload)
    print(json.dumps({"event": "results", "data": results}))
