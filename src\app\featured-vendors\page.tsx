"use client";

import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>Left, CheckCircle2, Star } from "lucide-react";
import Link from 'next/link';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

export default function FeaturedVendorsPage() {
  const featuredPackages = [
    {
      id: 1,
      name: "Silver",
      price: "$299",
      period: "per month",
      features: [
        "Featured badge on vendor profile",
        "Priority placement in search results",
        "Basic analytics dashboard",
        "3-month minimum commitment"
      ],
      highlighted: false
    },
    {
      id: 2,
      name: "Gold",
      price: "$599",
      period: "per month",
      features: [
        "Premium featured badge on vendor profile",
        "Top placement in search results",
        "Advanced analytics dashboard",
        "Monthly performance report",
        "Featured in 'Recommended Vendors' section",
        "6-month minimum commitment"
      ],
      highlighted: true
    },
    {
      id: 3,
      name: "Platinum",
      price: "$999",
      period: "per month",
      features: [
        "Elite featured badge on vendor profile",
        "Guaranteed top placement in search results",
        "Comprehensive analytics suite",
        "Weekly performance reports",
        "Featured in 'Recommended Vendors' section",
        "Highlighted in monthly newsletter",
        "12-month minimum commitment"
      ],
      highlighted: false
    }
  ];

  return (
    <div className="container max-w-4xl mx-auto py-8 px-4">
      <Link href="/">
        <Button variant="ghost" className="mb-6 pl-0 hover:bg-transparent">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Evicenter
        </Button>
      </Link>
      
      <div className="flex items-center mb-6">
        <h1 className="text-3xl font-bold">Become a Featured Agency</h1>
        {/* <Star className="ml-3 h-6 w-6 text-amber-400 fill-amber-400" /> */}
      </div>
      
      <div className="prose max-w-none mb-8">
        <p className="text-lg">
          Stand out from the competition and attract more clients by becoming a featured agency on 
          Evicenter. Featured agencies receive enhanced visibility to maximize exposure to potential clients.
        </p>
      </div>
      
      {/* <h2 className="text-2xl font-semibold mb-6">Featured Vendor Packages</h2>
      
      <div className="grid md:grid-cols-3 gap-6 mb-10">
        {featuredPackages.map((pkg) => (
          <Card 
            key={pkg.id} 
            className={`flex flex-col ${pkg.highlighted ? 'border-amber-400 shadow-lg' : ''}`}
          >
            {pkg.highlighted && (
              <div className="bg-amber-400 text-black text-center py-1 text-sm font-medium">
                Most Popular
              </div>
            )}
            <CardHeader>
              <CardTitle className="text-center">
                <div className="text-2xl font-bold">{pkg.name}</div>
                <div className="text-3xl font-bold mt-2">{pkg.price}</div>
                <div className="text-sm text-muted-foreground">{pkg.period}</div>
              </CardTitle>
            </CardHeader>
            <CardContent className="flex-grow">
              <ul className="space-y-2">
                {pkg.features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <CheckCircle2 className="h-5 w-5 text-amber-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
            <CardFooter>
              <Button 
                className={`w-full ${pkg.highlighted ? 'bg-amber-400 hover:bg-amber-500 text-black' : ''}`}
                variant={pkg.highlighted ? "default" : "outline"}
              >
                Apply Now
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div> */}
      
      {/* <div className="bg-muted p-6 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Benefits of Being a Featured Vendor</h2>
        <ul className="space-y-2 mb-6">
          <li className="flex items-start">
            <CheckCircle2 className="h-5 w-5 text-amber-500 mr-2 flex-shrink-0 mt-0.5" />
            <span>Increased visibility to potential clients</span>
          </li>
          <li className="flex items-start">
            <CheckCircle2 className="h-5 w-5 text-amber-500 mr-2 flex-shrink-0 mt-0.5" />
            <span>Higher conversion rates and more inquiries</span>
          </li>
          <li className="flex items-start">
            <CheckCircle2 className="h-5 w-5 text-amber-500 mr-2 flex-shrink-0 mt-0.5" />
            <span>Detailed analytics on profile views and interactions</span>
          </li>
          <li className="flex items-start">
            <CheckCircle2 className="h-5 w-5 text-amber-500 mr-2 flex-shrink-0 mt-0.5" />
            <span>Enhanced profile customization options</span>
          </li>
        </ul>
        <Button className="bg-amber-400 hover:bg-amber-500 text-black">
          Learn More
        </Button>
      </div>

      <div className="mt-10 bg-muted p-6 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Frequently Asked Questions</h2>
        
        <Accordion type="single" collapsible className="mb-6">
          <AccordionItem value="item-1">
            <AccordionTrigger className="text-left">
              How long does it take to become a featured vendor?
            </AccordionTrigger>
            <AccordionContent>
              Once you complete the application and payment process, your profile will be upgraded to featured status within 24-48 business hours.
            </AccordionContent>
          </AccordionItem>
          
          <AccordionItem value="item-2">
            <AccordionTrigger className="text-left">
              Can I upgrade or downgrade my package later?
            </AccordionTrigger>
            <AccordionContent>
              Yes, you can upgrade your package at any time. Downgrades can be processed at the end of your current billing cycle.
            </AccordionContent>
          </AccordionItem>
          
          <AccordionItem value="item-3">
            <AccordionTrigger className="text-left">
              What happens after my commitment period ends?
            </AccordionTrigger>
            <AccordionContent>
              Your featured status will continue on a month-to-month basis at the same rate until you decide to cancel or change your package.
            </AccordionContent>
          </AccordionItem>
          
          <AccordionItem value="item-4">
            <AccordionTrigger className="text-left">
              How do I track the performance of my featured listing?
            </AccordionTrigger>
            <AccordionContent>
              All featured vendors receive access to an analytics dashboard where you can track profile views, contact requests, and other key metrics.
            </AccordionContent>
          </AccordionItem>
        </Accordion>
        
        <Button className="bg-amber-400 hover:bg-amber-500 text-black">
          Contact Sales Team
        </Button>
      </div> */}
    </div>
  );
}
