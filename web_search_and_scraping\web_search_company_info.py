from pydantic import BaseModel, Field
from openai import OpenAI
import json
import os
from dotenv import load_dotenv

load_dotenv()

client = OpenAI(
    api_key=os.environ.get("OPENAI_API_KEY")
)

class CompanyData(BaseModel):
    Company: str = Field(description="Full legal name or brand name of the company")
    Contact_info: str = Field(default="", description="Primary email address for general inquiries (e.g., <EMAIL>)")
    Contact_page: str = Field(default="", description="URL to the company's contact page or contact form")
    Website: str = Field(default="", description="Main company website URL")
    Services: str = Field(default="", description="List of services offered by the company, formatted as bullet points with '- ' prefix for each service. Include specific service areas like HEOR, market access, clinical research, consulting, etc.")
    LinkedIn: str = Field(default="", description="Company's LinkedIn profile URL")
    Company_size: str = Field(default="", description="Employee count range (e.g., '11-50', '201-500', '1001-5000')")
    HQ_country: str = Field(default="", description="Country where the company's headquarters is located")
    Featured: int = Field(default=0, description="Featured status flag (0 for not featured, 1 for featured)")
    logo: str = Field(default="", description="Logo filename or path (typically company_name_logo.jpeg)")
    Publications: str = Field(default="", description="URL to company's publications page or research output, if available")

completion = client.beta.chat.completions.parse(
    model="gpt-4o-search-preview",
    messages=[
        {"role": "user", "content": "Please provide me with the following information about the company: https://www.evernorth.com/capabilities/intelligence-plus/evernorth-research-institute"},
    ],
    response_format=CompanyData,
)

company_data = completion.choices[0].message.parsed

# Convert to the exact JSON format specified
output_data = {
    "Company": company_data.Company,
    "Contact info": company_data.Contact_info,
    "Contact page": company_data.Contact_page,
    "Website": company_data.Website,
    "Services": company_data.Services,
    "LinkedIn": company_data.LinkedIn,
    "Company size": company_data.Company_size,
    "HQ country": company_data.HQ_country,
    "Featured": company_data.Featured,
    "logo": company_data.logo,
    "Publications": company_data.Publications
}

# Append to text file with comma and newline
with open("company_data.txt", "a") as f:
    json.dump(output_data, f, indent=2)
    f.write(",\n")  # Add comma and newline after each JSON item

print("Company data extracted and saved to company_data.txt")
print(json.dumps(output_data, indent=2))