"use client"

import React, { useState, useEffect, useCallback } from 'react';
import { X, <PERSON>R<PERSON>, <PERSON><PERSON>eft, HelpCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { useLocalStorage } from '@/hooks/use-local-storage';
import { useTheme } from '@/contexts/ThemeContext';
import { cn } from '@/lib/utils';

interface TutorialStep {
  id: number;
  title: string;
  content: string;
  targetId?: string;
}

interface TutorialWidgetProps {
  steps: TutorialStep[];
  storageKey?: string;
}

export function TutorialWidget({ 
  steps, 
  storageKey = "vendorhub-tutorial-completed" 
}: TutorialWidgetProps) {
  const { theme } = useTheme();
  const [tutorialCompleted, setTutorialCompleted] = useLocalStorage(storageKey, false);
  const [isVisible, setIsVisible] = useState<boolean | null>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [position, setPosition] = useState({ top: 20, right: 20 });

  // Define the function before any conditional returns
  const positionWidgetNearTarget = useCallback(() => {
    if (!steps[currentStep]?.targetId) {
      setPosition({ top: 20, right: 20 });
      return;
    }

    const targetElement = document.querySelector(`[data-tip-target="${steps[currentStep].targetId}"]`);
    
    if (targetElement) {
      const rect = targetElement.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      
      let newTop = rect.top + window.scrollY;
      let newRight = viewportWidth - rect.right - window.scrollX;
      
      if (newTop < 20) newTop = 20;
      if (newTop > viewportHeight - 200) newTop = viewportHeight - 200;
      if (newRight < 20) newRight = 20;
      
      setPosition({ top: newTop, right: newRight });
    }
  }, [currentStep, steps]);

  // Set the actual visibility state after hydration
  useEffect(() => {
    setIsVisible(!tutorialCompleted);
  }, [tutorialCompleted]);

  // Position the widget when the current step changes
  useEffect(() => {
    if (isVisible) {
      positionWidgetNearTarget();
      
      window.addEventListener('resize', positionWidgetNearTarget);
      return () => window.removeEventListener('resize', positionWidgetNearTarget);
    }
  }, [currentStep, isVisible, positionWidgetNearTarget]);

  // Skip rendering until after hydration
  if (isVisible === null) return null;

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      completeTutorial();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const completeTutorial = () => {
    setIsVisible(false);
    setTutorialCompleted(true);
  };

  const restartTutorial = () => {
    setCurrentStep(0);
    setIsVisible(true);
    setTutorialCompleted(false);
  };

  if (!isVisible && !tutorialCompleted) return null;

  // Floating help button when tutorial is completed
  if (!isVisible && tutorialCompleted) {
    return (
      <Button
        onClick={restartTutorial}
        className="fixed bottom-4 right-16 z-50 rounded-full bg-amber-400 text-black hover:bg-amber-500 shadow-md"
        size="icon"
      >
        <HelpCircle className="h-5 w-5" />
      </Button>
    );
  }

  const currentTutorialStep = steps[currentStep];

  return (
    <Card
      className="fixed z-50 w-80 bg-card text-card-foreground shadow-lg rounded-lg overflow-hidden border border-border"
      style={{ top: `${position.top}px`, right: `${position.right}px` }}
    >
      {/* Header */}
      <div className="bg-amber-400 p-3 flex justify-between items-center">
        <h3 className="font-medium text-black">{currentTutorialStep.title}</h3>
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={completeTutorial} 
          className="h-6 w-6 p-0 hover:bg-amber-500/20"
        >
          <X className="h-4 w-4 text-black" />
        </Button>
      </div>
      
      {/* Content */}
      <div className="p-4">
        <p className="text-sm text-foreground mb-4">{currentTutorialStep.content}</p>
        
        {/* Navigation */}
        <div className="flex justify-between items-center">
          <div className="text-xs text-muted-foreground">
            Step {currentStep + 1} of {steps.length}
          </div>
          <div className="flex gap-2">
            {currentStep > 0 && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handlePrevious}
                className="text-xs h-8"
              >
                <ArrowLeft className="h-3 w-3 mr-1" />
                Back
              </Button>
            )}
            <Button 
              size="sm" 
              onClick={handleNext}
              className={cn(
                "text-xs h-8 bg-amber-400 text-black hover:bg-amber-500"
              )}
            >
              {currentStep < steps.length - 1 ? (
                <>
                  Next
                  <ArrowRight className="h-3 w-3 ml-1" />
                </>
              ) : (
                "Got it!"
              )}
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
}
