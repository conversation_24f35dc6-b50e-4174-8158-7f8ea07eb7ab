
@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 207 90% 74%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 180 100% 25%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 207 90% 74%;
    --sidebar-foreground: 0 0% 3.9%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 12%; /* Darker background like in the image */
    --foreground: 0 0% 98%;
    --card: 0 0% 15%; /* Slightly lighter than background */
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 15%;
    --popover-foreground: 0 0% 98%;
    --primary: 35 100% 50%; /* Amber color from the image */
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 20%; /* Slightly lighter for secondary elements */
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 20%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 35 100% 50%; /* Amber accent color */
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 20%;
    --input: 0 0% 20%;
    --ring: 35 100% 50%; /* Amber for focus rings */
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 0 0% 12%;
    --sidebar-foreground: 0 0% 98%;
    --sidebar-primary: 35 100% 50%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 0 0% 20%;
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 0 0% 20%;
    --sidebar-ring: 35 100% 50%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}

/* Vendor card text styling for dark mode */
.vendor-card-text {
  color: var(--foreground);
}

.dark .vendor-card-text {
  color: white;
}

.dark .vendor-card-description {
  color: rgba(255, 255, 255, 0.7);
}

/* Button styling for dark mode to match the image */
.dark .bg-amber-400 {
  background-color: #ffb800; /* Bright amber color from the image */
}

.dark .hover\:bg-amber-500:hover {
  background-color: #ffa500; /* Slightly darker amber for hover */
}

/* Selected vendor card styling for dark mode */
.dark .border-amber-300 {
  border-color: #ffb800;
}

.dark .bg-amber-50\/50 {
  background-color: rgba(26, 26, 26, 0.5); /* Dark background with opacity */
}

.dark .bg-amber-950\/30 {
  background-color: rgba(26, 26, 26, 0.3); /* Dark background with opacity */
}

/* Text input styling for dark mode */
.dark .bg-secondary {
  background-color: #1a1a1a; /* Dark input background */
}

/* Add PDF highlight styles */
.pdf-highlight {
  position: absolute;
  background-color: rgba(255, 193, 7, 0.4);
  border-radius: 2px;
  pointer-events: none;
  z-index: 100;
}
