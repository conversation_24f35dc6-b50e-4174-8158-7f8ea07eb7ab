import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const id = request.nextUrl.searchParams.get('id');
  if (!id) {
    return NextResponse.json({ error: 'Missing id parameter' }, { status: 400 });
  }

  try {
    const res = await fetch(
      `https://dailymed.nlm.nih.gov/dailymed/services/v2/spls/${id}.json`
    );
    if (!res.ok) {
      return NextResponse.json(
        { error: 'Failed to fetch label data' },
        { status: res.status }
      );
    }
    const data = await res.json();
    return NextResponse.json(data);
  } catch (err) {
    console.error('Error fetching label', err);
    return NextResponse.json(
      { error: 'Unable to fetch label data' },
      { status: 500 }
    );
  }
}
