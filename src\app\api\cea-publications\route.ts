import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const search = searchParams.get('search') || '';
    const limit = parseInt(searchParams.get('limit') || '50');
    const skip = parseInt(searchParams.get('skip') || '0');

    const { db } = await connectToDatabase();
    const collection = db.collection('CEA_articles');

    // Build search query
    const query: any = {};
    if (search) {
      query.$or = [
        { Title: { $regex: search, $options: 'i' } },
        { Abstract: { $regex: search, $options: 'i' } },
        { Journal: { $regex: search, $options: 'i' } },
        { AuthorsWithAffiliations: { $regex: search, $options: 'i' } },
        { therapeutic_area: { $regex: search, $options: 'i' } },
        { indication: { $regex: search, $options: 'i' } },
        { MatchedVendor: { $regex: search, $options: 'i' } }
      ];
    }

    const [rawArticles, total] = await Promise.all([
      collection.find(query).skip(skip).limit(limit).toArray(),
      collection.countDocuments(query)
    ]);

    // Process the articles to ensure consistent field names
    const articles = rawArticles.map((article: any) => ({
      _id: article._id,
      title: article.Title,
      abstract: article.Abstract,
      journal: article.Journal,
      authors: article.AuthorsWithAffiliations,
      publication_date: article.PublicationDate,
      pmid: article.PMID,
      pmcid: article.PMCID_or_MID,
      doi: article.DOI,
      therapeutic_area: article.therapeutic_area,
      region: article.region,
      study_type: article.study_type,
      indication: article.indication,
      perspective: article.perspective,
      model_type: article.model_type,
      health_states: article.health_states,
      population: article.population,
      intervention: article.intervention,
      comparators: article.comparators,
      outcomes: article.outcomes,
      economic_outcome: article.economic_outcome,
      econ_analysis: article.econ_analysis,
      affiliation_types: article.affiliation_types,
      vendor: article.MatchedVendor
    }));

    return NextResponse.json({
      articles,
      total,
      hasMore: skip + limit < total
    });

  } catch (error) {
    console.error('Error fetching CEA publications:', error);
    return NextResponse.json(
      { error: 'Failed to fetch CEA publications' },
      { status: 500 }
    );
  }
}


