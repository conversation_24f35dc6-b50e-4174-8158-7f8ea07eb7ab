"use client";

import React from 'react';
import { Button } from "@/components/ui/button";
import { ArrowLeft, CheckCircle2 } from "lucide-react";
import Link from 'next/link';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";

export default function AdvertisePage() {
  const adPackages = [
    {
      id: 1,
      name: "Basic",
      price: "$",
      period: "per month",
      features: [
        "Banner ad in sidebar rotation",
        "1 sponsored content post",
        "Basic analytics dashboard",
        "30-day commitment"
      ],
      highlighted: false
    },
    {
      id: 2,
      name: "Premium",
      price: "$",
      period: "per month",
      features: [
        "Premium banner placement",
        "3 sponsored content posts",
        "Advanced analytics dashboard",
        "Featured in monthly newsletter",
        "60-day commitment"
      ],
      highlighted: true
    },
    {
      id: 3,
      name: "Enterprise",
      price: "$",
      period: "per month",
      features: [
        "Exclusive banner placement",
        "5 sponsored content posts",
        "Comprehensive analytics suite",
        "Featured in monthly newsletter",
        "Custom email campaign",
        "90-day commitment"
      ],
      highlighted: false
    }
  ];

  return (
    <div className="container max-w-4xl mx-auto py-8 px-4">
      <Link href="/">
        <Button variant="ghost" className="mb-6 pl-0 hover:bg-transparent">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Evicenter
        </Button>
      </Link>
      
      <h1 className="text-3xl font-bold mb-6">Advertise on Evicenter</h1>
      
      <div className="prose max-w-none mb-8">
        <p className="text-lg">
          Reach thousands of life science professionals looking for 
          HEOR agencies to partner with. Our targeted advertising options help you 
          showcase your services to potential clients actively seeking agency partners.
        </p>
        <h2 className="text-2xl font-semibold mt-8 mb-4">Limited Time Offer</h2>
        <p className="text-lg">
          We are offering free advertising to interested parties with the initial launch of Evicenter. Please contact the Evicenter team to learn more about our advertising packages and how to get started.
        </p>
      </div>
      
      {/* Hiding advertising packages section */}
      {false && (
        <>
          <h2 className="text-2xl font-semibold mb-6">Advertising Packages</h2>
          
          {/* This will hide everything below as well */}
          <div className="grid md:grid-cols-3 gap-6 mb-10">
            {adPackages.map((pkg) => (
              <Card 
                key={pkg.id} 
                className={`flex flex-col ${pkg.highlighted ? 'border-amber-400 shadow-lg' : ''}`}
              >
                {pkg.highlighted && (
                  <div className="bg-amber-400 text-black text-center py-1 text-sm font-medium">
                    Most Popular
                  </div>
                )}
                <CardHeader>
                  <CardTitle className="text-center">
                    <div className="text-2xl font-bold">{pkg.name}</div>
                    <div className="text-3xl font-bold mt-2">{pkg.price}</div>
                    <div className="text-sm text-muted-foreground">{pkg.period}</div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="flex-grow">
                  <ul className="space-y-2">
                    {pkg.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <CheckCircle2 className="h-5 w-5 text-amber-500 mr-2 flex-shrink-0 mt-0.5" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
                <CardFooter>
                  <Button 
                    className={`w-full ${pkg.highlighted ? 'bg-amber-400 hover:bg-amber-500 text-black' : ''}`}
                    variant={pkg.highlighted ? "default" : "outline"}
                  >
                    Get Started
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </>
      )}

      {/* <div className="bg-muted p-6 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Custom Advertising Solutions</h2>
        <p className="mb-4">
          Looking for something different? We offer custom advertising solutions tailored to your 
          specific needs and budget. Contact our advertising team to discuss your requirements.
        </p>
        <Button className="bg-amber-400 hover:bg-amber-500 text-black">
          Contact Advertising Team
        </Button>
      </div> */}
    </div>

  );
}
