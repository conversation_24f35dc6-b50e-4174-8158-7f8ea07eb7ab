import { NextRequest, NextResponse } from 'next/server';

// Proxy a PubMed Central PDF via the EFetch API to add CORS headers
export async function GET(request: NextRequest) {
  const pmcid = request.nextUrl.searchParams.get('pmcid');
  if (!pmcid) {
    return NextResponse.json({ error: 'Missing pmcid parameter' }, { status: 400 });
  }

  const pmcUrl =
    `https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi` +
    `?db=pmc&id=${pmcid}&rettype=pdf&retmode=binary`;
  try {
    const res = await fetch(pmcUrl);
    if (!res.ok || !res.body) {
      return NextResponse.json(
        { error: `Upstream error ${res.status}` },
        { status: res.status }
      );
    }
    return new NextResponse(res.body as ReadableStream, {
      headers: {
        'Content-Type': 'application/pdf',
        'Access-Control-Allow-Origin': '*',
        'Cache-Control': 'no-store'
      }
    });
  } catch (err) {
    console.error('PMC PDF proxy error', err);
    return NextResponse.json({ error: 'Server error' }, { status: 500 });
  }
}
