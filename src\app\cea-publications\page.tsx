"use client";

import { useState, useEffect, useRef, useMemo } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Search, FileText, ExternalLink, Filter, Loader2, GitCompare, Download, Sparkles, X } from "lucide-react";
import { SubpageHeader } from "@/components/SubpageHeader";
import { useToast } from "@/hooks/use-toast";
import Image from 'next/image';
import { Checkbox } from "@/components/ui/checkbox";

interface CEAArticle {
  _id: string;
  title?: string;
  abstract?: string;
  journal?: string;
  authors?: string;
  publication_date?: string;
  pmid?: string;
  pmcid?: string;
  doi?: string;
  therapeutic_area?: string;
  region?: string;
  study_type?: string;
  indication?: string;
  perspective?: string;
  model_type?: string;
  health_states?: string;
  population?: string;
  intervention?: string;
  comparators?: string;
  outcomes?: string;
  economic_outcome?: string;
  econ_analysis?: string;
  affiliation_types?: string;
  vendor?: string;
}

const getCountryCode = (country: string): string | null => {
  const countryMap: { [key: string]: string } = {
    'United States': 'us',
    'USA': 'us',
    'US': 'us',
    'United Kingdom': 'gb',
    'UK': 'gb',
    'Germany': 'de',
    'France': 'fr',
    'Italy': 'it',
    'Spain': 'es',
    'Netherlands': 'nl',
    'Belgium': 'be',
    'Switzerland': 'ch',
    'Austria': 'at',
    'Sweden': 'se',
    'Norway': 'no',
    'Denmark': 'dk',
    'Finland': 'fi',
    'Canada': 'ca',
    'Australia': 'au',
    'Japan': 'jp',
    'South Korea': 'kr',
    'China': 'cn',
    'India': 'in',
    'Brazil': 'br',
    'Mexico': 'mx',
    'Argentina': 'ar',
    'Chile': 'cl',
    'Colombia': 'co',
    'Peru': 'pe',
    'Venezuela': 've',
    'Poland': 'pl',
    'Czech Republic': 'cz',
    'Hungary': 'hu',
    'Romania': 'ro',
    'Bulgaria': 'bg',
    'Croatia': 'hr',
    'Slovenia': 'si',
    'Slovakia': 'sk',
    'Lithuania': 'lt',
    'Latvia': 'lv',
    'Estonia': 'ee',
    'Portugal': 'pt',
    'Greece': 'gr',
    'Turkey': 'tr',
    'Israel': 'il',
    'South Africa': 'za',
    'Egypt': 'eg',
    'Morocco': 'ma',
    'Nigeria': 'ng',
    'Kenya': 'ke',
    'Ghana': 'gh',
    'Tunisia': 'tn',
    'Algeria': 'dz',
    'Russia': 'ru',
    'Ukraine': 'ua',
    'Belarus': 'by',
    'Kazakhstan': 'kz',
    'Uzbekistan': 'uz',
    'Georgia': 'ge',
    'Armenia': 'am',
    'Azerbaijan': 'az',
    'Thailand': 'th',
    'Vietnam': 'vn',
    'Malaysia': 'my',
    'Singapore': 'sg',
    'Indonesia': 'id',
    'Philippines': 'ph',
    'Taiwan': 'tw',
    'Hong Kong': 'hk',
    'New Zealand': 'nz',
    'Ireland': 'ie',
    'Luxembourg': 'lu',
    'Iceland': 'is',
    'Malta': 'mt',
    'Cyprus': 'cy'
  };
  
  return countryMap[country] || null;
};

const parseBullets = (text: string) =>
  text
    .split(/\n+/)
    .map((l) => l.replace(/^[^\w]+|^[0-9]+\.?\s+/, "").trim())
    .filter(Boolean);

export default function CEAPublicationsPage() {
  const [articles, setArticles] = useState<CEAArticle[]>([]);
  const [filteredArticles, setFilteredArticles] = useState<CEAArticle[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedArticle, setSelectedArticle] = useState<CEAArticle | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [compareIds, setCompareIds] = useState<string[]>([]);
  const [comparisonSummary, setComparisonSummary] = useState<string>("");
  const [compareLoading, setCompareLoading] = useState(false);
  const comparisonRef = useRef<HTMLDivElement>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [currentPage, setCurrentPage] = useState(0);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  
  // Filter states
  const [therapeuticAreaFilter, setTherapeuticAreaFilter] = useState("");
  const [regionFilter, setRegionFilter] = useState("");
  const [studyTypeFilter, setStudyTypeFilter] = useState("");
  const [journalFilter, setJournalFilter] = useState("");

  const { toast } = useToast();

  const loadArticles = async (page = 0, append = false) => {
    try {
      if (!append) setIsLoading(true);
      else setIsLoadingMore(true);

      const params = new URLSearchParams({
        search: searchTerm,
        limit: '50',
        skip: (page * 50).toString()
      });

      const response = await fetch(`/api/cea-publications?${params}`);
      
      if (!response.ok) {
        throw new Error(`Failed to load articles: ${response.status}`);
      }

      const data = await response.json();
      
      if (append) {
        setArticles(prev => [...prev, ...data.articles]);
      } else {
        setArticles(data.articles);
      }
      
      setTotalCount(data.total);
      setHasMore(data.hasMore);
      setCurrentPage(page);

    } catch (error) {
      console.error('Error loading articles:', error);
      toast({
        title: "Error",
        description: "Failed to load CEA publications",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
      setIsLoadingMore(false);
    }
  };

  useEffect(() => {
    loadArticles();
  }, []);

  useEffect(() => {
    // Apply client-side filters
    let filtered = articles;

    if (therapeuticAreaFilter) {
      filtered = filtered.filter(article => 
        article.therapeutic_area?.toLowerCase().includes(therapeuticAreaFilter.toLowerCase())
      );
    }

    if (regionFilter) {
      filtered = filtered.filter(article => 
        article.region?.toLowerCase().includes(regionFilter.toLowerCase())
      );
    }

    if (studyTypeFilter) {
      filtered = filtered.filter(article => 
        article.study_type?.toLowerCase().includes(studyTypeFilter.toLowerCase())
      );
    }

    if (journalFilter) {
      filtered = filtered.filter(article => 
        article.journal?.toLowerCase().includes(journalFilter.toLowerCase())
      );
    }

    setFilteredArticles(filtered);
  }, [articles, therapeuticAreaFilter, regionFilter, studyTypeFilter, journalFilter]);

  const handleSearch = () => {
    setCurrentPage(0);
    loadArticles(0, false);
  };

  const handleLoadMore = () => {
    loadArticles(currentPage + 1, true);
  };

  const openArticleDialog = (article: CEAArticle) => {
    setSelectedArticle(article);
    setIsDialogOpen(true);
  };

  const clearFilters = () => {
    setTherapeuticAreaFilter("");
    setRegionFilter("");
    setStudyTypeFilter("");
    setJournalFilter("");
  };

  // Get unique values for filter dropdowns
  const uniqueTherapeuticAreas = [...new Set(articles.map(a => a.therapeutic_area).filter(Boolean))];
  const uniqueRegions = [...new Set(articles.map(a => a.region).filter(Boolean))];
  const uniqueStudyTypes = [...new Set(articles.map(a => a.study_type).filter(Boolean))];
  const uniqueJournals = [...new Set(articles.map(a => a.journal).filter(Boolean))];

  const toggleCompare = (id: string) => {
    if (compareIds.includes(id)) {
      setCompareIds(compareIds.filter((c) => c !== id));
    } else {
      setCompareIds([...compareIds, id]);
    }
  };

  const clearComparison = () => {
    setCompareIds([]);
    setComparisonSummary("");
  };

  const scrollToComparison = () => {
    if (comparisonRef.current) {
      comparisonRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  };

  const exportComparisonCsv = () => {
    if (compareIds.length === 0) return;
    const items = filteredArticles.filter((a) => compareIds.includes(a._id));
    const header = ["Title", "Authors", "Journal", "Publication Date", "PMID", "PMCID", "Vendor", "Indication", "Perspective", "Model Type", "Population", "Intervention", "Comparators", "Outcomes", "Economic Outcome", "Abstract"];
    const rows = items.map((a) =>
      [
        a.title || "",
        a.authors || "",
        a.journal || "",
        a.publication_date || "",
        a.pmid || "",
        a.pmcid || "",
        a.vendor || "",
        a.indication || "",
        a.perspective || "",
        a.model_type || "",
        a.population || "",
        a.intervention || "",
        a.comparators || "",
        a.outcomes || "",
        a.economic_outcome || "",
        a.abstract || "",
      ]
        .map((v) => `"${v.replace(/\"/g, '""')}"`)
        .join(","),
    );
    const csv = [header.join(","), ...rows].join("\n");
    const blob = new Blob([csv], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "cea-publications-comparison.csv";
    a.click();
    URL.revokeObjectURL(url);
  };

  const fetchChat = async (prompt: string) => {
    const res = await fetch("/api/chat", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ messages: [{ role: "user", content: prompt }] }),
    });
    if (!res.ok) throw new Error("ChatGPT request failed");
    const data = await res.json();
    return data.content as string;
  };

  const summarizeComparison = async () => {
    const items = filteredArticles.filter((a) => compareIds.includes(a._id));
    if (items.length < 2) return;
    setCompareLoading(true);
    const text = items
      .slice(0, 5)
      .map((a) => `Title: ${a.title}\nIndication: ${a.indication || 'N/A'}\nIntervention: ${a.intervention || 'N/A'}\nComparators: ${a.comparators || 'N/A'}\nOutcomes: ${a.outcomes || 'N/A'}\nEconomic Analysis: ${a.econ_analysis || 'N/A'}\nPerspective: ${a.perspective || 'N/A'}\nModel Type: ${a.model_type || 'N/A'}\nAbstract: ${a.abstract || 'N/A'}`)
      .join("\n\n");
    const prompt = `As an HEOR (Health Economics and Outcomes Research) and Market Access expert, compare these CEA publications and summarize their key similarities and differences in up to five • bullet points without numbers. Focus on economic methodology, clinical outcomes, market access implications, and study quality from an HEOR perspective.\n\n${text}`;
    try {
      const resp = await fetchChat(prompt);
      setComparisonSummary(resp);
    } finally {
      setCompareLoading(false);
    }
  };

  const compareItems = filteredArticles.filter((a) => compareIds.includes(a._id));

  return (
    <div className="container mx-auto py-8 px-4">
      <SubpageHeader current="cea-publications" />
      
      <h1 className="text-3xl font-bold mb-4">CEA Publications Database</h1>
      
      <div className="prose max-w-none mb-6">
        <p>
          Explore our comprehensive database of Cost-Effectiveness Analysis (CEA) publications. 
          This collection contains detailed information about health economic studies and analyses 
          from various therapeutic areas and regions.
        </p>
        <p className="font-medium mb-0">
          Total publications: <span className="font-bold">{totalCount.toLocaleString()}</span>
        </p>
      </div>
      
      <Tabs defaultValue="search" className="mb-6">
        <TabsList>
          <TabsTrigger value="search">Search</TabsTrigger>
          <TabsTrigger value="filter">Filter</TabsTrigger>
        </TabsList>
        
        <TabsContent value="search">
          <div className="flex gap-2 mb-4">
            <Input
              placeholder="Search publications by title, abstract, journal, authors, or therapeutic area..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              className="flex-1"
            />
            <Button onClick={handleSearch} disabled={isLoading}>
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Search className="h-4 w-4" />
              )}
            </Button>
          </div>
        </TabsContent>
        
        <TabsContent value="filter">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Therapeutic Area</label>
              <select
                value={therapeuticAreaFilter}
                onChange={(e) => setTherapeuticAreaFilter(e.target.value)}
                className="w-full p-2 border rounded-md"
              >
                <option value="">All Areas</option>
                {uniqueTherapeuticAreas.map(area => (
                  <option key={area} value={area}>{area}</option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">Region</label>
              <select
                value={regionFilter}
                onChange={(e) => setRegionFilter(e.target.value)}
                className="w-full p-2 border rounded-md"
              >
                <option value="">All Regions</option>
                {uniqueRegions.map(region => (
                  <option key={region} value={region}>{region}</option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">Study Type</label>
              <select
                value={studyTypeFilter}
                onChange={(e) => setStudyTypeFilter(e.target.value)}
                className="w-full p-2 border rounded-md"
              >
                <option value="">All Types</option>
                {uniqueStudyTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">Journal</label>
              <select
                value={journalFilter}
                onChange={(e) => setJournalFilter(e.target.value)}
                className="w-full p-2 border rounded-md"
              >
                <option value="">All Journals</option>
                {uniqueJournals.slice(0, 50).map(journal => (
                  <option key={journal} value={journal}>{journal}</option>
                ))}
              </select>
            </div>
          </div>
          
          <Button variant="outline" onClick={clearFilters} className="mb-4">
            <Filter className="h-4 w-4 mr-2" />
            Clear Filters
          </Button>
        </TabsContent>
      </Tabs>

      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : (
        <>
          <div className="grid gap-4 mb-6">
            {filteredArticles.map((article) => (
              <Card key={article._id}>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-lg flex-1 pr-20">
                      <a
                        href={article.pmid ? `https://pubmed.ncbi.nlm.nih.gov/${article.pmid}/` : '#'}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="hover:underline visited:text-blue-700 dark:visited:text-blue-400"
                        onClick={article.pmid ? undefined : (e) => { e.preventDefault(); openArticleDialog(article); }}
                      >
                        {article.title || 'Untitled'}
                      </a>
                      {article.doi && (
                        <a
                          href={`https://doi.org/${article.doi}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          aria-label="Open DOI"
                          className="text-primary hover:text-primary/80 visited:text-blue-700 inline-block ml-1"
                          title="View DOI"
                        >
                          <ExternalLink className="h-3 w-3" />
                        </a>
                      )}
                    </CardTitle>
                    <div className="flex items-center gap-2">
                      <Checkbox
                        checked={compareIds.includes(article._id)}
                        onCheckedChange={() => toggleCompare(article._id)}
                      />
                      {article.region && (() => {
                        const countryCode = getCountryCode(article.region);
                        return countryCode ? (
                          <div 
                            className="flex-shrink-0 w-5 h-5 relative rounded-full overflow-hidden border border-gray-200 dark:border-gray-700" 
                            title={article.region}
                          >
                            <Image
                              src={`/flags/${countryCode.toLowerCase()}.svg`}
                              alt={`${article.region} flag`}
                              fill
                              className="object-cover"
                              sizes="20px"
                            />
                          </div>
                        ) : null;
                      })()}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="ml-2 h-6 px-2 hover:bg-gray-200 hover:text-primary dark:hover:bg-gray-700"
                        onClick={() => openArticleDialog(article)}
                      >
                        <span className="text-xs">View Details</span>
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {/* Publication Info */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                      {article.authors && (
                        <div>
                          <strong>Authors: <AUTHORS>
                        </div>
                      )}
                      {article.journal && (
                        <div>
                          <strong>Journal:</strong> {article.journal}
                        </div>
                      )}
                      {article.publication_date && (
                        <div>
                          <strong>Publication Date:</strong> {article.publication_date}
                        </div>
                      )}
                      {article.pmid && (
                        <div>
                          <strong>PMID:</strong> {article.pmid}
                        </div>
                      )}
                      {article.pmcid && (
                        <div>
                          <strong>PMCID:</strong> {article.pmcid}
                        </div>
                      )}
                      {article.vendor && (
                        <div>
                          <strong>Vendor:</strong> {article.vendor}
                        </div>
                      )}
                    </div>

                    {/* Study Details */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                      {article.indication && (
                        <div>
                          <strong>Indication:</strong> {article.indication}
                        </div>
                      )}
                      {article.perspective && (
                        <div>
                          <strong>Perspective:</strong> {article.perspective}
                        </div>
                      )}
                      {article.model_type && (
                        <div>
                          <strong>Model Type:</strong> {article.model_type}
                        </div>
                      )}
                      {article.population && (
                        <div>
                          <strong>Population:</strong> {article.population}
                        </div>
                      )}
                      {article.health_states && (
                        <div>
                          <strong>Health States:</strong> {article.health_states}
                        </div>
                      )}
                      {article.econ_analysis && (
                        <div>
                          <strong>Economic Analysis:</strong> {article.econ_analysis}
                        </div>
                      )}
                    </div>

                    {/* Interventions & Outcomes */}
                    {(article.intervention || article.comparators || article.outcomes || article.economic_outcome) && (
                      <div className="space-y-2 text-sm">
                        {article.intervention && (
                          <div>
                            <strong>Intervention:</strong> <span className="text-muted-foreground">{article.intervention}</span>
                          </div>
                        )}
                        {article.comparators && (
                          <div>
                            <strong>Comparators:</strong> <span className="text-muted-foreground">{article.comparators}</span>
                          </div>
                        )}
                        {article.outcomes && (
                          <div>
                            <strong>Outcomes:</strong> <span className="text-muted-foreground">{article.outcomes}</span>
                          </div>
                        )}
                        {article.economic_outcome && (
                          <div>
                            <strong>Economic Outcome:</strong> <span className="text-muted-foreground">{article.economic_outcome}</span>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Abstract */}
                    {article.abstract && (
                      <div>
                        <strong>Abstract:</strong>
                        <p className="text-sm text-muted-foreground leading-relaxed mt-1">
                          {article.abstract.length > 400 ? `${article.abstract.slice(0, 400)}...` : article.abstract}
                        </p>
                      </div>
                    )}

                    {/* Badges */}
                    <div className="flex flex-wrap gap-2">
                      {article.therapeutic_area && (
                        <Badge variant="secondary" className="text-xs">
                          {article.therapeutic_area}
                        </Badge>
                      )}
                      {article.region && (
                        <Badge variant="outline" className="text-xs">
                          {article.region}
                        </Badge>
                      )}
                      {article.study_type && (
                        <Badge variant="default" className="text-xs">
                          {article.study_type}
                        </Badge>
                      )}
                      {article.affiliation_types && (
                        <Badge variant="secondary" className="text-xs">
                          {article.affiliation_types}
                        </Badge>
                      )}
                    </div>

                    {/* Links */}
                    <div className="flex items-center gap-4 pt-2 border-t">
                      {article.pmid && (
                        <a
                          href={`https://pubmed.ncbi.nlm.nih.gov/${article.pmid}/`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-xs text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300 flex items-center gap-1"
                        >
                          <FileText className="h-3 w-3" />
                          View on PubMed
                        </a>
                      )}
                      {article.doi && (
                        <a
                          href={`https://doi.org/${article.doi}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-xs text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300 flex items-center gap-1"
                        >
                          <ExternalLink className="h-3 w-3" />
                          View DOI
                        </a>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {hasMore && (
            <div className="flex justify-center">
              <Button 
                onClick={handleLoadMore} 
                disabled={isLoadingMore}
                variant="outline"
              >
                {isLoadingMore ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : null}
                Load More Publications
              </Button>
            </div>
          )}
        </>
      )}

      {/* Comparison Section */}
      {compareItems.length > 1 && (
        <div ref={comparisonRef} className="my-8 overflow-auto space-y-2">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold">Comparison ({compareItems.length} publications)</h2>
            <div className="flex gap-2 items-center">
              <Button variant="outline" size="sm" onClick={exportComparisonCsv} className="gap-1">
                <Download className="h-4 w-4" /> Export
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={summarizeComparison}
                disabled={compareLoading}
                className="gap-1"
              >
                {compareLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" /> Loading...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4" /> Summarize
                  </>
                )}
              </Button>
              <Button variant="outline" size="sm" onClick={clearComparison}>
                Clear
              </Button>
            </div>
          </div>
          
          {comparisonSummary && (
            <div className="mt-4 p-2 rounded-md bg-blue-50 dark:bg-blue-900/40 relative">
              <button
                onClick={() => setComparisonSummary("")}
                className="absolute top-1 right-1 text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-800 rounded-full p-1"
                aria-label="Close comparison summary"
              >
                <X className="h-3 w-3" />
              </button>
              <p className="font-semibold text-blue-700 dark:text-blue-300 flex items-center gap-1 mb-1">
                <Sparkles className="h-4 w-4" /> HEOR & Market Access Comparison Summary
              </p>
              <ul className="list-[circle] pl-4 text-sm text-blue-700 dark:text-blue-300">
                {parseBullets(comparisonSummary).map((b, i) => (
                  <li key={i}>{b}</li>
                ))}
              </ul>
            </div>
          )}

          <table className="min-w-full text-sm border-2 border-gray-300 dark:border-gray-600 rounded-md overflow-hidden">
            <thead className="bg-muted">
              <tr>
                <th className="border border-gray-300 dark:border-gray-600 px-3 py-2 w-32 text-left">Field</th>
                {compareItems.map((item) => (
                  <th key={item._id} className="border border-gray-300 dark:border-gray-600 px-3 py-2 w-60 text-left">
                    <div className="flex items-start justify-between">
                      <span className="text-xs">{item.title || 'Untitled'}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 px-2"
                        onClick={() => toggleCompare(item._id)}
                        aria-label="Remove from comparison"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {[
                { key: 'authors', label: 'Authors' },
                { key: 'journal', label: 'Journal' },
                { key: 'publication_date', label: 'Publication Date' },
                { key: 'pmid', label: 'PMID' },
                { key: 'pmcid', label: 'PMCID' },
                { key: 'vendor', label: 'Vendor' },
                { key: 'indication', label: 'Indication' },
                { key: 'perspective', label: 'Perspective' },
                { key: 'model_type', label: 'Model Type' },
                { key: 'population', label: 'Population' },
                { key: 'intervention', label: 'Intervention' },
                { key: 'comparators', label: 'Comparators' },
                { key: 'outcomes', label: 'Outcomes' },
                { key: 'economic_outcome', label: 'Economic Outcome' },
                { key: 'econ_analysis', label: 'Economic Analysis' },
                { key: 'therapeutic_area', label: 'Therapeutic Area' },
                { key: 'region', label: 'Region' },
                { key: 'affiliation_types', label: 'Affiliation Types' }
              ].map((field) => (
                <tr key={field.key}>
                  <td className="border border-gray-300 dark:border-gray-600 px-3 py-2 font-medium bg-muted/50">
                    {field.label}
                  </td>
                  {compareItems.map((item) => (
                    <td key={item._id} className="border border-gray-300 dark:border-gray-600 px-3 py-2 text-xs">
                      {(item as any)[field.key] || '-'}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Article Detail Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          {selectedArticle && (
            <>
              <DialogHeader>
                <DialogTitle className="text-xl pr-8">
                  {selectedArticle.title || 'Untitled'}
                </DialogTitle>
                <div className="flex items-center gap-2 mt-2">
                  {selectedArticle.region && (() => {
                    const countryCode = getCountryCode(selectedArticle.region);
                    return countryCode ? (
                      <div 
                        className="flex-shrink-0 w-5 h-5 relative rounded-full overflow-hidden border border-gray-200 dark:border-gray-700" 
                        title={selectedArticle.region}
                      >
                        <Image
                          src={`/flags/${countryCode.toLowerCase()}.svg`}
                          alt={`${selectedArticle.region} flag`}
                          fill
                          className="object-cover"
                          sizes="20px"
                        />
                      </div>
                    ) : null;
                  })()}
                  <div className="flex flex-wrap gap-2">
                    {selectedArticle.therapeutic_area && (
                      <Badge variant="secondary" className="text-xs">
                        {selectedArticle.therapeutic_area}
                      </Badge>
                    )}
                    {selectedArticle.region && (
                      <Badge variant="outline" className="text-xs">
                        {selectedArticle.region}
                      </Badge>
                    )}
                    {selectedArticle.study_type && (
                      <Badge variant="default" className="text-xs">
                        {selectedArticle.study_type}
                      </Badge>
                    )}
                  </div>
                </div>
              </DialogHeader>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                {/* Publication Information */}
                <div className="space-y-3">
                  <h3 className="font-bold text-lg border-b pb-1">Publication Information</h3>
                  <div className="space-y-2">
                    {selectedArticle.authors && (
                      <div>
                        <div className="font-medium text-amber-700 dark:text-amber-400">Authors: <AUTHORS>
                        <div className="text-sm">{selectedArticle.authors}</div>
                      </div>
                    )}
                    {selectedArticle.journal && (
                      <div>
                        <div className="font-medium text-amber-700 dark:text-amber-400">Journal:</div>
                        <div className="text-sm">{selectedArticle.journal}</div>
                      </div>
                    )}
                    {selectedArticle.publication_date && (
                      <div>
                        <div className="font-medium text-amber-700 dark:text-amber-400">Publication Date:</div>
                        <div className="text-sm">{selectedArticle.publication_date}</div>
                      </div>
                    )}
                    {selectedArticle.pmid && (
                      <div>
                        <div className="font-medium text-amber-700 dark:text-amber-400">PMID:</div>
                        <div className="text-sm">
                          <a
                            href={`https://pubmed.ncbi.nlm.nih.gov/${selectedArticle.pmid}/`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300 underline"
                          >
                            {selectedArticle.pmid}
                          </a>
                        </div>
                      </div>
                    )}
                    {selectedArticle.pmcid && (
                      <div>
                        <div className="font-medium text-amber-700 dark:text-amber-400">PMCID:</div>
                        <div className="text-sm">{selectedArticle.pmcid}</div>
                      </div>
                    )}
                    {selectedArticle.doi && (
                      <div>
                        <div className="font-medium text-amber-700 dark:text-amber-400">DOI:</div>
                        <div className="text-sm">
                          <a
                            href={`https://doi.org/${selectedArticle.doi}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300 underline"
                          >
                            {selectedArticle.doi} <ExternalLink className="inline h-3 w-3" />
                          </a>
                        </div>
                      </div>
                    )}
                    {selectedArticle.vendor && (
                      <div>
                        <div className="font-medium text-amber-700 dark:text-amber-400">Matched Vendor:</div>
                        <div className="text-sm">{selectedArticle.vendor}</div>
                      </div>
                    )}
                    {selectedArticle.affiliation_types && (
                      <div>
                        <div className="font-medium text-amber-700 dark:text-amber-400">Affiliation Types:</div>
                        <div className="text-sm">{selectedArticle.affiliation_types}</div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Study Design & Analysis */}
                <div className="space-y-3">
                  <h3 className="font-bold text-lg border-b pb-1">Study Design & Analysis</h3>
                  <div className="space-y-2">
                    {selectedArticle.indication && (
                      <div>
                        <div className="font-medium text-amber-700 dark:text-amber-400">Indication:</div>
                        <div className="text-sm">{selectedArticle.indication}</div>
                      </div>
                    )}
                    {selectedArticle.perspective && (
                      <div>
                        <div className="font-medium text-amber-700 dark:text-amber-400">Perspective:</div>
                        <div className="text-sm">{selectedArticle.perspective}</div>
                      </div>
                    )}
                    {selectedArticle.model_type && (
                      <div>
                        <div className="font-medium text-amber-700 dark:text-amber-400">Model Type:</div>
                        <div className="text-sm">{selectedArticle.model_type}</div>
                      </div>
                    )}
                    {selectedArticle.population && (
                      <div>
                        <div className="font-medium text-amber-700 dark:text-amber-400">Population:</div>
                        <div className="text-sm">{selectedArticle.population}</div>
                      </div>
                    )}
                    {selectedArticle.health_states && (
                      <div>
                        <div className="font-medium text-amber-700 dark:text-amber-400">Health States:</div>
                        <div className="text-sm">{selectedArticle.health_states}</div>
                      </div>
                    )}
                    {selectedArticle.econ_analysis && (
                      <div>
                        <div className="font-medium text-amber-700 dark:text-amber-400">Economic Analysis:</div>
                        <div className="text-sm">{selectedArticle.econ_analysis}</div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Interventions & Outcomes */}
                <div className="space-y-3 md:col-span-2">
                  <h3 className="font-bold text-lg border-b pb-1">Interventions & Outcomes</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {selectedArticle.intervention && (
                      <div>
                        <div className="font-medium text-amber-700 dark:text-amber-400">Intervention:</div>
                        <div className="text-sm">{selectedArticle.intervention}</div>
                      </div>
                    )}
                    {selectedArticle.comparators && (
                      <div>
                        <div className="font-medium text-amber-700 dark:text-amber-400">Comparators:</div>
                        <div className="text-sm">{selectedArticle.comparators}</div>
                      </div>
                    )}
                    {selectedArticle.outcomes && (
                      <div>
                        <div className="font-medium text-amber-700 dark:text-amber-400">Outcomes:</div>
                        <div className="text-sm">{selectedArticle.outcomes}</div>
                      </div>
                    )}
                    {selectedArticle.economic_outcome && (
                      <div>
                        <div className="font-medium text-amber-700 dark:text-amber-400">Economic Outcome:</div>
                        <div className="text-sm">{selectedArticle.economic_outcome}</div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Abstract */}
                {selectedArticle.abstract && (
                  <div className="space-y-3 md:col-span-2">
                    <h3 className="font-bold text-lg border-b pb-1">Abstract</h3>
                    <div className="text-sm leading-relaxed">{selectedArticle.abstract}</div>
                  </div>
                )}
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>

      {/* Floating Comparison Button */}
      {compareItems.length > 1 && (
        <Button
          onClick={scrollToComparison}
          className="fixed bottom-4 right-4 z-50 rounded-full bg-amber-400 text-black hover:bg-amber-500 shadow-md"
          size="sm"
        >
          View Comparison ({compareItems.length})
        </Button>
      )}
    </div>
  );
}
































