"use client";
import React, { useState, useMemo, useRef, useLayoutEffect } from "react";
import ReactD<PERSON><PERSON>iewer from 'react-diff-viewer-continued';
import { useClipboard } from "@/contexts/ClipboardContext";
import type { Note } from "@/contexts/ClipboardContext";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { X, ClipboardList, Plus, Camera, Pencil, Copy, Download, Upload, ChevronLeft, ChevronRight, Highlighter, Save, Minimize2, Check, Search, Loader2, Trash2, Clock, Tag, ArrowUp, ArrowDown } from "lucide-react";
import { cn } from "@/lib/utils";
import html2canvas from "html2canvas";
import { useLocalStorage } from "@/hooks/use-local-storage";
import { useToast } from "@/components/ui/use-toast";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";

interface TagOption {
  label: string;
  color: string;
  textColor: string;
}

const DEFAULT_TAG_COLOR = "#e2e8f0";
const DEFAULT_TAG_TEXT_COLOR = "#000000";

function estimateTokens(text: string) {
  return Math.ceil(text.length / 4);
}

export function ClipboardPanel({
  zIndex = 99999,
  onFocus,
}: { zIndex?: number; onFocus?: () => void }) {
  const { notes, addNote, removeNote, editNote, setColor, setTags } = useClipboard();
  const { toast } = useToast();
  const [open, setOpen] = useState(false);
  const [newText, setNewText] = useState("");
  const [prompt, setPrompt] = useState("");
  const [selected, setSelected] = useState<Record<string, boolean>>({});
  const [outputs, setOutputs] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [sessions, setSessions] = useLocalStorage<any[]>("drugDocsSessions", []);
  const [model, setModel] = useState("gpt-4.1-mini");
  const [newRef, setNewRef] = useState("");
  const [newSource, setNewSource] = useState("");
  const [search, setSearch] = useState("");
  const [searchInput, setSearchInput] = useState("");
  const [filterTags, setFilterTags] = useState<string[]>([]);
  const [newImage, setNewImage] = useState<string | null>(null);
  const [imageCaption, setImageCaption] = useState("");
  const [pdfSrc, setPdfSrc] = useState<string | null>(null);
  const [pdfTitle, setPdfTitle] = useState<string | null>(null);
  const [editTarget, setEditTarget] = useState<string | null>(null);
  const [editText, setEditText] = useState("");
  const [editRef, setEditRef] = useState("");
  const [storedAvailableTags, setStoredAvailableTags] = useLocalStorage<TagOption[]>(
    "clipboardTags",
    [
      { label: "Safety", color: DEFAULT_TAG_COLOR, textColor: DEFAULT_TAG_TEXT_COLOR },
      { label: "Efficacy", color: DEFAULT_TAG_COLOR, textColor: DEFAULT_TAG_TEXT_COLOR },
      { label: "RWE", color: DEFAULT_TAG_COLOR, textColor: DEFAULT_TAG_TEXT_COLOR },
    ],
  );
  const availableTags = useMemo<TagOption[]>(
    () =>
      storedAvailableTags.map((t: any) =>
        typeof t === "string"
          ? { label: t, color: DEFAULT_TAG_COLOR, textColor: DEFAULT_TAG_TEXT_COLOR }
          : {
              label: t.label ?? "",
              color: t.color ?? DEFAULT_TAG_COLOR,
              textColor: t.textColor ?? DEFAULT_TAG_TEXT_COLOR,
            },
      ),
    [storedAvailableTags],
  );
  const availableTagLabels = useMemo(() => availableTags.map((t) => t.label), [availableTags]);
  const setAvailableTags = (tags: TagOption[]) => setStoredAvailableTags(tags);
  const [newTags, setNewTags] = useState<string[]>([]);
  const [highlightMenu, setHighlightMenu] = useState<string | null>(null);
  const [tagInput, setTagInput] = useState("");
  const [tagColor, setTagColor] = useState(DEFAULT_TAG_COLOR);
  const [tagTextColor, setTagTextColor] = useState(DEFAULT_TAG_TEXT_COLOR);

  const createTag = (apply: (tag: string) => void) => {
    const label = tagInput.trim();
    if (!label) return;
    if (!availableTagLabels.includes(label)) {
      setAvailableTags([
        ...availableTags,
        { label, color: tagColor, textColor: tagTextColor },
      ]);
    }
    apply(label);
    setTagInput("");
    setTagColor(DEFAULT_TAG_COLOR);
    setTagTextColor(DEFAULT_TAG_TEXT_COLOR);
  };

  const deleteAvailableTag = (label: string) => {
    setAvailableTags(availableTags.filter((t) => t.label !== label));
    setNewTags((prev) => prev.filter((t) => t !== label));
  };

  const updateTagColor = (label: string, color: string) => {
    setAvailableTags(
      availableTags.map((t) =>
        t.label === label ? { ...t, color } : t,
      ),
    );
  };

  const updateTagTextColor = (label: string, color: string) => {
    setAvailableTags(
      availableTags.map((t) =>
        t.label === label ? { ...t, textColor: color } : t,
      ),
    );
  };

  const getTagInfo = (label: string) =>
    availableTags.find((t) => t.label === label);

  const toggleTag = (id: string, tag: string) => {
    const note = notes.find((n) => n.id === id);
    if (!note) return;
    const tags = note.tags || [];
    const has = tags.includes(tag);
    const updated = has ? tags.filter((t) => t !== tag) : [...tags, tag];
    setTags(id, updated);
    if (!availableTagLabels.includes(tag)) {
      setAvailableTags([
        ...availableTags,
        { label: tag, color: DEFAULT_TAG_COLOR, textColor: DEFAULT_TAG_TEXT_COLOR },
      ]);
    }
  };

  const addTagToNew = (tag: string) => {
    if (!newTags.includes(tag)) {
      setNewTags([...newTags, tag]);
    }
    if (!availableTagLabels.includes(tag)) {
      setAvailableTags([
        ...availableTags,
        { label: tag, color: DEFAULT_TAG_COLOR, textColor: DEFAULT_TAG_TEXT_COLOR },
      ]);
    }
  };

  const removeNewTag = (tag: string) => {
    setNewTags(newTags.filter((t) => t !== tag));
  };

  const toggleFilterTag = (tag: string) => {
    setFilterTags((prev) =>
      prev.includes(tag) ? prev.filter((t) => t !== tag) : [...prev, tag],
    );
  };
  const [page, setPage] = useState<"notes" | "context" | "records">("notes");
  const [contextItems, setContextItems] = useState<{ id: string; type: 'note' | 'context'; text?: string }[]>([]);
  const [draggingId, setDraggingId] = useState<string | null>(null);
  const headingClass = "text-base font-medium text-muted-foreground";
  // Add a function to calculate default size based on screen width
  const calculateDefaultSize = () => {
    // Default to 1/3 of screen width, with min/max constraints
    const screenWidth = typeof window !== 'undefined' ? window.innerWidth : 1200;
    const defaultWidth = Math.max(320, Math.min(600, screenWidth / 3));
    
    return {
      width: defaultWidth,
      height: 480 // Keep default height
    };
  };

  // Update the size state to use the calculated default
  const [size, setSize] = useState(calculateDefaultSize);
  const panelRef = useRef<HTMLDivElement | null>(null);
  const headerRef = useRef<HTMLDivElement | null>(null);
  const [minWidth, setMinWidth] = useState(320);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const startPos = useRef<{ x: number; y: number } | null>(null);
  const startSize = useRef<{ width: number; height: number } | null>(null);
  const activeHandle = useRef<string | null>(null);
  const [addingVariant, setAddingVariant] = useState<string | null>(null);
  const [variantText, setVariantText] = useState("");
  const [currentVariantIndexes, setCurrentVariantIndexes] = useState<Record<string, number>>({});
  const [copiedId, setCopiedId] = useState<string | null>(null);
  const [deleteTarget, setDeleteTarget] = useState<string | null>(null);
  const [deleteSessionId, setDeleteSessionId] = useState<string | null>(null);
  const [savedId, setSavedId] = useState<string | null>(null);
  const [saveDialogOpen, setSaveDialogOpen] = useState(false);
  const [sessionTitleInput, setSessionTitleInput] = useState("");
  const [compareA, setCompareA] = useState<any | null>(null);
  const [compareB, setCompareB] = useState<any | null>(null);
  const [compareOptions, setCompareOptions] = useState({
    context: true,
    prompt: true,
    outputs: true,
  });
  const [showTimestamps, setShowTimestamps] = useState(false);
  const [expandedNotes, setExpandedNotes] = useState<Record<string, boolean>>({});
  const [savedPrompts, setSavedPrompts] = useLocalStorage<{id: string, title: string, prompt: string, createdAt: number}[]>("savedPrompts", []);
  const [newPromptTitle, setNewPromptTitle] = useState("");
  const [newPromptText, setNewPromptText] = useState("");
  // Add state for color picker
  // Pastel colors for highlighting text
  const colorOptions = [
    "#FFF59D",
    "#F8BBD0",
    "#B2DFDB",
    "#FFCC80",
    "#B39DDB",
    "#C5E1A5",
  ];

  const groupBorderColors = [
    "#F8D7DA",
    "#D1E7DD",
    "#E2E3E5",
    "#FFF3CD",
    "#DDEBF7",
    "#E2D6F3",
  ];

  const selectedNotes = notes.filter((n) => selected[n.id]);

  const contextAnchor = selectedNotes.length > 0 ? "#context-heading" : "#context-section";
  const promptAnchor = prompt.trim() ? "#prompt-heading" : "#prompt-section";

  const orderedNotes = useMemo(() => {
    return contextItems
      .filter(i => i.type === 'note' && selectedNotes.some(n => n.id === i.id))
      .map(i => selectedNotes.find(n => n.id === i.id)!) as Note[];
  }, [contextItems.map(i=>i.id).join(','), selectedNotes.map(n=>n.id).join(',')]);

  const groupColors = useMemo(() => {
    const map: Record<string, string> = {};
    let last = -1;
    orderedNotes.forEach((n, idx) => {
      let ci = idx % groupBorderColors.length;
      if (ci === last) ci = (ci + 1) % groupBorderColors.length;
      map[n.id] = groupBorderColors[ci];
      last = ci;
    });
    return map;
  }, [orderedNotes]);

  const measureHeaderWidth = React.useCallback(() => {
    if (!headerRef.current) return 320;
    const clone = headerRef.current.cloneNode(true) as HTMLElement;
    clone.style.position = 'absolute';
    clone.style.visibility = 'hidden';
    clone.style.width = 'max-content';
    clone.style.whiteSpace = 'nowrap';
    document.body.appendChild(clone);
    const width = clone.offsetWidth + 16; // extra padding
    document.body.removeChild(clone);
    return Math.max(320, width);
  }, []);

  useLayoutEffect(() => {
    if (!open) return;
    const w = measureHeaderWidth();
    setMinWidth(w);
    setSize(prev => ({ ...prev, width: Math.max(prev.width, w) }));
  }, [open, page, measureHeaderWidth]);

  const contextText = useMemo(() => {
    return contextItems
      .map((item) => {
        if (item.type === 'context') return item.text || '';
        const n = notes.find((nn) => nn.id === item.id);
        return n?.text || n?.caption || '';
      })
      .filter(Boolean)
      .join("\n\n");
  }, [contextItems, notes]);

  const totalTokens = estimateTokens(contextText + prompt);

  const runPrompt = async () => {
    setLoading(true);
    try {
      const res = await fetch("/api/chat", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          messages: [
            { role: "system", content: contextText },
            { role: "user", content: prompt },
          ],
          model,
        }),
      });
      const data = await res.json();
      setOutputs([data.content || ""]);
    } catch (e) {
      console.error("chat failed", e);
    } finally {
      setLoading(false);
    }
  };

  const captureScreenshot = async () => {
    const canvas = await html2canvas(document.body);
    const dataUrl = canvas.toDataURL("image/png");
    setNewImage(dataUrl);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => setNewImage(reader.result as string);
      reader.readAsDataURL(file);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const file = e.dataTransfer.files?.[0];
    if (file && file.type.startsWith("image/")) {
      const reader = new FileReader();
      reader.onload = () => setNewImage(reader.result as string);
      reader.readAsDataURL(file);
    }
  };

  const diffOutputs = () => {
    if (outputs.length < 2) return null;
    const [a, b, c] = outputs;
    const base = a.split(/\n+/);
    const compare = (t?: string) => {
      if (!t) return [];
      const lines = t.split(/\n+/);
      return lines.filter((l) => !base.includes(l));
    };
    return (
      <div className="mt-2 text-xs space-y-1">
        {outputs.map((o, idx) => (
          <div key={idx} className="border p-2">
            {compare(o).map((l, i) => (
              <p key={i} className="bg-yellow-100 dark:bg-yellow-900/40">
                {l}
              </p>
            ))}
          </div>
        ))}
      </div>
    );
  };

  const confirmSaveSession = () => {
    const session = {
      id: `${Date.now()}-${Math.random().toString(36).slice(2, 9)}`,
      time: Date.now(),
      context: contextText,
      prompt,
      outputs,
      title: sessionTitleInput,
    };
    setSessions([...sessions, session]);
    setSessionTitleInput('');
  };

  const groupedNotes = useMemo(() => {
    const groups: Record<string, Note[]> = {};

    const sorted = [...notes].sort((a, b) => b.createdAt - a.createdAt);

    // Safely add notes to groups
    sorted.forEach((n) => {
      if (!n || !n.id) return; // Skip invalid notes
      
      const base = n.editedFrom || n.id;
      if (!groups[base]) groups[base] = [];
      
      if (n.editedFrom) groups[base].push(n);
      else groups[base].unshift(n);
    });
    
    // Filter out empty groups and ensure each group has valid notes
    return Object.values(groups)
      .filter(group => group.length > 0 && group.every(note => note && note.id));
  }, [notes]);

  const filteredGroups = useMemo(() => {
    const q = search.toLowerCase();
    return groupedNotes.filter((g) =>
      g.some((n) => {
        const matchesSearch =
          !q ||
          n.text.toLowerCase().includes(q) ||
          (n.tags || []).some((t) => t.toLowerCase().includes(q));
        const matchesTags =
          filterTags.length === 0 ||
          filterTags.every((t) => n.tags?.includes(t) ?? false);
        return matchesSearch && matchesTags;
      }),
    );
  }, [groupedNotes, search, filterTags]);

  const startResize = (handle: string, e: React.MouseEvent) => {
    e.preventDefault();
    activeHandle.current = handle;
    startPos.current = { x: e.clientX, y: e.clientY };
    startSize.current = { ...size };
    document.addEventListener("mousemove", onResize);
    document.addEventListener("mouseup", stopResize);
  };

  const onResize = (e: MouseEvent) => {
    if (!activeHandle.current || !startPos.current || !startSize.current) return;
    const dx = e.clientX - startPos.current.x;
    const dy = e.clientY - startPos.current.y;
    let w = startSize.current.width;
    let h = startSize.current.height;
    if (activeHandle.current.includes("right")) w += dx;
    if (activeHandle.current.includes("left")) w -= dx;
    if (activeHandle.current.includes("bottom")) h += dy;
    if (activeHandle.current.includes("top")) h -= dy;
    
    // Set minimum width based on header and prevent exceeding screen width
    const maxWidth = window.innerWidth - 20;
    if (w < minWidth) w = minWidth;
    if (w > maxWidth) w = maxWidth;
    
    if (h < 300) h = 300;
    setSize({ width: w, height: h });
  };

  const stopResize = () => {
    document.removeEventListener("mousemove", onResize);
    document.removeEventListener("mouseup", stopResize);
    activeHandle.current = null;
  };

  const initializeVariantIndexes = () => {
    const indexes: Record<string, number> = {};
    
    groupedNotes.forEach(group => {
      if (!group || group.length === 0 || !group[0]) return;
      
      const baseNote = group[0];
      const baseId = baseNote.editedFrom || baseNote.id;
      
      if (!baseId) return; // Skip if baseId is undefined
      
      if (!currentVariantIndexes[baseId]) {
        indexes[baseId] = 0;
      }
    });
    
    setCurrentVariantIndexes(prev => ({...prev, ...indexes}));
  };

  React.useEffect(() => {
    initializeVariantIndexes();
  }, [groupedNotes.length]);

  const copyNoteText = (text: string, id: string) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopiedId(id);
      toast({ title: "Text copied to clipboard" });
      setTimeout(() => setCopiedId(null), 2000);
    });
  };

  // Update the addNote function to capture search context
  // This would be called from other components when text is selected
  // For example, in a search results page:

  // Example implementation for capturing search context
  const captureSearchContext = (text: string, searchContext: string) => {
    addNote(text, window.location.href, undefined, searchContext);
  };

  React.useEffect(() => {
    const ids = selectedNotes.map((n) => n.id);
    setContextItems((prev) => {
      const filtered = prev.filter(
        (it) => it.type === 'context' || ids.includes(it.id)
      );
      ids.forEach((id) => {
        if (!filtered.some((it) => it.id === id)) {
          filtered.push({ id, type: 'note' });
        }
      });
      return filtered;
    });
  }, [selectedNotes.map((n) => n.id).join(',')]);

  // You would need to expose this function and call it from your search pages
  // For example, in your PubMed explorer or other search pages:
  // 
  // const handleSaveToClipboard = (text) => {
  //   const searchContext = `Search: "${searchQuery}" with filters: ${activeFilters.join(', ')}`;
  //   clipboardContext.captureSearchContext(text, searchContext);
  // }; 

  // Update the safeRemoveNote function to handle edge cases
  const safeRemoveNote = (id: string) => {
    if (!id) return; // Skip if id is undefined
    
    // Find which group this note belongs to
    const groupIndex = groupedNotes.findIndex(group => 
      group && group.some(note => note && note.id === id)
    );
    
    if (groupIndex === -1) {
      // Note not found in any group, just remove it
      removeNote(id);
      return;
    }
    
    const group = groupedNotes[groupIndex];
    if (!group || group.length === 0) {
      removeNote(id);
      return;
    }
    
    const noteIndex = group.findIndex(note => note && note.id === id);
    if (noteIndex === -1) {
      removeNote(id);
      return;
    }
    
    const baseNote = group[0];
    if (!baseNote) {
      removeNote(id);
      return;
    }
    
    const baseId = baseNote.editedFrom || baseNote.id;
    if (!baseId) {
      removeNote(id);
      return;
    }
    
    // If we're removing the currently displayed variant
    if (currentVariantIndexes[baseId] === noteIndex) {
      // If this is the last variant in the group, select the previous one
      if (noteIndex === group.length - 1 && noteIndex > 0) {
        setCurrentVariantIndexes(prev => ({
          ...prev,
          [baseId]: noteIndex - 1
        }));
      }
    }
    
    // Remove the note
    removeNote(id);
    setContextItems((items) => items.filter((it) => it.id !== id));
    
    // If we just removed the last note in a group, clean up the variant index
    if (group.length === 1) {
      setCurrentVariantIndexes(prev => {
        const newIndexes = {...prev};
        delete newIndexes[baseId];
        return newIndexes;
      });
    }
  };

  const moveItem = (id: string, dir: number) => {
    setContextItems((items) => {
      const idx = items.findIndex((i) => i.id === id);
      if (idx === -1) return items;
      const newIdx = idx + dir;
      if (newIdx < 0 || newIdx >= items.length) return items;
      const arr = [...items];
      const [it] = arr.splice(idx, 1);
      arr.splice(newIdx, 0, it);
      return arr;
    });
  };

  const moveItemUp = (id: string) => moveItem(id, -1);
  const moveItemDown = (id: string) => moveItem(id, 1);

  const insertContextAt = (index: number) => {
    const id = `ctx-${Date.now()}-${Math.random().toString(36).slice(2, 6)}`;
    setContextItems((items) => {
      const arr = [...items];
      arr.splice(index, 0, { id, type: 'context', text: '' });
      return arr;
    });
  };

  const savePrompt = () => {
    if (!newPromptTitle.trim() || !newPromptText.trim()) return;
    
    const newPrompt = {
      id: `prompt-${Date.now()}-${Math.random().toString(36).slice(2, 9)}`,
      title: newPromptTitle.trim(),
      prompt: newPromptText.trim(),
      createdAt: Date.now()
    };
    
    setSavedPrompts([...savedPrompts, newPrompt]);
    setNewPromptTitle('');
    setNewPromptText('');
  };

  const deletePrompt = (id: string) => {
    setSavedPrompts(savedPrompts.filter(p => p.id !== id));
  };

  const usePrompt = (promptText: string) => {
    setPrompt(promptText);
    setPage('context');
  };

  // Add a resize listener to adjust size when window size changes
  React.useEffect(() => {
    const handleResize = () => {
      // Only adjust width if it's close to 1/3 of the previous screen size
      // This prevents resizing when the user has manually adjusted the width
      const screenWidth = window.innerWidth;
      const oneThirdScreen = screenWidth / 3;
      
      // Check if current width is within 20% of the previous 1/3 width
      // If so, adjust to maintain the 1/3 ratio
      const previousOneThird = (window.innerWidth + 20) / 3; // Approximate previous width
      const isCloseToOneThird = 
        size.width > previousOneThird * 0.8 && 
        size.width < previousOneThird * 1.2;
      
      if (isCloseToOneThird) {
        setSize(prev => ({
          ...prev,
          width: Math.max(minWidth, Math.min(600, oneThirdScreen))
        }));
      }
    };

    // Add event listener
    window.addEventListener('resize', handleResize);
    
    // Clean up
    return () => window.removeEventListener('resize', handleResize);
  }, [size.width]);


  return (
    <>
      <Button
        onClick={() => {
          if (!open) onFocus?.()
          setOpen((o) => !o)
        }}
        className="fixed bottom-4 right-4 z-50 rounded-full bg-amber-400 text-black hover:bg-amber-500"
        size="icon"
      >
        <ClipboardList className="h-5 w-5" />
      </Button>
      {open && (
        <div
          ref={panelRef}
          style={{ width: size.width, height: size.height, minWidth, zIndex }}
          className="fixed bottom-16 right-4 max-h-[90vh] bg-[#fbf7ee] text-card-foreground border shadow-lg rounded-xl flex flex-col"
          onMouseDown={() => onFocus?.()}
        >
          <div className="flex items-center justify-between p-2 border-b" ref={headerRef}>
            <div className="flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round" className="h-7 w-7 -mr-1">
                <path d="M5 8 Q6 14 7 20 L19 18 Q18 12 17 6 Z" fill="white"/>
                <path d="M6 6 Q6 12 6 18 L13 18 Q15.5 15.5 18 13 Q18 9.5 18 6 Z" fill="white"/>
                <path d="M13 18 Q13 15.5 13 13 Q15.5 13 18 13" fill="none"/>
                <line x1="8" y1="9" x2="15" y2="9" stroke="currentColor" strokeWidth="1" opacity="0.5"/>
                <line x1="8" y1="11.5" x2="14" y2="11.5" stroke="currentColor" strokeWidth="1" opacity="0.5"/>
                <line x1="8" y1="14" x2="11" y2="14" stroke="currentColor" strokeWidth="1" opacity="0.5"/>
              </svg>
              <h3 className="font-medium">Context Board</h3>
            </div>
            <div className="flex items-center gap-1">
            {page === 'context' && (
              <>
                <Button size="sm" variant="ghost" onClick={() => setPage('notes')}>Notes</Button>
                <Button size="sm" variant="ghost" onClick={() => setPage('records')}>Records</Button>
              </>
            )}
{page === 'records' && (
              <>
                <Button size="sm" variant="ghost" onClick={() => setPage('notes')}>Notes</Button>
                <Button asChild size="sm" variant="ghost" onClick={() => setPage('context')}>
                  <a href={contextAnchor}>Context</a>
                </Button>
                <Button asChild size="sm" variant="ghost" onClick={() => setPage('context')}>
                  <a href={promptAnchor}>Prompt</a>
                </Button>
              </>
            )}
            {page === 'notes' && (
              <>
                <Button asChild size="sm" variant="ghost" onClick={() => setPage('context')}>
                  <a href={contextAnchor}>Context</a>
                </Button>
                <Button asChild size="sm" variant="ghost" onClick={() => setPage('context')}>
                  <a href={promptAnchor}>Prompt</a>
                </Button>
                <Button size="sm" variant="ghost" onClick={() => setPage('records')}>Records</Button>
              </>
            )}
              <Button variant="ghost" size="icon" onClick={() => setOpen(false)} className="h-6 w-6 p-0">
                <Minimize2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <div className="p-2 overflow-y-auto flex-1 space-y-4">
            {page === 'notes' ? (
              <>
            <div
              className="space-y-1"
              onDragOver={(e) => e.preventDefault()}
              onDrop={handleDrop}
            >
              <div className="flex gap-1 items-end">
                <div className="flex flex-col flex-1 gap-1">
                  <Textarea
                    value={newText}
                    onChange={(e) => setNewText(e.target.value)}
                    placeholder="New note"
                    className="min-h-[80px]"
                  />
                  <div className="flex gap-1 items-end">
                    <Input
                      value={newRef}
                      onChange={(e) => setNewRef(e.target.value)}
                      placeholder="Reference"
                      className="flex-1 h-9"
                    />
                    <Input
                      type="url"
                      value={newSource}
                      onChange={(e) => setNewSource(e.target.value)}
                      placeholder="Source"
                      className="flex-1 h-9"
                    />
                  </div>
                  <div className="flex gap-1 items-center flex-wrap">
                    {newTags.map((tag) => {
                      const info = getTagInfo(tag);
                      return (
                        <Badge
                          key={tag}
                          variant="secondary"
                          className="gap-1"
                          style={{ backgroundColor: info?.color, color: info?.textColor }}
                        >
                          {tag}
                          <button
                            type="button"
                            onClick={() => removeNewTag(tag)}
                            className="ml-1"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </Badge>
                      );
                    })}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button size="icon" variant="ghost" className="h-6 w-6 p-0">
                          <Tag className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="z-[900000]">
                        {availableTags.map((tag) => (
                          <DropdownMenuItem
                            key={tag.label}
                            onSelect={() => addTagToNew(tag.label)}
                            className="flex items-center gap-2"
                          >
                            <span
                              className="flex-1 px-2 py-0.5 rounded"
                              style={{ backgroundColor: tag.color, color: tag.textColor }}
                            >
                              {tag.label}
                            </span>
                            <input
                              type="color"
                              className="h-6 w-6 p-0 border rounded-sm cursor-pointer"
                              value={tag.color}
                              onClick={(e) => e.stopPropagation()}
                              onChange={(e) => updateTagColor(tag.label, e.target.value)}
                            />
                            <input
                              type="color"
                              className="h-6 w-6 p-0 border rounded-sm cursor-pointer"
                              value={tag.textColor}
                              onClick={(e) => e.stopPropagation()}
                              onChange={(e) => updateTagTextColor(tag.label, e.target.value)}
                            />
                            <button
                              type="button"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                deleteAvailableTag(tag.label);
                              }}
                            >
                              <Trash2 className="h-4 w-4 text-destructive" />
                            </button>
                          </DropdownMenuItem>
                        ))}
                        <div className="p-2 flex items-center gap-2">
                          <Input
                            placeholder="Add tag name"
                            value={tagInput}
                            onChange={(e) => setTagInput(e.target.value)}
                            onKeyDown={(e) => e.stopPropagation()}
                            className="h-8 flex-1"
                          />
                          <input
                            type="color"
                            className="h-8 w-8 p-0 border rounded-sm"
                            value={tagColor}
                            onChange={(e) => setTagColor(e.target.value)}
                          />
                          <input
                            type="color"
                            className="h-8 w-8 p-0 border rounded-sm"
                            value={tagTextColor}
                            onChange={(e) => setTagTextColor(e.target.value)}
                          />
                          <Button size="sm" onClick={() => createTag(addTagToNew)}>
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
                <div className="flex flex-col items-start">
                  <Button
                    size="icon"
                    variant="ghost"
                    className="h-6 w-6 p-0 mb-2"
                    onClick={() => {
                      setNewText('');
                      setNewRef('');
                      setNewSource('');
                      setNewImage(null);
                      setImageCaption('');
                      setNewTags([]);
                    }}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button size="icon" variant="ghost" className="h-6 w-6 p-0 mb-1" onClick={captureScreenshot}>
                          <Camera className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Take screenshot</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          size="icon"
                          variant="ghost"
                          className="h-6 w-6 p-0 mb-2"
                          onClick={() => fileInputRef.current?.click()}
                        >
                    <Upload className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Upload image</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <input
                type="file"
                accept="image/*"
                ref={fileInputRef}
                onChange={handleFileChange}
                className="hidden"
              />
                  <Button
                    size="icon"
                    variant="ghost"
                    className="h-6 w-6 p-0 mt-auto"
                    onClick={() => {
                      if (newSource && newSource.trim()) {
                        try {
                          new URL(newSource);
                        } catch {
                          toast({ title: 'Invalid URL for Source' });
                          return;
                        }
                      }
                      if (newText.trim() || newImage) {
                        addNote(
                          newText,
                          newSource || undefined,
                          undefined,
                          newRef,
                          undefined,
                          newImage || undefined,
                          imageCaption || undefined,
                          newTags,
                        );
                        setNewText('');
                        setNewRef('');
                        setNewSource('');
                        setNewImage(null);
                        setImageCaption('');
                        setNewTags([]);
                        setSavedId('new');
                        setTimeout(() => setSavedId(null), 2000);
                      }
                    }}
                  >
                    {savedId === 'new' ? <Check className="h-4 w-4" /> : <Save className="h-4 w-4" />}
                  </Button>
                </div>
              </div>
              {newImage && (
                <div className="space-y-1 relative">
                  <img src={newImage} alt="preview" className="max-w-full" />
                  <Button
                    size="icon"
                    variant="ghost"
                    className="h-6 w-6 p-0 absolute top-1 right-1"
                    onClick={() => setNewImage(null)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                  <Input
                    value={imageCaption}
                    onChange={(e) => setImageCaption(e.target.value)}
                    placeholder="Caption"
                    className="w-full"
                  />
                </div>
              )}
            </div>
            <Separator className="my-2 bg-gray-300 dark:bg-gray-600" />
            <div className="flex gap-1 items-center flex-wrap">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    size="icon"
                    variant={filterTags.length ? "default" : "ghost"}
                    className="h-6 w-6 p-0"
                  >
                    <Tag className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="z-[900000]">
                  {availableTags.map((tag) => (
                    <DropdownMenuItem
                      key={tag.label}
                      onSelect={(e) => {
                        e.preventDefault();
                        toggleFilterTag(tag.label);
                      }}
                      className="flex items-center gap-2"
                    >
                      <span
                        className="flex-1 px-2 py-0.5 rounded"
                        style={{ backgroundColor: tag.color, color: tag.textColor }}
                      >
                        {tag.label}
                      </span>
                      {filterTags.includes(tag.label) && <Check className="h-4 w-4" />}
                    </DropdownMenuItem>
                  ))}
                  {filterTags.length > 0 && (
                    <DropdownMenuItem
                      onSelect={(e) => {
                        e.preventDefault();
                        setFilterTags([]);
                      }}
                      className="text-destructive"
                    >
                      Clear Filters
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
              {filterTags.map((tag) => {
                const info = getTagInfo(tag);
                return (
                  <Badge
                    key={tag}
                    variant="secondary"
                    className="gap-1"
                    style={{ backgroundColor: info?.color, color: info?.textColor }}
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => toggleFilterTag(tag)}
                      className="ml-1"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                );
              })}
              <Input
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    setSearch(searchInput);
                  }
                }}
                placeholder="Search notes"
                className="h-9 flex-1"
              />
              <Button
                size="icon"
                variant="ghost"
                className="h-6 w-6 p-0"
                onClick={() => setSearch(searchInput)}
              >
                <Search className="h-4 w-4" />
              </Button>
              <Button
                size="icon"
                variant="ghost"
                className="h-6 w-6 p-0"
                onClick={() => setShowTimestamps((s) => !s)}
              >
                <Clock className="h-4 w-4" />
              </Button>
              <Button
                size="icon"
                variant="ghost"
                className="h-6 w-6 p-0"
                onClick={() => {
                  selectedNotes.forEach((n) => safeRemoveNote(n.id));
                  setSelected({});
                }}
                disabled={selectedNotes.length < 2}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
            <Separator className="my-2 bg-gray-300 dark:bg-gray-600" />
            {filteredGroups.map((group, gi) => {
              const baseId = group[0].editedFrom || group[0].id;
              const currentIndex = currentVariantIndexes[baseId] || 0;
              const currentNote = group[currentIndex];
              
              return (
                <div key={gi} className="space-y-1" id={`note-${currentNote.id}`}> 
                  <div className="flex items-start">
                    <div className="flex flex-col items-center">
                      <input
                        type="checkbox"
                        className="mt-1 accent-[#A0522D]"
                        checked={!!selected[currentNote.id]}
                        onChange={(e) =>
                          setSelected((s) => ({ ...s, [currentNote.id]: e.target.checked }))
                        }
                      />
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button size="icon" variant="ghost" className="h-5 w-5 p-0 mt-1">
                            <Tag className="h-3 w-3" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent className="z-[900000]">
                          {availableTags.map((tag) => (
                            <DropdownMenuItem
                              key={tag.label}
                              onSelect={() => toggleTag(currentNote.id, tag.label)}
                              className="flex items-center gap-2"
                            >
                              <span
                                className="flex-1 px-2 py-0.5 rounded"
                                style={{ backgroundColor: tag.color, color: tag.textColor }}
                              >
                                {tag.label}
                              </span>
                              <input
                                type="color"
                                className="h-6 w-6 p-0 border rounded-sm cursor-pointer"
                                value={tag.color}
                                onClick={(e) => e.stopPropagation()}
                                onChange={(e) => updateTagColor(tag.label, e.target.value)}
                              />
                              <input
                                type="color"
                                className="h-6 w-6 p-0 border rounded-sm cursor-pointer"
                                value={tag.textColor}
                                onClick={(e) => e.stopPropagation()}
                                onChange={(e) => updateTagTextColor(tag.label, e.target.value)}
                              />
                          <button
                                type="button"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  deleteAvailableTag(tag.label);
                                }}
                              >
                                <Trash2 className="h-4 w-4 text-destructive" />
                              </button>
                            </DropdownMenuItem>
                          ))}
                          <div className="p-2 flex items-center gap-2">
                          <Input
                            placeholder="Add tag name"
                            value={tagInput}
                            onChange={(e) => setTagInput(e.target.value)}
                            onKeyDown={(e) => e.stopPropagation()}
                            className="h-8 flex-1"
                          />
                          <input
                            type="color"
                            className="h-8 w-8 p-0 border rounded-sm"
                            value={tagColor}
                            onChange={(e) => setTagColor(e.target.value)}
                          />
                          <input
                            type="color"
                            className="h-8 w-8 p-0 border rounded-sm"
                            value={tagTextColor}
                            onChange={(e) => setTagTextColor(e.target.value)}
                          />
                          <Button size="sm" onClick={() => createTag((t) => toggleTag(currentNote.id, t))}>
                              <Plus className="h-4 w-4" />
                            </Button>
                          </div>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    <div
                      className={cn(
                        "ml-2 flex-1 border p-2 rounded space-y-1",
                        selected[currentNote.id] && "border-[#A0522D] border-[0.5px]"
                      )}
                    >
                      <div className="flex items-start gap-2 justify-between">
                        <div className="text-sm flex-1">
                          <div className="flex flex-wrap gap-1 mb-1">
                            {currentNote.tags?.map((tag) => {
                              const info = getTagInfo(tag);
                              return (
                                <Badge
                                  key={tag}
                                  variant="secondary"
                                  className="gap-1"
                                  style={{ backgroundColor: info?.color, color: info?.textColor }}
                                >
                                  {tag}
                                  <button
                                    type="button"
                                    onClick={() => toggleTag(currentNote.id, tag)}
                                    className="ml-1"
                                  >
                                    <X className="h-3 w-3" />
                                  </button>
                                </Badge>
                              );
                            })}
                          </div>
                          {editTarget === currentNote.id ? (
                            <div className="space-y-1">
                              <Textarea
                                value={editText}
                                onChange={(e) => setEditText(e.target.value)}
                                className="min-h-[60px] w-full text-sm"
                              />
                              <div className="flex gap-1 items-start">
                                <Input
                                  value={editRef}
                                  onChange={(e) => setEditRef(e.target.value)}
                                  placeholder="Reference"
                                  className="flex-1"
                                />
                              </div>
                            </div>
                          ) : (
                            <div className="whitespace-pre-wrap break-all word-break-break-all overflow-wrap-anywhere">
                              <span
                                style={{ backgroundColor: currentNote.color || 'transparent' }}
                                className={currentNote.color ? 'px-0.5' : undefined}
                              >
                                {(expandedNotes[currentNote.id] || currentNote.text.split(/\r?\n/).length <= 3)
                                  ? currentNote.text
                                  : currentNote.text.split(/\r?\n/).slice(0, 3).join('\n')}
                              </span>
                              {currentNote.text.split(/\r?\n/).length > 3 && (
                                <button
                                  type="button"
                                  onClick={() =>
                                    setExpandedNotes((e) => ({ ...e, [currentNote.id]: !e[currentNote.id] }))
                                  }
                                  className="ml-1 text-xs underline"
                                >
                                  {expandedNotes[currentNote.id] ? "Show less" : "Show more"}
                                </button>
                              )}
                              {currentNote.image && (
                                <>
                                  <img src={currentNote.image} alt="note" className="mt-1 max-w-full" />
                                  {currentNote.caption && (
                                    <p className="text-xs mt-1 italic">{currentNote.caption}</p>
                                  )}
                                </>
                              )}
                            </div>
                          )}
                        </div>
                        <div className="flex flex-col items-center gap-1">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  size="icon"
                                  variant="ghost"
                                  className="h-6 w-6 p-0"
                                  onClick={() => {
                                    if (editTarget === currentNote.id) {
                                      setEditTarget(null)
                                      setEditText('')
                                      setEditRef('')
                                    } else {
                                      setDeleteTarget(currentNote.id)
                                    }
                                  }}
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{editTarget === currentNote.id ? 'Cancel edit' : 'Remove note'}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          {group.length > 1 && (
                            <span className="text-xs mt-2">v{currentIndex + 1}</span>
                          )}
                          {editTarget === currentNote.id && (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    size="icon"
                                    variant="ghost"
                                    className="h-6 w-6 p-0"
                                    onClick={() => {
                                      editNote(currentNote.id, editText, editRef, undefined, currentNote.tags)
                                      setEditTarget(null)
                                      setEditText('')
                                      setEditRef('')
                                      setSavedId(currentNote.id)
                                      setTimeout(() => setSavedId(null), 2000)
                                    }}
                                  >
                                    {savedId === currentNote.id ? <Check className="h-4 w-4" /> : <Save className="h-4 w-4" />}
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Save</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}
                        </div>
                    </div>
                    {/* Reference with ellipsis and tooltip */}
                    {currentNote.reference && (
                      <div className="mt-1">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <p className="text-xs text-muted-foreground">
                                Ref: {currentNote.reference}
                              </p>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="max-w-xs break-all">{currentNote.reference}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    )}

                    {currentNote.source && (
                      <div className="mt-1">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <p className="text-xs text-muted-foreground">
                                <a
                                  href={currentNote.source}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="underline"
                                  onClick={(e) => {
                                    if (currentNote.source?.endsWith('.pdf')) {
                                      e.preventDefault()
                                      setPdfSrc(currentNote.source)
                                      setPdfTitle(currentNote.reference || null)
                                    }
                                  }}
                                >
                                  Source: {currentNote.source}
                                </a>
                              </p>
                            </TooltipTrigger>
                            <TooltipContent style={{ maxWidth: '300px' }}>
                              <p className="break-all">
                                {currentNote.source}
                                {currentNote.reference && ` (${currentNote.reference})`}
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    )}

                    {/* Action icons with tooltips - add highlighter between pencil and copy */}
                    <div className="flex items-center justify-between mt-2">
                      <div className="flex items-center gap-1">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                size="icon"
                                variant="ghost"
                                onClick={() => {
                                  // Close variant form if open
                                  if (addingVariant === currentNote.id) {
                                    setAddingVariant(null);
                                    setVariantText('');
                                  }
                                  setEditTarget(currentNote.id);
                                  setEditText(currentNote.text);
                                  setEditRef(currentNote.reference || '');
                                }}
                                className="h-6 w-6 p-0"
                              >
                                <Pencil className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Edit note</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                        
                        <DropdownMenu
                          open={highlightMenu === currentNote.id}
                          onOpenChange={(o) =>
                            setHighlightMenu(o ? currentNote.id : null)
                          }
                        >
                          <TooltipProvider>
                            <Tooltip>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  size="icon"
                                  variant="ghost"
                                  className="h-6 w-6 p-0"
                                >
                                  <Highlighter className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <TooltipContent>
                                <p>Highlight text</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          <DropdownMenuContent side="bottom" align="start" className="z-[100002] flex items-center gap-1 w-auto p-2">
                            {colorOptions.map((c) => (
                              <button
                                key={c}
                                type="button"
                                className="w-4 h-4 rounded-full border"
                                style={{ backgroundColor: c }}
                                onClick={() => {
                                  setColor(currentNote.id, c)
                                  setHighlightMenu(null)
                                }}
                              />
                            ))}
                            <button
                              type="button"
                              className="ml-2 text-xs underline"
                              onClick={() => {
                                setColor(currentNote.id, '')
                                setHighlightMenu(null)
                              }}
                            >
                              Clear
                            </button>
                          </DropdownMenuContent>
                        </DropdownMenu>
                        
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                size="icon"
                                variant="ghost"
                                onClick={() => copyNoteText(currentNote.text, currentNote.id)}
                                className="h-6 w-6 p-0"
                              >
                                {copiedId === currentNote.id ? (
                                  <span className="text-xs text-green-600">✓</span>
                                ) : (
                                  <Copy className="h-4 w-4" />
                                )}
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Copy to clipboard</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>

                        {currentNote.image && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <a href={currentNote.image} download>
                                  <Button size="icon" variant="ghost" className="h-6 w-6 p-0">
                                    <Download className="h-4 w-4" />
                                  </Button>
                                </a>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Download image</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                        
                        {currentIndex === 0 && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  size="icon"
                                  variant="ghost"
                                  onClick={() => {
                                    // Close edit form if open
                                    if (editTarget === currentNote.id) {
                                      setEditTarget(null);
                                      setEditText('');
                                      setEditRef('');
                                    }
                                    setAddingVariant(currentNote.id);
                                    setVariantText('');
                                  }}
                                  className="h-6 w-6 p-0"
                                >
                                  <Plus className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Add variant</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </div>
                      
                      <div className="flex items-center gap-1 ml-auto">
                        {group.length > 1 && (
                          <div className="flex items-center gap-1">
                            <Button
                              size="icon"
                              variant="ghost"
                              disabled={currentIndex === 0}
                              onClick={() => setCurrentVariantIndexes(prev => ({
                                ...prev,
                                [baseId]: Math.max(0, (prev[baseId] || 0) - 1)
                              }))}
                              className="h-6 w-6 p-0"
                            >
                              <ChevronLeft className="h-4 w-4" />
                            </Button>
                            <span className="text-xs">
                              {currentIndex + 1}/{group.length}
                            </span>
                            <Button
                              size="icon"
                              variant="ghost"
                              disabled={currentIndex === group.length - 1}
                              onClick={() => setCurrentVariantIndexes(prev => ({
                                ...prev,
                                [baseId]: Math.min(group.length - 1, (prev[baseId] || 0) + 1)
                              }))}
                              className="h-6 w-6 p-0"
                            >
                              <ChevronRight className="h-4 w-4" />
                            </Button>
                          </div>
                        )}
                        {showTimestamps && (
                          <span className="text-[10px] text-muted-foreground ml-2 whitespace-nowrap">
                            {new Date(currentNote.createdAt).toLocaleString()}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                  
                  
                  {/* Add variant form with improved styling */}
                  {currentIndex === 0 && (
                    <div className="mt-1">
                      {addingVariant === currentNote.id ? (
                        <div className="relative border-[0px] border-[#6366F1] dark:border-[#6366F1] rounded-xl p-2 pr-12">
                          {/* Visual connector to the note above */}
                          {/* <div
                            className="absolute -top-3 left-10 w-2 h-3 bg-[#6366F1] dark:bg-[#6366F1]"
                            style={{ clipPath: 'polygon(0 100%, 100% 100%, 50% 0)' }}
                          /> */}
                          
                          <Button
                            size="icon"
                            variant="ghost"
                            onClick={() => setAddingVariant(null)}
                            className="h-6 w-6 p-0 absolute top-1 right-1"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                          <Button
                            size="icon"
                            variant="ghost"
                            onClick={() => {
                              if (variantText.trim()) {
                                addNote(
                                  variantText,
                                  currentNote.source,
                                  currentNote.id,
                                  currentNote.reference || '',
                                  currentNote.color,
                                  currentNote.image
                                );
                                setAddingVariant(null);
                                setVariantText('');
                                setSavedId(currentNote.id);
                                setTimeout(() => setSavedId(null), 2000);
                              }
                            }}
                            className="h-6 w-6 p-0 absolute top-8 right-1"
                          >
                            {savedId === currentNote.id ? <Check className="h-4 w-4" /> : <Save className="h-4 w-4" />}
                          </Button>
                          
                          <Textarea
                            value={variantText}
                            onChange={(e) => setVariantText(e.target.value)}
                            className="min-h-[60px] w-full"
                            placeholder="Enter variant text"
                          />
                        </div>
                      ) : null}
                    </div>
                  )}
                </div>
              );
              })}
            </>
            ) : page === 'context' ? (
            <>
              {contextItems.length > 0 && (
                <h4 id="context-heading" className={`${headingClass} mt-0 -mb-2`}>Context</h4>
              )}
              <div id="context-section" className="space-y-2 mt-1 border p-2 rounded">
                {contextItems.length > 0 ? null : (
                  <p className="text-sm text-muted-foreground">Context</p>
                )}
                {contextItems.map((item, idx) => (
                    <React.Fragment key={item.id}>
                      <div className="relative group">
                        {item.type === 'note' && (
                          <Button
                            size="icon"
                            variant="ghost"
                            className="h-4 w-4 p-0 absolute left-1/2 -translate-x-1/2 -top-2 opacity-0 group-hover:opacity-100"
                            onClick={() => insertContextAt(idx)}
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                        )}
                        <div
                          draggable
                          onDragStart={() => setDraggingId(item.id)}
                          onDragOver={(e) => e.preventDefault()}
                          onDrop={() => {
                            if (draggingId && draggingId !== item.id) {
                              setContextItems((arr) => {
                                const a = [...arr];
                                const from = a.findIndex((i) => i.id === draggingId);
                                const to = a.findIndex((i) => i.id === item.id);
                                if (from !== -1 && to !== -1) {
                                  const [m] = a.splice(from, 1);
                                  a.splice(to, 0, m);
                                }
                                return a;
                              });
                            }
                            setDraggingId(null);
                          }}
                          style={{ borderColor: item.type === 'note' ? groupColors[item.id] : undefined }}
                          className={`space-y-1 border rounded p-2 relative ${item.type === 'context' ? 'bg-white' : ''}`}
                        >
                          <div className="absolute right-2 top-1/2 -translate-y-1/2 flex flex-col gap-0">
                          <Button
                            size="icon"
                            variant="ghost"
                            className="h-4 w-4 p-0"
                            onClick={() => {
                              if (item.type === 'note') {
                                setSelected((s) => ({ ...s, [item.id]: false }));
                                safeRemoveNote(item.id);
                              } else {
                                setContextItems((it) => it.filter((i) => i.id !== item.id));
                              }
                            }}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                          <Button
                            size="icon"
                            variant="ghost"
                            className="h-4 w-4 p-0"
                            disabled={idx === 0}
                            onClick={() => moveItemUp(item.id)}
                          >
                            <ArrowUp className="h-3 w-3" />
                          </Button>
                          <Button
                            size="icon"
                            variant="ghost"
                            className="h-4 w-4 p-0"
                            disabled={idx === contextItems.length - 1}
                            onClick={() => moveItemDown(item.id)}
                          >
                            <ArrowDown className="h-3 w-3" />
                          </Button>
                        </div>
                        {item.type === 'note' ? (
                          <div className="whitespace-pre-wrap text-sm py-0 pr-6">
                            {notes.find((n) => n.id === item.id)?.text}
                            <a
                              href={`#note-${item.id}`}
                              onClick={() => setPage('notes')}
                              className="ml-2 text-xs underline"
                            >
                              edit
                            </a>
                          </div>
                        ) : (
                          <div className="py-1 pr-6">
                            <Textarea
                              value={item.text}
                              onChange={(e) =>
                                setContextItems((its) =>
                                  its.map((it) => (it.id === item.id ? { ...it, text: e.target.value } : it))
                                )
                              }
                              placeholder="Add context..."
                              rows={1}
                              style={{ minHeight: '1.5rem', height: '1.5rem', overflowY: 'auto', lineHeight: '1.5rem', resize: 'none' }}
                              className="text-xs flex-1 p-0 border-none focus-visible:ring-0 resize-none"
                            />
                          </div>
                        )}
                      </div>
                      {item.type === 'note' && (
                        <Button
                          size="icon"
                          variant="ghost"
                          className="h-4 w-4 p-0 absolute left-1/2 -translate-x-1/2 -bottom-2 opacity-0 group-hover:opacity-100"
                          onClick={() => insertContextAt(idx + 1)}
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      )}
                      </div>
                    </React.Fragment>
                  ))}
                <div className="relative group h-0">
                  <Button
                    size="icon"
                    variant="ghost"
                    className="h-4 w-4 p-0 absolute left-1/2 -translate-x-1/2 -top-2 opacity-0 group-hover:opacity-100"
                    onClick={() => insertContextAt(contextItems.length)}
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                </div>
              </div>
              {prompt.trim() && (
                <h4 id="prompt-heading" className={`${headingClass} mt-2`}>Prompt</h4>
              )}
              <div id="prompt-section" className="mt-1">
                <Textarea
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  placeholder="Prompt"
                  className="min-h-[80px] -mt-1.5"
                />
                <div className="flex items-center justify-between mt-1 mb-2">
                  <div className="flex items-center gap-1 text-xs">
                    <span>LLM:</span>
                    <select
                      value={model}
                      onChange={(e) => setModel(e.target.value)}
                      className="border rounded p-0.5 text-xs bg-background"
                    >
                      <option value="gpt-4.1-mini">gpt-4.1-mini</option>
                      <option value="gpt-4.0-mini">gpt-4.0-mini</option>
                    </select>
                  </div>
                  <p className="text-xs">Approx. tokens: {totalTokens}</p>
                </div>
                <Button size="sm" className="mt-1 -mb-1 bg-amber-400 text-black hover:bg-amber-500" onClick={runPrompt} disabled={loading}>
                  {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : "Submit"}
                </Button>
              </div>
              <Separator className="my-2" />
              <div className="-mt-1">
                {outputs.length > 0 && (
                  <h4 className={`${headingClass} -mt-1 mb-3`}>AI output</h4>
                )}
                {outputs.length === 0 ? (
                  <div className="border p-2 mt-1 rounded whitespace-pre-wrap text-sm text-muted-foreground">
                    AI response
                  </div>
                ) : (
                  outputs.map((o, idx) => (
                    <div key={idx} className="border p-2 mt-1 rounded whitespace-pre-wrap text-sm">
                      {o}
                    </div>
                  ))
                )}
              </div>
              {outputs.length > 0 && (
                <Dialog open={saveDialogOpen} onOpenChange={setSaveDialogOpen}>
                  <DialogTrigger asChild>
                    <Button size="sm" className="mt-2 bg-amber-400 text-black hover:bg-amber-500">
                      Save
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Save Session</DialogTitle>
                      <DialogDescription>Enter a description for this session.</DialogDescription>
                    </DialogHeader>
                    <Input
                      value={sessionTitleInput}
                      onChange={(e) => setSessionTitleInput(e.target.value)}
                      placeholder="Description"
                    />
                    <DialogFooter>
                      <DialogClose asChild>
                        <Button variant="outline">Cancel</Button>
                      </DialogClose>
                      <Button
                        onClick={() => {
                          confirmSaveSession();
                          setSaveDialogOpen(false);
                        }}
                      >
                        Save
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              )}
              {diffOutputs()}
            </>
            ) : page === 'records' ? (
            <div className="space-y-2">
              <h4 className={`${headingClass} mb-1`}>Saved</h4>
              {sessions.length === 0 ? (
                <p className="text-sm">No sessions saved.</p>
              ) : (
                <ul className="space-y-1">
                  {sessions.map((s) => (
                    <li key={s.id} className="border rounded text-[13px] pl-2 pr-1 py-0.5 flex justify-between items-center">
                      <button
                        type="button"
                        className="underline"
                        onClick={() => {
                          setPrompt(s.prompt);
                          setOutputs(s.outputs);
                          setContextItems([
                            {
                              id: `ctx-${Date.now()}`,
                              type: 'context',
                              text: s.context || '',
                            },
                          ]);
                          setPage('context');
                        }}
                      >
                        {s.title ? `${s.title} - ${new Date(s.time).toLocaleString()}` : new Date(s.time).toLocaleString()}
                      </button>
                      <div className="flex gap-1">
                        <Button
                          size="sm"
                          variant={compareA?.id === s.id ? 'default' : 'ghost'}
                          className={compareA?.id === s.id ? 'bg-amber-400 text-black' : ''}
                          onClick={() => setCompareA(s)}
                        >
                          A
                        </Button>
                        <Button
                          size="sm"
                          variant={compareB?.id === s.id ? 'default' : 'ghost'}
                          className={compareB?.id === s.id ? 'bg-amber-400 text-black' : ''}
                          onClick={() => setCompareB(s)}
                        >
                          B
                        </Button>
                        <Button size="sm" variant="ghost" onClick={() => setDeleteSessionId(s.id)}>
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </li>
                  ))}
                </ul>
              )}
              {compareA && compareB && (
                <div className="mt-4 space-y-2.5">
                  <Separator className="my-3" />
                  <h4 className={headingClass}>Compare</h4>
                  <div className="flex gap-2 text-xs">
                    <label className="flex items-center gap-1">
                      <input
                        type="checkbox"
                        checked={compareOptions.context}
                        onChange={(e) =>
                          setCompareOptions((o) => ({ ...o, context: e.target.checked }))
                        }
                      />
                      Context
                    </label>
                    <label className="flex items-center gap-1">
                      <input
                        type="checkbox"
                        checked={compareOptions.prompt}
                        onChange={(e) =>
                          setCompareOptions((o) => ({ ...o, prompt: e.target.checked }))
                        }
                      />
                      Prompt
                    </label>
                    <label className="flex items-center gap-1">
                      <input
                        type="checkbox"
                        checked={compareOptions.outputs}
                        onChange={(e) =>
                          setCompareOptions((o) => ({ ...o, outputs: e.target.checked }))
                        }
                      />
                      Outputs
                    </label>
                  </div>
                  {compareOptions.context && (
                    <>
                      <h4 className="text-sm font-medium mb-1">Context Comparison</h4>
                      <div className="text-xs">
                        <ReactDiffViewer
                          oldValue={compareA.context || ''}
                          newValue={compareB.context || ''}
                          splitView={true}
                          leftTitle="Context A"
                          rightTitle="Context B"
                        />
                      </div>
                    </>
                  )}
                  {compareOptions.prompt && (
                    <>
                      <h4 className="text-sm font-medium mt-4 mb-1">Prompt Comparison</h4>
                      <div className="text-xs">
                        <ReactDiffViewer
                          oldValue={compareA.prompt}
                          newValue={compareB.prompt}
                          splitView={true}
                          leftTitle="Prompt A"
                          rightTitle="Prompt B"
                        />
                      </div>
                    </>
                  )}
                  {compareOptions.outputs && (
                    <>
                      <h4 className="text-sm font-medium mt-4 mb-1">Output Comparison</h4>
                      <div className="text-xs">
                        <ReactDiffViewer
                          oldValue={(compareA.outputs || []).join('\n\n')}
                          newValue={(compareB.outputs || []).join('\n\n')}
                          splitView={true}
                          leftTitle="Output A"
                          rightTitle="Output B"
                        />
                      </div>
                    </>
                  )}
                </div>
              )}
              
              {/* Create Section */}
              <Separator className="my-4" />
              <h4 className={`${headingClass} mb-2`}>Create</h4>
              <div className="space-y-2">
                <Input
                  value={newPromptTitle}
                  onChange={(e) => setNewPromptTitle(e.target.value)}
                  placeholder="Prompt title"
                  className="text-sm"
                />
                <Textarea
                  value={newPromptText}
                  onChange={(e) => setNewPromptText(e.target.value)}
                  placeholder="Enter your prompt here..."
                  className="min-h-[80px] text-sm"
                />
                <Button
                  size="sm"
                  onClick={savePrompt}
                  disabled={!newPromptTitle.trim() || !newPromptText.trim()}
                  className="bg-amber-400 text-black hover:bg-amber-500"
                >
                  Save Prompt
                </Button>
              </div>
              
              {/* Saved Prompts */}
              {savedPrompts.length > 0 && (
                <>
                  <Separator className="my-4" />
                  <h4 className={`${headingClass} mb-2`}>Prompts</h4>
                  <div className="space-y-1">
                    {savedPrompts.map((prompt) => (
                      <div key={prompt.id} className="border rounded text-[13px] pl-2 pr-1 py-0.5 flex justify-between items-center">
                        <button
                          type="button"
                          className="underline flex-1 text-left"
                          onClick={() => usePrompt(prompt.prompt)}
                        >
                          {prompt.title}
                        </button>
                        <div className="flex gap-1">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => copyNoteText(prompt.prompt, prompt.id)}
                            className="h-6 w-6 p-0"
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => deletePrompt(prompt.id)}
                            className="h-6 w-6 p-0"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </>
              )}
            </div>
            ) : null}
          </div>
          {/* resize handles */}
          {['top-left','top','top-right','right','bottom-right','bottom','bottom-left','left'].map((h) => {
            const isCorner = ['top-left','top-right','bottom-left','bottom-right'].includes(h);
            const sizeClass = h === 'top-left'
              ? 'w-4 h-4'
              : isCorner
              ? 'w-2 h-2'
              : h.includes('left') || h.includes('right')
              ? 'w-1'
              : 'h-1';
            return (
              <div
                key={h}
                onMouseDown={(e) => startResize(h, e)}
                className={`absolute ${
                  h.includes('top') ? 'top-0' : h.includes('bottom') ? 'bottom-0' : 'inset-y-0'
                } ${
                  h.includes('left') ? 'left-0' : h.includes('right') ? 'right-0' : 'inset-x-0'
                } ${
                  sizeClass
                } ${h.includes('left') || h.includes('right') ? 'cursor-ew-resize' : ''} ${
                  h.includes('top') || h.includes('bottom') ? 'cursor-ns-resize' : ''
                } ${h.includes('left') && h.includes('top') ? 'cursor-nwse-resize' : ''} ${
                  h.includes('right') && h.includes('bottom') ? 'cursor-nwse-resize' : ''
                } ${h.includes('left') && h.includes('bottom') ? 'cursor-nesw-resize' : ''} ${
                  h.includes('right') && h.includes('top') ? 'cursor-nesw-resize' : ''
                }`}
                style={{ zIndex: 50 }}
              />
            );
          })}
        </div>
      )}
      {pdfSrc && (
        <div className="fixed inset-0 bg-black/50 flex flex-col" style={{ zIndex: zIndex + 1 }}>
          <div className="flex justify-end p-2">
            <Button
              variant="ghost"
              onClick={() => {
                setPdfSrc(null)
                setPdfTitle(null)
              }}
              className="h-6 w-6 p-0 bg-white rounded"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <iframe
            src={`/pdf-frame?url=${encodeURIComponent(pdfSrc)}${pdfTitle ? `&title=${encodeURIComponent(pdfTitle)}` : ''}`}
            className="flex-1"
          />
        </div>
      )}

      <AlertDialog open={!!deleteTarget} onOpenChange={(o) => !o && setDeleteTarget(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete note?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (deleteTarget) safeRemoveNote(deleteTarget)
                setDeleteTarget(null)
              }}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <AlertDialog open={!!deleteSessionId} onOpenChange={(o) => !o && setDeleteSessionId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete session?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (deleteSessionId) setSessions(sessions.filter(s => s.id !== deleteSessionId));
                setDeleteSessionId(null);
              }}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
