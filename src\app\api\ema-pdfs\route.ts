import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const url = request.nextUrl.searchParams.get('url');
  if (!url) {
    return NextResponse.json({ error: 'Missing url parameter' }, { status: 400 });
  }

  try {
    const res = await fetch(url);
    if (!res.ok) {
      return NextResponse.json({ error: 'Failed to fetch page' }, { status: res.status });
    }
    const html = await res.text();
    const regex = /href="([^"]+\.pdf)"/gi;
    const links: string[] = [];
    let match;
    while ((match = regex.exec(html)) !== null) {
      try {
        const absolute = new URL(match[1], url).href;
        if (!links.includes(absolute)) links.push(absolute);
      } catch {}
    }
    return NextResponse.json({ pdfs: links });
  } catch (err) {
    console.error('Error fetching pdfs:', err);
    return NextResponse.json({ error: 'Failed to retrieve pdf links' }, { status: 500 });
  }
}
