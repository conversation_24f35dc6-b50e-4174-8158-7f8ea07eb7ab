import { NextResponse } from 'next/server';
import { spawn } from 'child_process';

export const dynamic = 'force-dynamic';

export async function POST(request: Request) {
  const payload = await request.json();
  const encoder = new TextEncoder();
  const stream = new ReadableStream({
    start(controller) {
      const child = spawn('python', ['scripts/workflow_runner.py']);
      child.stdout.on('data', (d) => {
        controller.enqueue(encoder.encode(d));
      });
      child.stderr.on('data', (d) => {
        console.error(d.toString());
      });
      child.on('close', () => controller.close());
      child.stdin.write(JSON.stringify(payload));
      child.stdin.end();
    },
  });
  return new NextResponse(stream, {
    headers: { 'Content-Type': 'application/x-ndjson' },
  });
}
