import { NextRequest, NextResponse } from 'next/server';

/**
 * Proxy the FDA drugs API and optionally download remote documents.
 *
 * Query parameters:
 *   - query:      search term (required for FDA search)
 *   - field:      search field (brand, generic, application, manufacturer)
 *   - limit:      number of results (default 10)
 *   - extra:      comma separated list of additional openFDA fields to include
 *   - download:   if present along with `url`, the route will proxy the file
 *   - url:        remote document URL to download when `download` is provided
 */

export async function GET(request: NextRequest) {
  const params = request.nextUrl.searchParams;

  // Handle file download proxying
  const download = params.get('download');
  const urlParam = params.get('url');
  const inline = params.get('inline');
  if (download !== null && urlParam) {
    try {
      const res = await fetch(urlParam);
      if (!res.ok) {
        return NextResponse.json({ error: 'Failed to fetch file' }, { status: res.status });
      }
      const buffer = await res.arrayBuffer();
      const contentType = res.headers.get('content-type') || 'application/octet-stream';
      const filename = urlParam.split('/').pop() || 'download';
      return new NextResponse(buffer, {
        headers: {
          'Content-Type': contentType,
          'Content-Disposition': `${inline !== null ? 'inline' : 'attachment'}; filename="${filename}"`,
          'Access-Control-Allow-Origin': '*'
        }
      });
    } catch (err) {
      console.error('Download proxy error', err);
      return NextResponse.json({ error: 'Failed to download file' }, { status: 500 });
    }
  }

  const query = params.get('query');
  if (!query) {
    return NextResponse.json({ error: 'Query parameter is required' }, { status: 400 });
  }

  const field = params.get('field') || 'brand';

  const limit = parseInt(params.get('limit') || '10', 10);

  let searchField: string;
  switch (field) {
    case 'brand':
      searchField = `openfda.brand_name:"${query}"`;
      break;
    case 'generic':
      searchField = `openfda.generic_name:"${query}"`;
      break;
    case 'application':
      searchField = `openfda.application_number:"${query}"`;
      break;
    case 'manufacturer':
      searchField = `openfda.manufacturer_name:"${query}"`;
      break;
    default:
      // allow searching arbitrary openFDA fields or top level sponsor_name
      if (field === 'sponsor_name') {
        searchField = `sponsor_name:"${query}"`;
      } else {
        searchField = `openfda.${field}:"${query}"`;
      }
      break;
  }

  const search = encodeURIComponent(searchField);
  const url = `https://api.fda.gov/drug/drugsfda.json?search=${search}&limit=${limit}`;

  try {
    const res = await fetch(url);
    if (!res.ok) {
      throw new Error(`OpenFDA request failed: ${res.status}`);
    }
    const data = await res.json();
    const extra = params.get('extra');
    const extraFields = extra && extra !== 'all' ? extra.split(',').map(f => f.trim()).filter(Boolean) : [];

    const formatDate = (d: string) => {
      return /^\d{8}$/.test(d) ? `${d.slice(0,4)}-${d.slice(4,6)}-${d.slice(6)}` : d;
    };

    const allowedBaseFields = [
      'application_number',
      'manufacturer_name',
      'route',
      'brand_name',
      'generic_name',
      'pharm_class_moa'
    ];
    const allowedFields = Array.from(new Set([...allowedBaseFields, ...extraFields]));

    const results = (data.results || []).map((r: any) => {
      let openfda: Record<string, any> = {};
      if (extra === 'all') {
        openfda = r.openfda || {};
      } else {
        allowedFields.forEach((k) => {
          if (r.openfda && r.openfda[k]) {
            openfda[k] = r.openfda[k];
          }
        });
      }

      const submissions = (r.submissions || []).map((s: any) => ({
        submission_number: s.submission_number,
        submission_type: s.submission_type,
        application_docs: (s.application_docs || []).map((d: any) => ({
          id: d.id,
          date: d.date ? formatDate(d.date) : undefined,
          title: d.title,
          type: d.type,
          url: d.url,
        }))
      }));

      return {
        sponsor_name: r.sponsor_name,
        openfda,
        submissions,
      };
    });

    return NextResponse.json({ results });
  } catch (err) {
    console.error('OpenFDA error', err);
    return NextResponse.json({ error: 'Failed to fetch from OpenFDA' }, { status: 500 });
  }
}
