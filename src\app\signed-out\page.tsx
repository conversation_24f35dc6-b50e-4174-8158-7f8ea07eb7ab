"use client";

import Image from "next/image";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { useTheme } from "@/contexts/ThemeContext";
import EvicenterLogo from "@/icons/Evicenter.svg?url";
import EvicenterWhiteLogo from "@/icons/evicenter-white.svg?url";

export default function SignedOutPage() {
  const { theme } = useTheme();
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 space-y-4">
      <Image
        src={theme === "dark" ? EvicenterWhiteLogo : EvicenterLogo}
        alt="Evicenter Logo"
        width={200}
        height={80}
      />
      <h1 className="text-xl font-bold">You have been signed out</h1>
      <p className="text-sm text-center">Thank you for visiting Evicenter.</p>
      <Button asChild>
        <Link href="/login">Sign In Again</Link>
      </Button>
    </div>
  );
}
