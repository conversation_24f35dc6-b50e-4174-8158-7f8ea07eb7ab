import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const id = request.nextUrl.searchParams.get('id');
  if (!id) {
    return NextResponse.json({ error: 'Missing id parameter' }, { status: 400 });
  }

  try {
    console.log(`Proxying request to DailyMed for ID: ${id}`);
    const res = await fetch(
      `https://dailymed.nlm.nih.gov/dailymed/services/v2/spls/${id}.json`,
      {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      }
    );
    
    if (!res.ok) {
      console.error(`DailyMed API error: ${res.status} ${res.statusText}`);
      return NextResponse.json(
        { error: `Failed to fetch label data: ${res.status} ${res.statusText}` },
        { status: res.status }
      );
    }
    
    const data = await res.json();
    console.log(`Successfully fetched data for ID: ${id}`);
    return NextResponse.json(data);
  } catch (err) {
    console.error('Error fetching label', err);
    return NextResponse.json(
      { error: `Unable to fetch label data: ${err instanceof Error ? err.message : 'Unknown error'}` },
      { status: 500 }
    );
  }
}