"use client";

import React, { useState, useEffect, useRef } from "react";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  <PERSON><PERSON>hart,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  ResponsiveContainer,
} from "recharts";
import { SubpageHeader } from "@/components/SubpageHeader";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

const INCH_TO_PX = 96;

interface ChartPreviewProps {
  chart: any;
  left: number;
  top: number;
  width: number;
  height: number;
  titleLeft: number;
  titleTop: number;
  titleWidth: number;
  titleHeight: number;
  shapes: any[];
  background?: string | null;
  onShapesChange?: (s: any[]) => void;
}

function ChartPreview({
  chart,
  left,
  top,
  width,
  height,
  titleLeft,
  titleTop,
  titleWidth,
  titleHeight,
  shapes,
  background,
  onShapesChange,
}: ChartPreviewProps) {
  if (!chart) return null;
  const { type, args } = chart;
  const title = args?.title || "Preview";

  if (!args?.data) return null;

  const data = args.data as any[];

  const chartStyle = {
    position: "absolute" as const,
    left: left * INCH_TO_PX,
    top: top * INCH_TO_PX,
    width: width * INCH_TO_PX,
    height: height * INCH_TO_PX,
  };

  const renderChart = () => {
    switch (type) {
      case "plot_bar_chart":
        return (
          <ResponsiveContainer width="100%" height="100%" className="overflow-visible">
            <BarChart data={data} margin={{ left: 40, right: 20 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={args.x} />
              <YAxis />
              <Tooltip />
              <Bar dataKey={args.y}>
                {data.map((_: any, idx: number) => (
                  <Cell key={idx} fill={args.colors?.[idx] || args.color || "#f59e0b"} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        );
    case "plot_horizontal_bar_chart":
      return (
        <ResponsiveContainer width="100%" height="100%" className="overflow-visible">
          <BarChart data={data} layout="vertical" margin={{ left: 40, right: 20 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis type="number" />
            <YAxis type="category" dataKey={args.x} />
            <Tooltip />
            <Bar dataKey={args.y}>
              {data.map((_: any, idx: number) => (
                <Cell key={idx} fill={args.colors?.[idx] || args.color || "#f59e0b"} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      );
    case "plot_line_chart": {
      const group = args.group_by;
      let lineData: any[] = [];
      if (group) {
        const xVals = Array.from(new Set(data.map((d) => String(d[args.x]))));
        const groups = Array.from(new Set(data.map((d) => d[group])));
        lineData = xVals.map((xVal) => {
          const row: any = { [args.x]: xVal };
          groups.forEach((g: any) => {
            const found = data.find(
              (d) => String(d[args.x]) === xVal && d[group] === g,
            );
            row[g] = found ? found[args.y] : 0;
          });
          return row;
        });
      } else {
        lineData = data.map((d) => ({
          [args.x]: d[args.x],
          [args.y]: d[args.y],
        }));
      }
      return (
        <ResponsiveContainer width="100%" height="100%" className="overflow-visible">
          <LineChart data={lineData} margin={{ left: 40, right: 20 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey={args.x} />
            <YAxis />
            <Tooltip />
            {group ? (
              Array.from(new Set(data.map((d) => d[group]))).map((g, idx) => (
                <Line
                  key={g}
                  dataKey={g}
                  type="monotone"
                  stroke={args.colors?.[idx] || args.color || "#f59e0b"}
                />
              ))
            ) : (
              <Line
                type="monotone"
                dataKey={args.y}
                stroke={args.colors?.[0] || args.color || "#f59e0b"}
              />
            )}
          </LineChart>
        </ResponsiveContainer>
      );
    }
    case "plot_scatter_plot":
      return (
        <ResponsiveContainer width="100%" height="100%" className="overflow-visible">
          <ScatterChart margin={{ left: 40, right: 20 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey={args.x} name={args.x} />
            <YAxis dataKey={args.y} name={args.y} />
            <Tooltip cursor={{ strokeDasharray: "3 3" }} />
            <Scatter data={data}>
              {data.map((_: any, idx: number) => (
                <Cell key={idx} fill={args.colors?.[idx] || args.color || "#f59e0b"} />
              ))}
            </Scatter>
          </ScatterChart>
        </ResponsiveContainer>
      );
    case "plot_pie_chart":
      return (
        <ResponsiveContainer width="100%" height="100%" className="overflow-visible">
          <PieChart>
            <Tooltip />
            <Pie data={data} dataKey={args.value} nameKey={args.category} label>
              {data.map((_: any, idx: number) => (
                <Cell key={idx} fill={args.colors?.[idx] || args.color || "#f59e0b"} />
              ))}
            </Pie>
          </PieChart>
        </ResponsiveContainer>
      );
    default:
      return <p className="mb-6">Preview not available for this chart type.</p>;
  }
  };

  const slideStyle = {
    width: 960,
    height: 720,
    backgroundSize: "cover",
    backgroundImage: background
      ? `url(data:image/png;base64,${background})`
      : undefined,
  };

  const containerRef = useRef<HTMLDivElement>(null);
  const [dragIdx, setDragIdx] = useState<number | null>(null);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  const startDrag = (idx: number, e: React.MouseEvent<HTMLDivElement>) => {
    if (!onShapesChange) return;
    const rect = containerRef.current?.getBoundingClientRect();
    if (!rect) return;
    setDragIdx(idx);
    setDragOffset({
      x: e.clientX - (rect.left + shapes[idx].left * INCH_TO_PX),
      y: e.clientY - (rect.top + shapes[idx].top * INCH_TO_PX),
    });
  };

  const handleMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (dragIdx === null || !onShapesChange) return;
    const rect = containerRef.current?.getBoundingClientRect();
    if (!rect) return;
    const x = e.clientX - rect.left - dragOffset.x;
    const y = e.clientY - rect.top - dragOffset.y;
    const newShapes = [...shapes];
    newShapes[dragIdx] = {
      ...newShapes[dragIdx],
      left: x / INCH_TO_PX,
      top: y / INCH_TO_PX,
    };
    onShapesChange(newShapes);
  };

  const stopDrag = () => setDragIdx(null);

  const titleStyle = {
    position: "absolute" as const,
    left: titleLeft * INCH_TO_PX,
    top: titleTop * INCH_TO_PX,
    width: titleWidth * INCH_TO_PX,
    height: titleHeight * INCH_TO_PX,
    fontWeight: "bold",
    fontSize: "24px",
    color: "#003366",
    textAlign: "center" as const,
  };

  return (
    <div className="mb-6">
      <div
        ref={containerRef}
        className="relative border bg-white shadow"
        style={slideStyle}
        onMouseMove={handleMove}
        onMouseUp={stopDrag}
        onMouseLeave={stopDrag}
      >
        <div style={titleStyle}>{title}</div>
        <div style={chartStyle}>{renderChart()}</div>
        {shapes?.map((s, idx) => {
          const style = {
            position: "absolute" as const,
            left: s.left * INCH_TO_PX,
            top: s.top * INCH_TO_PX,
            width: s.width * INCH_TO_PX,
            height: s.height * INCH_TO_PX,
            backgroundColor:
              s.type === "text" ? "transparent" : s.color || "#eee",
            color: s.color || "#000",
            borderRadius: s.type === "ellipse" ? "50%" : undefined,
            border: `1px solid ${s.borderColor || "#999"}`,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            padding: "2px",
            boxSizing: "border-box" as const,
            fontSize: `${s.font_size || 14}px`,
            fontWeight: s.bold ? "bold" : undefined,
            textAlign: s.align || "left",
            cursor: onShapesChange ? "move" : "default",
            userSelect: "none" as const,
          };
          return (
            <div
              key={idx}
              style={style}
              onMouseDown={(e) => startDrag(idx, e)}
              onContextMenu={(e) => {
                if (s.type === "text") {
                  e.preventDefault();
                  const newText = prompt("Edit text", s.text || "");
                  if (newText !== null && onShapesChange) {
                    const newShapes = [...shapes];
                    newShapes[idx] = { ...newShapes[idx], text: newText };
                    onShapesChange(newShapes);
                  }
                }
              }}
            >
              {s.type === "text" ? (s.bullet ? "\u2022 " : "") + s.text : ""}
            </div>
          );
        })}
      </div>
    </div>
  );
}

export default function PptChartGeneratorPage() {
  const [text, setText] = useState("");
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");
  const [chart, setChart] = useState<any | null>(null);
  const [pptx, setPptx] = useState<string | null>(null);
  const [edits, setEdits] = useState("{}");
  const [color, setColor] = useState("#f59e0b");
  const [colors, setColors] = useState<string[]>([]);
  const [left, setLeft] = useState(1);
  const [top, setTop] = useState(1.5);
  const [widthIn, setWidthIn] = useState(8);
  const [heightIn, setHeightIn] = useState(5.5);
  const [titleLeft, setTitleLeft] = useState(0.5);
  const [titleTop, setTitleTop] = useState(0.3);
  const [titleWidthIn, setTitleWidthIn] = useState(9);
  const [titleHeightIn, setTitleHeightIn] = useState(1);
  const [shapes, setShapes] = useState<any[]>([]);
  const [background, setBackground] = useState<string | null>(null);
  const [showEdits, setShowEdits] = useState(false);
  const textRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    try {
      const parsed = JSON.parse(edits || "{}");
      if (parsed.color) {
        setColor(parsed.color);
      }
      if (Array.isArray(parsed.colors)) {
        setColors(parsed.colors);
      } else {
        setColors([]);
      }
      if (typeof parsed.left === "number") setLeft(parsed.left);
      if (typeof parsed.top === "number") setTop(parsed.top);
      if (typeof parsed.width === "number") setWidthIn(parsed.width);
      if (typeof parsed.height === "number") setHeightIn(parsed.height);
      if (typeof parsed.title_left === "number") setTitleLeft(parsed.title_left);
      if (typeof parsed.title_top === "number") setTitleTop(parsed.title_top);
      if (typeof parsed.title_width === "number") setTitleWidthIn(parsed.title_width);
      if (typeof parsed.title_height === "number") setTitleHeightIn(parsed.title_height);
      if (Array.isArray(parsed.shapes)) setShapes(parsed.shapes);
      if (typeof parsed.background_image === "string") setBackground(parsed.background_image);
    } catch {
      // ignore parse errors
    }
  }, [edits]);

  useEffect(() => {
    const el = textRef.current;
    if (el) {
      el.style.height = "auto";
      const max = 9 * 24;
      el.style.height = Math.min(el.scrollHeight, max) + "px";
    }
  }, [text]);

  const handleGenerate = async () => {
    if (!text) return;
    setLoading(true);
    setMessage("");
    try {
      const res = await fetch("/api/ppt-chart-generator?preview=1", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ text }),
      });
      if (!res.ok) {
        const data = await res.json().catch(() => ({}));
        throw new Error(data.error || "Request failed");
      }
      const data = await res.json();
      if (data.pptx && data.chart) {
        const argsWithColor = {
          ...data.chart.args,
          color: data.chart.args.color || "#f59e0b",
          colors: data.chart.args.colors || [],
          left: typeof data.chart.args.left === "number" ? data.chart.args.left : 1,
          top: typeof data.chart.args.top === "number" ? data.chart.args.top : 1.5,
          width: typeof data.chart.args.width === "number" ? data.chart.args.width : 8,
          height: typeof data.chart.args.height === "number" ? data.chart.args.height : 5.5,
          title_left: typeof data.chart.args.title_left === "number" ? data.chart.args.title_left : 0.5,
          title_top: typeof data.chart.args.title_top === "number" ? data.chart.args.title_top : 0.3,
          title_width: typeof data.chart.args.title_width === "number" ? data.chart.args.title_width : 9,
          title_height: typeof data.chart.args.title_height === "number" ? data.chart.args.title_height : 1,
          shapes: Array.isArray(data.chart.args.shapes) ? data.chart.args.shapes : [],
          background_image:
            typeof data.chart.args.background_image === "string"
              ? data.chart.args.background_image
              : null,
        };
        if (argsWithColor.colors.length === 0 && Array.isArray(argsWithColor.data)) {
          argsWithColor.colors = Array(argsWithColor.data.length).fill(argsWithColor.color);
        }
        setChart({ type: data.chart.type, args: argsWithColor });
        setPptx(data.pptx);
        setEdits(JSON.stringify(argsWithColor, null, 2));
        setColor(argsWithColor.color);
        setColors(argsWithColor.colors);
        setLeft(argsWithColor.left);
        setTop(argsWithColor.top);
        setWidthIn(argsWithColor.width);
        setHeightIn(argsWithColor.height);
        setTitleLeft(argsWithColor.title_left);
        setTitleTop(argsWithColor.title_top);
        setTitleWidthIn(argsWithColor.title_width);
        setTitleHeightIn(argsWithColor.title_height);
        setShapes(argsWithColor.shapes);
        setBackground(argsWithColor.background_image);
        setMessage("Preview generated.");
      } else {
        setChart(null);
        setPptx(null);
        setMessage(data.message || "No plottable data found.");
      }
    } catch (err: any) {
      console.error("ppt-chart-generator error", err);
      setMessage(err.message || "Error generating chart");
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = () => {
    if (!pptx) return;
    const a = document.createElement("a");
    a.href = `data:application/vnd.openxmlformats-officedocument.presentationml.presentation;base64,${pptx}`;
    a.download = "chart.pptx";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const handleApplyEdits = async () => {
    if (!chart) return;
    let parsed: any;
    try {
      parsed = JSON.parse(edits || "{}");
    } catch (err) {
      setMessage("Invalid JSON edits");
      return;
    }
    if (!parsed.color) {
      parsed.color = color;
    } else {
      setColor(parsed.color);
    }
    if (!parsed.colors && colors.length) {
      parsed.colors = colors;
    } else if (Array.isArray(parsed.colors)) {
      setColors(parsed.colors);
    }
    if (typeof parsed.left !== "number") parsed.left = left;
    if (typeof parsed.top !== "number") parsed.top = top;
    if (typeof parsed.width !== "number") parsed.width = widthIn;
    if (typeof parsed.height !== "number") parsed.height = heightIn;
    if (typeof parsed.title_left !== "number") parsed.title_left = titleLeft;
    if (typeof parsed.title_top !== "number") parsed.title_top = titleTop;
    if (typeof parsed.title_width !== "number") parsed.title_width = titleWidthIn;
    if (typeof parsed.title_height !== "number") parsed.title_height = titleHeightIn;
    if (!Array.isArray(parsed.shapes)) parsed.shapes = shapes;
    if (typeof parsed.background_image !== "string" && background)
      parsed.background_image = background;
    setLoading(true);
    setMessage("");
    try {
      const res = await fetch("/api/ppt-chart-generator?preview=1", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ chart: { type: chart.type, args: parsed } }),
      });
      if (!res.ok) {
        const data = await res.json().catch(() => ({}));
        throw new Error(data.error || "Request failed");
      }
      const data = await res.json();
      if (data.pptx && data.chart) {
        const argsWithColor = {
          ...data.chart.args,
          color: data.chart.args.color || color,
          colors: data.chart.args.colors || colors,
          left: typeof data.chart.args.left === "number" ? data.chart.args.left : left,
          top: typeof data.chart.args.top === "number" ? data.chart.args.top : top,
          width: typeof data.chart.args.width === "number" ? data.chart.args.width : widthIn,
          height: typeof data.chart.args.height === "number" ? data.chart.args.height : heightIn,
          title_left: typeof data.chart.args.title_left === "number" ? data.chart.args.title_left : titleLeft,
          title_top: typeof data.chart.args.title_top === "number" ? data.chart.args.title_top : titleTop,
          title_width: typeof data.chart.args.title_width === "number" ? data.chart.args.title_width : titleWidthIn,
          title_height: typeof data.chart.args.title_height === "number" ? data.chart.args.title_height : titleHeightIn,
          shapes: Array.isArray(data.chart.args.shapes) ? data.chart.args.shapes : shapes,
        };
        if (argsWithColor.colors.length === 0 && Array.isArray(argsWithColor.data)) {
          argsWithColor.colors = Array(argsWithColor.data.length).fill(argsWithColor.color);
        }
        setChart({ type: data.chart.type, args: argsWithColor });
        setPptx(data.pptx);
        setEdits(JSON.stringify(argsWithColor, null, 2));
        setColor(argsWithColor.color);
        setColors(argsWithColor.colors);
        setLeft(argsWithColor.left);
        setTop(argsWithColor.top);
        setWidthIn(argsWithColor.width);
        setHeightIn(argsWithColor.height);
        setTitleLeft(argsWithColor.title_left);
        setTitleTop(argsWithColor.title_top);
        setTitleWidthIn(argsWithColor.title_width);
        setTitleHeightIn(argsWithColor.title_height);
        setShapes(argsWithColor.shapes);
        setBackground(argsWithColor.background_image);
        setMessage("Preview updated.");
      } else {
        setMessage(data.message || "Unable to update chart");
      }
    } catch (err: any) {
      console.error("ppt-chart-generator edit error", err);
      setMessage(err.message || "Error applying edits");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <SubpageHeader current="ppt-chart-generator" />
      <h1 className="text-3xl font-bold mb-6">PowerPoint Chart Generator</h1>
      <p className="mb-4">
        Enter text containing numerical data, and the AI will attempt to
        generate a PowerPoint chart.
      </p>
      <Textarea
        ref={textRef}
        value={text}
        onChange={(e) => setText(e.target.value)}
        placeholder="Enter text with numeric data..."
        rows={1}
        className="mb-4 min-h-[38px] max-h-[216px] overflow-y-auto"
      />
      <Button onClick={handleGenerate} disabled={loading} className="mb-4">
        {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
        Generate
      </Button>
      {chart && (
        <>
          <ChartPreview
            chart={chart}
            left={left}
            top={top}
            width={widthIn}
            height={heightIn}
            titleLeft={titleLeft}
            titleTop={titleTop}
            titleWidth={titleWidthIn}
            titleHeight={titleHeightIn}
            shapes={shapes}
            background={background}
            onShapesChange={(s) => {
              setShapes(s);
              try {
                const parsed = JSON.parse(edits || "{}");
                parsed.shapes = s;
                setEdits(JSON.stringify(parsed, null, 2));
              } catch {}
            }}
          />
          <div className="mb-2 flex flex-wrap items-center gap-2">
            <label className="mr-2 font-medium">Color:</label>
            {colors.length > 0 ? (
              colors.map((c, idx) => (
                <input
                  key={idx}
                  type="color"
                  value={c}
                  onChange={(e) => {
                    const newColors = [...colors];
                    newColors[idx] = e.target.value;
                    setColors(newColors);
                    try {
                      const parsed = JSON.parse(edits || "{}");
                      parsed.colors = newColors;
                      setEdits(JSON.stringify(parsed, null, 2));
                    } catch {}
                  }}
                />
              ))
            ) : (
              <input
                type="color"
                value={color}
                onChange={(e) => {
                  const newColor = e.target.value;
                  setColor(newColor);
                  try {
                    const parsed = JSON.parse(edits || "{}");
                    parsed.color = newColor;
                    setEdits(JSON.stringify(parsed, null, 2));
                  } catch {}
                }}
              />
            )}
          </div>
          <div className="mb-2 flex flex-wrap items-center gap-2">
            <label className="mr-2 font-medium">Position/Size (inches):</label>
            <label className="flex items-center gap-1">
              Left:
              <input
                type="number"
                step="0.1"
                value={left}
                onChange={(e) => {
                  const v = parseFloat(e.target.value);
                  setLeft(v);
                  try {
                    const parsed = JSON.parse(edits || "{}");
                    parsed.left = v;
                    setEdits(JSON.stringify(parsed, null, 2));
                  } catch {}
                }}
                className="w-20 border rounded px-1"
              />
            </label>
            <label className="flex items-center gap-1">
              Top:
              <input
                type="number"
                step="0.1"
                value={top}
                onChange={(e) => {
                  const v = parseFloat(e.target.value);
                  setTop(v);
                  try {
                    const parsed = JSON.parse(edits || "{}");
                    parsed.top = v;
                    setEdits(JSON.stringify(parsed, null, 2));
                  } catch {}
                }}
                className="w-20 border rounded px-1"
              />
            </label>
            <label className="flex items-center gap-1">
              Width:
              <input
                type="number"
                step="0.1"
                value={widthIn}
                onChange={(e) => {
                  const v = parseFloat(e.target.value);
                  setWidthIn(v);
                  try {
                    const parsed = JSON.parse(edits || "{}");
                    parsed.width = v;
                    setEdits(JSON.stringify(parsed, null, 2));
                  } catch {}
                }}
                className="w-20 border rounded px-1"
              />
            </label>
            <label className="flex items-center gap-1">
              Height:
              <input
                type="number"
                step="0.1"
                value={heightIn}
                onChange={(e) => {
                  const v = parseFloat(e.target.value);
                  setHeightIn(v);
                  try {
                    const parsed = JSON.parse(edits || "{}");
                    parsed.height = v;
                    setEdits(JSON.stringify(parsed, null, 2));
                  } catch {}
                }}
                className="w-20 border rounded px-1"
          />
        </label>
      </div>
      <div className="mb-2 flex flex-wrap items-center gap-2">
        <label className="mr-2 font-medium">Title Position (inches):</label>
        <label className="flex items-center gap-1">
          Left:
          <input
            type="number"
            step="0.1"
            value={titleLeft}
            onChange={(e) => {
              const v = parseFloat(e.target.value);
              setTitleLeft(v);
              try {
                const parsed = JSON.parse(edits || "{}");
                parsed.title_left = v;
                setEdits(JSON.stringify(parsed, null, 2));
              } catch {}
            }}
            className="w-20 border rounded px-1"
          />
        </label>
        <label className="flex items-center gap-1">
          Top:
          <input
            type="number"
            step="0.1"
            value={titleTop}
            onChange={(e) => {
              const v = parseFloat(e.target.value);
              setTitleTop(v);
              try {
                const parsed = JSON.parse(edits || "{}");
                parsed.title_top = v;
                setEdits(JSON.stringify(parsed, null, 2));
              } catch {}
            }}
            className="w-20 border rounded px-1"
          />
        </label>
        <label className="flex items-center gap-1">
          Width:
          <input
            type="number"
            step="0.1"
            value={titleWidthIn}
            onChange={(e) => {
              const v = parseFloat(e.target.value);
              setTitleWidthIn(v);
              try {
                const parsed = JSON.parse(edits || "{}");
                parsed.title_width = v;
                setEdits(JSON.stringify(parsed, null, 2));
              } catch {}
            }}
            className="w-20 border rounded px-1"
          />
        </label>
        <label className="flex items-center gap-1">
          Height:
          <input
            type="number"
            step="0.1"
            value={titleHeightIn}
            onChange={(e) => {
              const v = parseFloat(e.target.value);
              setTitleHeightIn(v);
              try {
                const parsed = JSON.parse(edits || "{}");
                parsed.title_height = v;
                setEdits(JSON.stringify(parsed, null, 2));
              } catch {}
            }}
            className="w-20 border rounded px-1"
          />
        </label>
      </div>

      <div className="mb-2">
        <div className="flex flex-wrap items-center gap-2 mb-2">
          <label className="mr-2 font-medium">Shapes:</label>
          <label className="flex items-center gap-1">
            Background:
            <input
              type="file"
              accept="image/*"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (!file) return;
                const reader = new FileReader();
                reader.onload = () => {
                  const b64 = (reader.result as string).split(",")[1];
                  setBackground(b64);
                  try {
                    const parsed = JSON.parse(edits || "{}");
                    parsed.background_image = b64;
                    setEdits(JSON.stringify(parsed, null, 2));
                  } catch {}
                };
                reader.readAsDataURL(file);
              }}
              className="h-6"
            />
          </label>
          <Button
            type="button"
            onClick={() => {
              const newShapes = [...shapes, { type: "rect", left: 1, top: 1, width: 2, height: 1, color: "#cccccc" }];
              setShapes(newShapes);
              try {
                const parsed = JSON.parse(edits || "{}");
                parsed.shapes = newShapes;
                setEdits(JSON.stringify(parsed, null, 2));
              } catch {}
            }}
            className="h-6 px-2"
          >
            Add Rectangle
          </Button>
          <Button
            type="button"
            onClick={() => {
              const newShapes = [...shapes, { type: "ellipse", left: 1, top: 1, width: 2, height: 1, color: "#cccccc" }];
              setShapes(newShapes);
              try {
                const parsed = JSON.parse(edits || "{}");
                parsed.shapes = newShapes;
                setEdits(JSON.stringify(parsed, null, 2));
              } catch {}
            }}
            className="h-6 px-2"
          >
            Add Ellipse
          </Button>
          <Button
            type="button"
            onClick={() => {
              const newShapes = [...shapes, { type: "text", text: "Text", left: 1, top: 1, width: 2, height: 1, color: "#000000" }];
              setShapes(newShapes);
              try {
                const parsed = JSON.parse(edits || "{}");
                parsed.shapes = newShapes;
                setEdits(JSON.stringify(parsed, null, 2));
              } catch {}
            }}
            className="h-6 px-2"
          >
            Add Text
          </Button>
        </div>
        {shapes.map((s, idx) => (
          <div key={idx} className="mb-1 flex flex-wrap items-center gap-2 pl-4">
            <span className="mr-2">{s.type}</span>
            <label className="flex items-center gap-1">
              Left:
              <input
                type="number"
                step="0.1"
                value={s.left}
                onChange={(e) => {
                  const v = parseFloat(e.target.value);
                  const newShapes = [...shapes];
                  newShapes[idx].left = v;
                  setShapes(newShapes);
                  try {
                    const parsed = JSON.parse(edits || "{}");
                    parsed.shapes = newShapes;
                    setEdits(JSON.stringify(parsed, null, 2));
                  } catch {}
                }}
                className="w-16 border rounded px-1"
              />
            </label>
            <label className="flex items-center gap-1">
              Top:
              <input
                type="number"
                step="0.1"
                value={s.top}
                onChange={(e) => {
                  const v = parseFloat(e.target.value);
                  const newShapes = [...shapes];
                  newShapes[idx].top = v;
                  setShapes(newShapes);
                  try {
                    const parsed = JSON.parse(edits || "{}");
                    parsed.shapes = newShapes;
                    setEdits(JSON.stringify(parsed, null, 2));
                  } catch {}
                }}
                className="w-16 border rounded px-1"
              />
            </label>
            <label className="flex items-center gap-1">
              Width:
              <input
                type="number"
                step="0.1"
                value={s.width}
                onChange={(e) => {
                  const v = parseFloat(e.target.value);
                  const newShapes = [...shapes];
                  newShapes[idx].width = v;
                  setShapes(newShapes);
                  try {
                    const parsed = JSON.parse(edits || "{}");
                    parsed.shapes = newShapes;
                    setEdits(JSON.stringify(parsed, null, 2));
                  } catch {}
                }}
                className="w-16 border rounded px-1"
              />
            </label>
            <label className="flex items-center gap-1">
              Height:
              <input
                type="number"
                step="0.1"
                value={s.height}
                onChange={(e) => {
                  const v = parseFloat(e.target.value);
                  const newShapes = [...shapes];
                  newShapes[idx].height = v;
                  setShapes(newShapes);
                  try {
                    const parsed = JSON.parse(edits || "{}");
                    parsed.shapes = newShapes;
                    setEdits(JSON.stringify(parsed, null, 2));
                  } catch {}
                }}
                className="w-16 border rounded px-1"
              />
            </label>
            {s.type !== "text" ? (
              <>
                <label className="flex items-center gap-1">
                  Fill:
                  <input
                    type="color"
                    value={s.color}
                    onChange={(e) => {
                      const newShapes = [...shapes];
                      newShapes[idx].color = e.target.value;
                      setShapes(newShapes);
                      try {
                        const parsed = JSON.parse(edits || "{}");
                        parsed.shapes = newShapes;
                        setEdits(JSON.stringify(parsed, null, 2));
                      } catch {}
                    }}
                  />
                </label>
                <label className="flex items-center gap-1">
                  Border:
                  <input
                    type="color"
                    value={s.borderColor || "#000000"}
                    onChange={(e) => {
                      const newShapes = [...shapes];
                      newShapes[idx].borderColor = e.target.value;
                      setShapes(newShapes);
                      try {
                        const parsed = JSON.parse(edits || "{}");
                        parsed.shapes = newShapes;
                        setEdits(JSON.stringify(parsed, null, 2));
                      } catch {}
                    }}
                  />
                </label>
              </>
            ) : (
              <>
                <label className="flex items-center gap-1">
                  Text:
                  <input
                    type="text"
                    value={s.text}
                    onChange={(e) => {
                      const newShapes = [...shapes];
                      newShapes[idx].text = e.target.value;
                      setShapes(newShapes);
                      try {
                        const parsed = JSON.parse(edits || "{}");
                        parsed.shapes = newShapes;
                        setEdits(JSON.stringify(parsed, null, 2));
                      } catch {}
                    }}
                    className="border rounded px-1"
                  />
                </label>
                <label className="flex items-center gap-1">
                  Color:
                  <input
                    type="color"
                    value={s.color}
                    onChange={(e) => {
                      const newShapes = [...shapes];
                      newShapes[idx].color = e.target.value;
                      setShapes(newShapes);
                      try {
                        const parsed = JSON.parse(edits || "{}");
                        parsed.shapes = newShapes;
                        setEdits(JSON.stringify(parsed, null, 2));
                      } catch {}
                    }}
                  />
                </label>
                <label className="flex items-center gap-1">
                  Size:
                  <input
                    type="number"
                    className="w-12 border rounded px-1"
                    value={s.font_size || 14}
                    onChange={(e) => {
                      const newShapes = [...shapes];
                      newShapes[idx].font_size = parseFloat(e.target.value);
                      setShapes(newShapes);
                      try {
                        const parsed = JSON.parse(edits || "{}");
                        parsed.shapes = newShapes;
                        setEdits(JSON.stringify(parsed, null, 2));
                      } catch {}
                    }}
                  />
                </label>
                <label className="flex items-center gap-1">
                  Bold:
                  <input
                    type="checkbox"
                    checked={!!s.bold}
                    onChange={(e) => {
                      const newShapes = [...shapes];
                      newShapes[idx].bold = e.target.checked;
                      setShapes(newShapes);
                      try {
                        const parsed = JSON.parse(edits || "{}");
                        parsed.shapes = newShapes;
                        setEdits(JSON.stringify(parsed, null, 2));
                      } catch {}
                    }}
                  />
                </label>
                <label className="flex items-center gap-1">
                  Align:
                  <select
                    value={s.align || "left"}
                    onChange={(e) => {
                      const newShapes = [...shapes];
                      newShapes[idx].align = e.target.value;
                      setShapes(newShapes);
                      try {
                        const parsed = JSON.parse(edits || "{}");
                        parsed.shapes = newShapes;
                        setEdits(JSON.stringify(parsed, null, 2));
                      } catch {}
                    }}
                    className="border rounded px-1"
                  >
                    <option value="left">Left</option>
                    <option value="center">Center</option>
                    <option value="right">Right</option>
                  </select>
                </label>
                <label className="flex items-center gap-1">
                  Bullet:
                  <input
                    type="checkbox"
                    checked={!!s.bullet}
                    onChange={(e) => {
                      const newShapes = [...shapes];
                      newShapes[idx].bullet = e.target.checked;
                      setShapes(newShapes);
                      try {
                        const parsed = JSON.parse(edits || "{}");
                        parsed.shapes = newShapes;
                        setEdits(JSON.stringify(parsed, null, 2));
                      } catch {}
                    }}
                  />
                </label>
                <label className="flex items-center gap-1">
                  Border:
                  <input
                    type="color"
                    value={s.borderColor || "#000000"}
                    onChange={(e) => {
                      const newShapes = [...shapes];
                      newShapes[idx].borderColor = e.target.value;
                      setShapes(newShapes);
                      try {
                        const parsed = JSON.parse(edits || "{}");
                        parsed.shapes = newShapes;
                        setEdits(JSON.stringify(parsed, null, 2));
                      } catch {}
                    }}
                  />
                </label>
              </>
            )}
            <Button
              type="button"
              onClick={() => {
                const newShapes = shapes.filter((_, i) => i !== idx);
                setShapes(newShapes);
                try {
                  const parsed = JSON.parse(edits || "{}");
                  parsed.shapes = newShapes;
                  setEdits(JSON.stringify(parsed, null, 2));
                } catch {}
              }}
              className="h-6 px-2"
            >
              Remove
            </Button>
          </div>
        ))}
      </div>

      <Button onClick={() => setShowEdits(!showEdits)} className="mb-2">
        {showEdits ? "Hide JSON" : "Show JSON"}
      </Button>
      {showEdits && (
        <Textarea
          value={edits}
          onChange={(e) => setEdits(e.target.value)}
          className="min-h-[160px] mb-2"
        />
      )}
      <Button onClick={handleApplyEdits} disabled={loading} className="mb-4 mr-4">
        {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
        Apply Edits
      </Button>
        </>
      )}
      {pptx && (
        <Button onClick={handleDownload} className="mb-4">
          Download PowerPoint
        </Button>
      )}
      {message && <p className="mt-4">{message}</p>}
    </div>
  );
}
