import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';

export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const vendorName = searchParams.get('name');
    
    // Get optional search parameters
    const title = searchParams.get('title') || '';
    const abstract = searchParams.get('abstract') || '';
    const therapeuticArea = searchParams.get('therapeuticArea') || '';
    const region = searchParams.get('region') || '';
    const products = searchParams.get('products') || '';
    const studyType = searchParams.get('studyType') || '';
    const all = searchParams.get('all') || '';
    
    if (!vendorName) {
      return NextResponse.json(
        { error: 'Vendor name is required' },
        { status: 400 }
      );
    }

    // Connect to MongoDB
    const { db } = await connectToDatabase();
    
    // Create a regex that matches the vendor name as a whole word (case-insensitive)
    // This prevents partial matches like "ConnectHEOR" when searching for "HEOR"
    const escapedVendorName = vendorName.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
    const vendorRegex = new RegExp(`\\b${escapedVendorName}\\b`, 'i');
    
    // Build the base query for vendor matching
    const query: any = {
      $or: [
        { vendor: vendorRegex },
        { Vendor: vendorRegex },
        { OriginalAffiliation: vendorRegex }
      ]
    };
    
    // Add content filters if any search parameters are provided
    const hasSearchFilters = title || abstract || therapeuticArea || region || products || studyType || all;

    if (hasSearchFilters) {
      const andConditions: any[] = [];
      
      if (title) {
        andConditions.push({ 
          $or: [
            { Title: { $regex: title, $options: 'i' } },
            { title: { $regex: title, $options: 'i' } }
          ]
        });
      }
      
      if (abstract) {
        andConditions.push({ 
          $or: [
            { Abstract: { $regex: abstract, $options: 'i' } },
            { abstract: { $regex: abstract, $options: 'i' } }
          ]
        });
      }
      
      if (therapeuticArea) {
        andConditions.push({ 
          $or: [
            { 'therapeutic_area': { $regex: therapeuticArea, $options: 'i' } },
            { 'therapeuticArea': { $regex: therapeuticArea, $options: 'i' } },
            { 'therapeutic-area': { $regex: therapeuticArea, $options: 'i' } }
          ]
        });
      }
      
      if (region) {
        andConditions.push({ 
          $or: [
            { 'region': { $regex: region, $options: 'i' } },
            { 'regions': { $regex: region, $options: 'i' } }
          ]
        });
      }
      
      if (products) {
        andConditions.push({ 
          $or: [
            { 'products': { $regex: products, $options: 'i' } },
            { 'product': { $regex: products, $options: 'i' } }
          ]
        });
      }
      
      if (studyType) {
        andConditions.push({ 
          $or: [
            { 'study_type': { $regex: studyType, $options: 'i' } },
            { 'studyType': { $regex: studyType, $options: 'i' } },
            { 'study-type': { $regex: studyType, $options: 'i' } }
          ]
        });
      }
      
      // Add all conditions to the main query
      if (andConditions.length > 0) {
        query.$and = andConditions;
      }
    }
    
    console.log(`Fetching publications for vendor "${vendorName}" with${hasSearchFilters ? '' : 'out'} search filters`);
    console.log('Query:', JSON.stringify(query, null, 2));
    
    // Try a more flexible search if the exact match doesn't work
    let articles = await db.collection('vendor_articles')
      .find(query)
      .limit(20)
      .toArray();
    
    // If no results with the exact match, try a more flexible search
    if (articles.length === 0) {
      console.log(`No exact matches found for "${vendorName}", trying partial match...`);
      
      // Create a more flexible regex that matches any part of the vendor name
      const flexibleRegex = new RegExp(escapedVendorName, 'i');
      
      // Update the query with the flexible regex
      const flexibleQuery = {
        ...query,
        $or: [
          { vendor: flexibleRegex },
          { Vendor: flexibleRegex },
          { OriginalAffiliation: flexibleRegex }
        ]
      };
      
      console.log('Flexible query:', JSON.stringify(flexibleQuery, null, 2));
      
      articles = await db.collection('vendor_articles')
        .find(flexibleQuery)
        .limit(20)
        .toArray();
    }
    
    console.log(`Found ${articles.length} publications for vendor "${vendorName}"`);
    
    return NextResponse.json({ publications: articles });
  } catch (error) {
    console.error('Error fetching vendor publications:', error);
    return NextResponse.json(
      { error: 'Failed to fetch publications' },
      { status: 500 }
    );
  }
}
