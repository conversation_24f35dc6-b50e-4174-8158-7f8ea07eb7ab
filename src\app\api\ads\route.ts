import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET() {
  try {
    // Get the absolute path to the public/ads directory
    const adsDirectory = path.join(process.cwd(), 'public', 'ads');
    
    // Read the directory
    const fileNames = fs.readdirSync(adsDirectory);
    
    // Filter for image files only
    const imageFiles = fileNames.filter(file => {
      const ext = path.extname(file).toLowerCase();
      return ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'].includes(ext);
    });
    
    // Return the list of image files
    return NextResponse.json({ images: imageFiles });
  } catch (error) {
    console.error('Error reading ads directory:', error);
    return NextResponse.json(
      { error: 'Failed to read ads directory' },
      { status: 500 }
    );
  }
}