/**
 * Represents a vendor with basic contact information.
 */
export interface Vendor {
  /**
   * The unique identifier for the vendor.
   */
  id: string;
  /**
   * The name of the vendor.
   */
  name: string;
  /**
   * The email address of the vendor.
   */
  email: string;
  /**
   * A description of the vendor's services or products.
   */
  description: string;
  /**
   * URL of the vendor's logo.
   */
  logoUrl: string;
  /**
   * URL of the vendor's website.
   */
  websiteUrl: string;
  /**
   * URL of the vendor's LinkedIn profile.
   */
  linkedinUrl: string;
  /**
   * List of services provided by the vendor.
   */
  services: string;
  /**
   * Whether the vendor is featured.
   */
  featured: boolean;
  /**
   * URL of publication's page, if available
   */
  publications: string;
  /**
   * Country where the vendor's headquarters is located
   */
  hqCountry: string;
  /**
   * Whether the vendor is a data provider
   */
  dataProvider: boolean;
}

// Import the JSON data
import vendorData from '../../../heor_and_market_access_vendor_details_with_logos.json';

/**
 * Retrieves a list of vendors.
 *
 * @returns A promise that resolves to an array of Vendor objects.
 */
export async function getVendors(): Promise<Vendor[]> {
  return vendorData.map((vendor: any, index: number) => {
    // Debug logging for each vendor
    console.log('Raw vendor data:', {
      companyName: vendor.Company,
      rawLogoField: vendor.logo,  // This should show us the "logo" field from JSON
      allFields: JSON.stringify(vendor, null, 2)  // Show all fields for this vendor
    });

    // Pull the raw 'logo' field (if present) and trim whitespace
    const rawLogo = typeof vendor.logo === 'string'
      ? vendor.logo.trim()
      : '';

    console.log('Processed logo info:', {
      companyName: vendor.Company,
      rawLogo,
      willUseLogo: !!rawLogo,
      finalLogoUrl: rawLogo ? `/icons/${rawLogo}` : `https://picsum.photos/id/${index + 1}/50/50`
    });

    // Build the URL into your public/icons folder, or fallback to picsum
    const logoUrl = rawLogo
      ? `/icons/${rawLogo}`
      : `https://picsum.photos/id/${index + 1}/50/50`;

    return {
      id: (index + 1).toString(),
      name: vendor.Company,
      email: vendor['Contact info'],
      description: '',
      logoUrl,
      websiteUrl: vendor.Website,
      linkedinUrl: vendor.LinkedIn || '#',
      services: vendor.Services || 'No services listed.',
      featured: vendor.Featured === 1,
      publications: vendor.Publications,
      hqCountry: vendor['HQ country'] || 'Unknown',
      dataProvider: vendor['Data provider'] === 1
    };
  });
}
