import { NextResponse } from 'next/server';
import OpenAI from 'openai';

const client = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY, // Use server-side env var
});

export async function POST(request: Request) {
  try {
    const { messages, model } = await request.json();
    
    const completion = await client.chat.completions.create({
      model: model || "gpt-4.1-mini",
      messages,
    });
    
    return NextResponse.json(completion.choices[0].message);
  } catch (error) {
    console.error('OpenAI API error:', error);
    return NextResponse.json(
      { error: 'There was an error processing your request' },
      { status: 500 }
    );
  }
}

