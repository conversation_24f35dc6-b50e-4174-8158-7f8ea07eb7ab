import sys
import requests
import xml.etree.ElementTree as ET


def get_pmcid_from_pmid(pmid: str):
    url = "https://www.ncbi.nlm.nih.gov/pmc/utils/idconv/v1.0/"
    params = {"tool": "pmid2pmc", "email": "<EMAIL>", "ids": pmid, "format": "json"}
    r = requests.get(url, params=params)
    if not r.ok:
        return None
    data = r.json()
    records = data.get("records", [])
    if records and "pmcid" in records[0]:
        return records[0]["pmcid"]
    return None


def extract_markdown_from_pmc_xml(xml_content: str):
    try:
        ns = {
            "oai": "http://www.openarchives.org/OAI/2.0/",
            "jats": "https://jats.nlm.nih.gov/ns/archiving/1.3/",
        }
        root = ET.fromstring(xml_content)
        article = root.find(".//jats:article", namespaces=ns)
        if article is None:
            return "\u26A0\uFE0F No article found in XML."
        md = []
        title_el = article.find(".//jats:title-group/jats:article-title", namespaces=ns)
        if title_el is not None:
            md.append(f"# {''.join(title_el.itertext()).strip()}\n")
        abstract_el = article.find(".//jats:abstract", namespaces=ns)
        if abstract_el is not None:
            md.append("## Abstract\n")
            for p in abstract_el.findall(".//jats:p", namespaces=ns):
                md.append(f"{''.join(p.itertext()).strip()}\n")
        body = article.find("jats:body", namespaces=ns)
        if body is not None:
            for sec in body.findall(".//jats:sec", namespaces=ns):
                title = sec.find("jats:title", namespaces=ns)
                if title is not None:
                    md.append(f"\n## {''.join(title.itertext()).strip()}\n")
                for p in sec.findall("jats:p", namespaces=ns):
                    text = ''.join(p.itertext()).strip()
                    if text:
                        md.append(text + "\n")
        return "\n".join(md).strip()
    except ET.ParseError as e:
        return f"\u26A0\uFE0F XML Parse Error: {e}"


def download_and_convert_pmc_to_markdown(pmcid: str):
    url = "https://www.ncbi.nlm.nih.gov/pmc/oai/oai.cgi"
    params = {
        "verb": "GetRecord",
        "identifier": f"oai:pubmedcentral.nih.gov:{pmcid[3:]}",
        "metadataPrefix": "pmc",
    }
    response = requests.get(url, params=params)
    if response.status_code == 200 and "<GetRecord>" in response.text:
        return extract_markdown_from_pmc_xml(response.text)
    else:
        return None


if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("", end="")
        sys.exit(0)
    pmid = sys.argv[1]
    pmcid = get_pmcid_from_pmid(pmid)
    if not pmcid:
        print("", end="")
        sys.exit(0)
    md = download_and_convert_pmc_to_markdown(pmcid)
    if md is None:
        print("", end="")
        sys.exit(0)
    print(f"Converted PMID {pmid} (PMCID {pmcid}) to markdown", file=sys.stderr)
    print(md)
