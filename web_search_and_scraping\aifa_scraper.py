#!/usr/bin/env python3
"""
AIFA News Scraper — Creates RSS feed from AIFA search results
Renders https://www.aifa.gov.it/en/ricerca-aifa?searchKeywords=&structures=93604
and extracts every .card element into an RSS feed.
"""

import time, re, sys
from datetime import datetime
from urllib.parse import urljoin
import xml.etree.ElementTree as ET

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class AIFAScraper:
    def __init__(self):
        self.base_url   = "https://www.aifa.gov.it"
        self.target_url = "https://www.aifa.gov.it/en/ricerca-aifa?searchKeywords=&structures=93604"
        
        opts = Options()
        opts.add_argument("--headless=new")
        opts.add_argument("--no-sandbox")
        opts.add_argument("--disable-dev-shm-usage")
        opts.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
        self.driver = webdriver.Chrome(options=opts)

    def clean(self, txt):
        return re.sub(r"\s+", " ", txt).strip()

    def parse_date(self, s):
        # AIFA uses DD/MM/YYYY format
        try:
            date_str = s.strip()
            return datetime.strptime(date_str, "%d/%m/%Y")
        except:
            try:
                # Fallback for other formats
                return datetime.strptime(date_str, "%Y-%m-%d")
            except:
                return datetime.now()

    def scrape(self):
        print(f"[INFO] Loading {self.target_url}")
        self.driver.get(self.target_url)

        # Wait for the results container to load
        try:
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "div.result-container"))
            )
            print("[INFO] Results container found")
        except:
            print("[ERROR] Could not find results container")
            self.driver.quit()
            return []

        # Scroll and pause to allow full render
        self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(3)

        # Look for pagination and load more results if needed
        try:
            # Check if there are more pages
            page_info = self.driver.find_element(By.CSS_SELECTOR, "div.col-xs-6 i")
            print(f"[INFO] Found page info: {page_info.text}")
        except:
            print("[INFO] No pagination info found")

        # Collect every card element
        cards = self.driver.find_elements(By.CSS_SELECTOR, "div.card.smooth")
        print(f"[INFO] Found {len(cards)} news items")
        
        updates = []
        for card in cards:
            try:
                # Extract date and category from the bottom span element
                date_txt = ""
                category = "General"
                
                # Look for the span with date and category info
                info_span = card.find_element(By.CSS_SELECTOR, "span.u-color-blu")
                span_text = info_span.text.strip()
                
                # Split by newlines and process each line
                lines = [line.strip() for line in span_text.split('\n') if line.strip()]
                
                for line in lines:
                    # Look for date pattern (DD/MM/YYYY)
                    if "/" in line and len(line.split("/")) == 3:
                        # Extract just the date part (first token)
                        date_txt = line.split()[0] if line.split() else line
                    # Look for category (lines that contain ">" indicating breadcrumb)
                    elif ">" in line:
                        category = line.strip()

                pub_dt = self.parse_date(date_txt) if date_txt else datetime.now()

                # Extract title and link
                title_link = card.find_element(By.CSS_SELECTOR, "h3 a.asset-link")
                title = self.clean(title_link.text)
                href = title_link.get_attribute("href").strip()
                
                # Handle relative URLs
                if href.startswith('/'):
                    link = urljoin(self.base_url, href)
                else:
                    link = href

                # Extract description
                try:
                    desc_element = card.find_element(By.CSS_SELECTOR, "div.content p")
                    desc = self.clean(desc_element.text)
                except:
                    desc = title

                # Check if it's a highlighted news item
                is_highlighted = len(card.find_elements(By.CSS_SELECTOR, "strong.fas.fa-bullhorn")) > 0

                print(f"[DEBUG] Processed: {title[:50]}... | Date: {date_txt} | Category: {category[:30]}...")

                updates.append({
                    "title": title,
                    "link": link,
                    "description": desc,
                    "pub_dt": pub_dt,
                    "category": category,
                    "highlighted": is_highlighted
                })

            except Exception as e:
                print(f"[WARN] Error processing card: {e}")
                continue

        self.driver.quit()
        
        # Sort newest first
        updates.sort(key=lambda u: u["pub_dt"], reverse=True)
        print(f"[INFO] Successfully extracted {len(updates)} items")
        return updates

    def create_rss(self, updates):
        rss = ET.Element("rss", version="2.0", **{"xmlns:atom": "http://www.w3.org/2005/Atom"})
        ch = ET.SubElement(rss, "channel")
        
        for tag, txt in [
            ("title", "AIFA | News and Updates"),
            ("link", self.target_url),
            ("description", "Latest news and updates from the Italian Medicines Agency (AIFA)"),
            ("language", "en-us"),
            ("copyright", f"© AIFA {datetime.now():%Y}"),
            ("lastBuildDate", datetime.now().strftime("%a, %d %b %Y %H:%M:%S +0000"))
        ]:
            e = ET.SubElement(ch, tag)
            e.text = txt

        # Add atom:link for RSS best practices
        atom_link = ET.SubElement(ch, "{http://www.w3.org/2005/Atom}link")
        atom_link.set("href", self.target_url)
        atom_link.set("rel", "self")
        atom_link.set("type", "application/rss+xml")

        for u in updates:
            item = ET.SubElement(ch, "item")
            
            # Use title as-is without fire icon
            title_text = u["title"]
            
            ET.SubElement(item, "title").text = title_text
            ET.SubElement(item, "link").text = u["link"]
            ET.SubElement(item, "guid").text = u["link"]
            
            # Enhanced description with category
            desc_html = f"<p>{u['description']}</p>"
            if u.get("category") and u["category"] != "General":
                desc_html += f"<p><strong>Category:</strong> {u['category']}</p>"
            
            ET.SubElement(item, "description").text = desc_html
            
            pub = u["pub_dt"].strftime("%a, %d %b %Y 12:00:00 +0000")
            ET.SubElement(item, "pubDate").text = pub
            
            # Add category as RSS category
            if u.get("category") and u["category"] != "General":
                ET.SubElement(item, "category").text = u["category"]

        return rss

def main():
    fmt = "xml"
    if len(sys.argv) > 1 and sys.argv[1].lower() in ("text", "txt"):
        fmt = "text"

    scraper = AIFAScraper()
    
    try:
        updates = scraper.scrape()
        
        if not updates:
            print("[ERROR] No data scraped")
            return

        if fmt == "text":
            out = "\n".join(
                f"{i}. {u['pub_dt'].date()} — {u['title']}\n   {u['link']}\n   Category: {u.get('category', 'General')}"
                for i, u in enumerate(updates, start=1)
            )
        else:
            rss = scraper.create_rss(updates)
            raw = ET.tostring(rss, "utf-8")
            from xml.dom import minidom
            pretty = minidom.parseString(raw).toprettyxml(indent="  ")
            # Drop XML declaration line
            out = "\n".join(pretty.splitlines()[1:])

        if out:
            print(out)
            
            # Create output directory if it doesn't exist
            import os
            os.makedirs("web_search_and_scraping/output", exist_ok=True)
            
            fname = f"web_search_and_scraping/output/aifa_news.{fmt}"
            with open(fname, "w", encoding="utf-8") as f:
                if fmt == "xml":
                    f.write('<?xml version="1.0" encoding="utf-8"?>\n')
                f.write(out)
            print(f"[INFO] Saved to {fname}")
        else:
            print("[ERROR] No output generated")
            
    except Exception as e:
        print(f"[ERROR] Scraping failed: {e}")

if __name__ == "__main__":
    main()
