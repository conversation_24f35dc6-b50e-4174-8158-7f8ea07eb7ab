{"mongoUrl": "${MONGODB_URI}", "database": "<PERSON><PERSON><PERSON>", "collection": "nice_reports", "provider": "openai", "apiKey": "${OPENAI_API_KEY}", "baseUrl": "http://localhost:11434", "model": "text-embedding-3-large", "dimensions": 3072, "embedding": {"provider": "openai", "model": "text-embedding-3-large", "dimensions": 3072, "apiKey": "${OPENAI_API_KEY}", "baseUrl": "http://localhost:11434", "batchSize": 100}, "search": {"maxResults": 5, "minScore": 0.7}, "indexName": "vector_index"}