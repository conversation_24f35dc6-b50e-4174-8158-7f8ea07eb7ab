import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const params = request.nextUrl.searchParams;
    const query = params.get('query');
    if (!query) {
      return NextResponse.json({ error: 'Query parameter is required' }, { status: 400 });
    }

    const page = parseInt(params.get('page') || '1', 10);
    const pageSize = parseInt(params.get('pageSize') || '25', 10);
    const startYear = params.get('startYear');
    const endYear = params.get('endYear');
    const sort = params.get('sort') || 'relevance';
    const author = params.get('author');
    const journal = params.get('journal');

    let q = query;
    if (startYear || endYear) {
      const range = `${startYear || '1900'}:${endYear || new Date().getFullYear()}`;
      q += ` AND PUB_YEAR:[${range}]`;
    }
    if (author) {
      q += ` AND AUTH:"${author}"`;
    }
    if (journal) {
      q += ` AND JOURNAL:"${journal}"`;
    }

    const sortParam = sort === 'date' ? 'DATE_D' : '';
    const url = `https://www.ebi.ac.uk/europepmc/webservices/rest/search?query=${encodeURIComponent(q)}&format=json&pageSize=${pageSize}&page=${page}${sortParam ? `&sort=${sortParam}` : ''}`;

    const res = await fetch(url, { cache: 'no-store' });
    if (!res.ok) throw new Error('Europe PMC request failed');
    const data = await res.json();

    const baseResults = (data.resultList?.result || []).map((item: any) => ({
      id: item.id,
      source: item.source,
      title: item.title,
      pubdate: item.pubYear ? String(item.pubYear) : '',
      journal: item.journalTitle,
      authors: item.authorString,
      abstract: item.abstractText || '',
    }));

    const details = await Promise.all(
      baseResults.map((r: any) =>
        fetch(`https://www.ebi.ac.uk/europepmc/webservices/rest/${r.source}/${r.id}/json`, { cache: 'no-store' }).then((d) => (d.ok ? d.json() : null))
      )
    );
    const results = baseResults.map((r: any, idx: number) => {
      const det: any = details[idx] || {};
      const pubs = det.pubTypeList?.pubType;
      const publicationTypes = Array.isArray(pubs) ? pubs : pubs ? [pubs] : [];
      const meshes = det.meshHeadingList?.meshHeading;
      const meshTerms = Array.isArray(meshes)
        ? meshes.map((m: any) => m.descriptorName).filter(Boolean)
        : meshes?.descriptorName
          ? [meshes.descriptorName]
          : [];
      return {
        ...r,
        publicationTypes,
        meshTerms,
      };
    });

    return NextResponse.json(
      { count: data.hitCount || 0, results },
      { headers: { 'Cache-Control': 'no-store' } }
    );
  } catch (err) {
    console.error('Europe PMC search error', err);
    return NextResponse.json(
      { error: 'Failed to search Europe PMC' },
      { status: 500, headers: { 'Cache-Control': 'no-store' } }
    );
  }
}
