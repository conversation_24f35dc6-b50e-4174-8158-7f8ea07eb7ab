import { NextRequest, NextResponse } from 'next/server';
import { getPresignedUrl } from '@/lib/s3';
import { PDFDocument, rgb } from 'pdf-lib';

export async function GET(request: NextRequest) {
  try {
    // Get the filename from the query parameters
    const filename = request.nextUrl.searchParams.get('filename');
    const pageNumber = parseInt(request.nextUrl.searchParams.get('page') || '1', 10);
    const bboxParam = request.nextUrl.searchParams.get('bbox');
    
    console.log('PDF Proxy Request:', { filename, pageNumber, bbox: bboxParam });
    
    if (!filename) {
      return NextResponse.json(
        { error: 'Filename parameter is required' },
        { status: 400 }
      );
    }

    // Generate a presigned URL
    const presignedUrl = await getPresignedUrl(filename);
    console.log('Generated presigned URL for:', filename);
    
    // Fetch the file content
    const response = await fetch(presignedUrl);
    
    if (!response.ok) {
      console.error(`Failed to fetch PDF file: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: 'Failed to fetch PDF file', status: response.status },
        { status: response.status }
      );
    }
    
    // Get the file content as an array buffer
    const fileBuffer = await response.arrayBuffer();
    console.log(`Fetched PDF file: ${fileBuffer.byteLength} bytes`);
    
    // If bbox is provided, draw the bounding box on the PDF
    if (bboxParam) {
      try {
        // Parse the bbox parameter
        const bbox = JSON.parse(bboxParam);
        console.log('Drawing bounding box:', { bbox, pageNumber });
        
        if (Array.isArray(bbox) && bbox.length === 4) {
          // Load the PDF document
          const pdfDoc = await PDFDocument.load(fileBuffer);
          console.log(`PDF loaded, pages: ${pdfDoc.getPageCount()}`);
          
          // Get the page (adjust for 0-based indexing)
          const pages = pdfDoc.getPages();
          
          // Ensure page number is within bounds
          if (pageNumber <= 0 || pageNumber > pages.length) {
            console.warn(`Page number ${pageNumber} is out of bounds (1-${pages.length}), using page 1`);
          }
          
          const pageIndex = Math.min(Math.max(0, pageNumber - 1), pages.length - 1);
          const page = pages[pageIndex];
          
          if (page) {
            // Get page dimensions
            const { width, height } = page.getSize();
            console.log(`Page dimensions: ${width}x${height}`);
            
            // Calculate the bounding box coordinates
            const x = bbox[0] * width;
            const y = height - (bbox[1] * height); // PDF coordinates start from bottom-left
            const boxWidth = (bbox[2] - bbox[0]) * width;
            const boxHeight = (bbox[3] - bbox[1]) * height;
            
            console.log('Calculated box coordinates:', { x, y, boxWidth, boxHeight });
            
            // Draw the bounding box with a thicker border and more visible color
            page.drawRectangle({
              x,
              y: y - boxHeight, // Adjust y for height
              width: boxWidth,
              height: boxHeight,
              borderColor: rgb(1, 0.5, 0), // Amber color
              borderWidth: 3, // Thicker border
              color: undefined, // Remove the fill by setting color to undefined
            });
            
            // Save the modified PDF
            const modifiedPdfBytes = await pdfDoc.save();
            console.log(`Modified PDF saved: ${modifiedPdfBytes.byteLength} bytes`);
            
            // Return the modified PDF
            return new NextResponse(modifiedPdfBytes, {
              headers: {
                'Content-Type': 'application/pdf',
                'Content-Disposition': `inline; filename="${filename}"`,
                'Access-Control-Allow-Origin': '*',
                'Cache-Control': 'no-cache, no-store, must-revalidate'
              }
            });
          } else {
            console.error(`Page ${pageIndex} not found in PDF`);
          }
        } else {
          console.error('Invalid bbox format:', bboxParam);
        }
      } catch (error) {
        console.error('Error modifying PDF:', error);
        // If there's an error modifying the PDF, fall back to returning the original
      }
    }
    
    // Return the original file if no bbox or if modification failed
    console.log('Returning original PDF file');
    return new NextResponse(fileBuffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `inline; filename="${filename}"`,
        'Access-Control-Allow-Origin': '*',
        'Cache-Control': 'public, max-age=3600'
      }
    });
  } catch (error) {
    console.error('PDF proxy error:', error);
    return NextResponse.json(
      { error: 'Failed to proxy PDF file', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
