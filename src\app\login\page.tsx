"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { useAuth } from "@/contexts/AuthContext";
import { useTheme } from "@/contexts/ThemeContext";
import { Toaster } from "@/components/ui/toaster";
import { useToast } from "@/components/ui/use-toast";
import EvicenterLogo from "@/icons/Evicenter.svg?url";
import EvicenterWhiteLogo from "@/icons/evicenter-white.svg?url";

export default function LoginPage() {
  const { signIn } = useAuth();
  const { theme } = useTheme();
  const { toast } = useToast();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    try {
      await signIn(email, password);
      toast({ title: "Welcome back!" });
      router.push("/");
    } catch (err: any) {
      setError(err.message);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <Image
        src={theme === "dark" ? EvicenterWhiteLogo : EvicenterLogo}
        alt="Evicenter Logo"
        width={200}
        height={80}
        className="mb-6"
      />
      <form
        onSubmit={handleSubmit}
        className="space-y-4 bg-white dark:bg-gray-900 p-6 rounded shadow-md w-full max-w-sm"
      >
        <h1 className="text-xl font-bold text-center">Sign In</h1>
        {error && <p className="text-red-500">{error}</p>}
        <input
          className="border p-2 block w-full"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="Email"
        />
        <input
          className="border p-2 block w-full"
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          placeholder="Password"
        />
        <button className="bg-blue-500 text-white px-4 py-2 w-full" type="submit">
          Sign In
        </button>
        <p className="text-sm text-center">
          Don't have an account?{' '}
          <a href="/signup" className="text-blue-600 underline">
            Sign Up
          </a>
        </p>
      </form>
      <Toaster />
    </div>
  );
}
