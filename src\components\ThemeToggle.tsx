"use client";

import { useTheme } from "@/contexts/ThemeContext";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Moon, Sun } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

export function ThemeToggle() {
  const { theme, toggleTheme } = useTheme();

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleTheme}
            className="hover:bg-transparent group"
          >
            {theme === 'light' ? (
              <Moon className="h-4 w-4 group-hover:text-amber-500" />
            ) : (
              <Sun className="h-4 w-4 group-hover:text-amber-500" />
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{theme === 'light' ? 'Switch to dark mode' : 'Switch to light mode'}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}