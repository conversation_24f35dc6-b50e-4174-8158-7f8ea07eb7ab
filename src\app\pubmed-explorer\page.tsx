

"use client";

import React, { useState, useMemo, useEffect, useRef, Suspense } from "react";
import {
  Search,
  Save,
  Download,
  Clipboard,
  X,
  Bookmark,
  BookmarkCheck,
  Plus,
  Sparkles,
  Lightbulb,
  Loader2,
  Eye,
  FileText,
  FilePlus2,
  FileCheck2,
  ExternalLink,
  ChevronDown,
  BarChart3,
  LineChart as LineChartIcon,
  Check,
  Info,
  Filter,
  EyeOff,
} from "lucide-react";
import axios from "axios";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useLocalStorage } from "@/hooks/use-local-storage";
import { useToast } from "@/components/ui/use-toast";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { toTitleCase } from "@/utils/textUtils";
import { Checkbox } from "@/components/ui/checkbox";
import { Skeleton } from "@/components/ui/skeleton";
import { SubpageHeader } from "@/components/SubpageHeader";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  LineChart,
  Line,
} from "recharts";
import SelectionPopup from "@/components/SelectionPopup";
import { ClipboardProvider, useClipboard } from "@/contexts/ClipboardContext";
import { ClipboardPanel } from "@/components/Clipboard";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import PagePdfViewer from "@/components/PagePdfViewer";
import { PDFDocument } from "pdf-lib";
import { linkClinicalTrials } from "@/lib/linkClinicalTrials";

interface PubMedResult {
  id: string;
  title: string;
  pubdate?: string;
  journal?: string;
  authors?: string;
  abstract?: string;
  publicationTypes?: string[];
  meshTerms?: string[];
}

interface SavedQuery {
  query: string;
  startYear?: string;
  endYear?: string;
  sort: string;
  author?: string;
  journal?: string;
}

interface JournalDataItem {
  journal: string;
  value: number;
}

interface KeywordTreeDataItem {
  name: string;
  size: number;
  fill: string;
}

interface OpenAlexWork {
  id: string;
  display_name?: string;
  doi?: string;
  cited_by_count: number;
  cited_by_api_url?: string;
  referenced_works?: string[];
  related_works?: string[];
  field_citation_ratio?: number;
  cited_by_percentile_year?: number;
  open_access?: {
    is_oa: boolean;
    oa_status: string;
  };
  concepts?: { display_name: string }[];
  counts_by_year?: { year: number; cited_by_count: number }[];
  authorships?: {
    author: { id: string; display_name: string };
    institutions: { display_name: string }[];
  }[];
  citingWorks?: { id: string; display_name: string }[];
  referencedWorkDetails?: { id: string; display_name: string }[];
}

interface UnpaywallData {
  is_oa: boolean;
  oa_status?: string;
  pdfUrl: string | null;
}

interface SemanticScholarData {
  citationCount?: number;
  influentialCitationCount?: number;
  fieldsOfStudy?: string[];
  tldr?: string;
  url?: string;
  pdfUrl?: string | null;
}

interface SearchItem {
  id: number;
  query: string;
  submittedQuery: string;
  author: string;
  journal: string;
  startYear: string;
  endYear: string;
  sort: string;
  results: PubMedResult[];
  page: number;
  totalCount: number;
  isSearching: boolean;
  hasRun: boolean;
}

const ResultCardSkeleton = () => (
  <Card className="animate-pulse">
    <CardHeader>
      <Skeleton className="h-4 w-3/4 mb-2" />
      <Skeleton className="h-3 w-1/2" />
    </CardHeader>
    <CardContent>
      <div className="space-y-2">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-5/6" />
      </div>
    </CardContent>
  </Card>
);

function PubMedExplorerContent({ bringPdfToFront, pdfOnTop }: { bringPdfToFront: () => void; pdfOnTop: boolean }) {
  // Add this to prevent hydration mismatches
  const [mounted, setMounted] = useState(false);
  const { addNote } = useClipboard();
  const [popup, setPopup] = useState<
    { text: string; x: number; y: number; articleUrl?: string } | null
  >(null);
  const [reference, setReference] = useState("");
  
  // Search items state - allow up to five simultaneous searches
  const [searches, setSearches] = useState<SearchItem[]>([
    {
      id: Date.now(),
      query: "",
      submittedQuery: "",
      author: "",
      journal: "",
      startYear: "",
      endYear: "",
      sort: "relevance",
      results: [],
      page: 1,
      totalCount: 0,
      isSearching: false,
      hasRun: false,
    },
  ]);
  const [savedQueries, setSavedQueries] = useLocalStorage<SavedQuery[]>(
    "pubmedSavedQueries",
    [],
  );
  const [bookmarks, setBookmarks] = useLocalStorage<PubMedResult[]>(
    "pubmedBookmarks",
    [],
  );
  const [expandedIds, setExpandedIds] = useState<string[]>([]);
  const [compareIds, setCompareIds] = useState<string[]>([]);
  const [expandedCompareIds, setExpandedCompareIds] = useState<string[]>([]);
  // Remove viewTypes state since we're using bar chart by default
  const [keywordView, setKeywordView] = useState<"badges" | "treemap">("badges");
  const [copiedId, setCopiedId] = useState<string | null>(null);
  const [journalCounts, setJournalCounts] = useState<number[]>([5]);
  const [authorCounts, setAuthorCounts] = useState<number[]>([5]);
  const [openAlexData, setOpenAlexData] = useState<Record<string, OpenAlexWork>>({});
  const [bookmarkSort, setBookmarkSort] = useState<'added' | 'title' | 'date'>('added');
  const [selectedBookmarkIds, setSelectedBookmarkIds] = useState<string[]>([]);
  const sortedBookmarks = useMemo(() => {
    const b = [...bookmarks];
    if (bookmarkSort === 'title') {
      b.sort((a, b2) => a.title.localeCompare(b2.title));
    } else if (bookmarkSort === 'date') {
      b.sort((a, b2) => (b2.pubdate || '').localeCompare(a.pubdate || ''));
    }
    return b;
  }, [bookmarks, bookmarkSort]);
  const [hoverTooltip, setHoverTooltip] = useState<
    | { label: string; articles: PubMedResult[]; x: number; y: number }
    | null
  >(null);
  const [searchQueue, setSearchQueue] = useState<{ idx: number; query: string }[]>([]);
  const queueProcessing = useRef(false);
  const hideTooltipTimeout = useRef<NodeJS.Timeout | null>(null);
  const { toast } = useToast();

  // Add this state to store PDF links
  const [pdfLinks, setPdfLinks] = useState<Record<string, string | null>>({});
  const [pubmedLinks, setPubmedLinks] = useState<Record<string, { pdf: string | null; link: string | null }>>({});
  const [unpaywallData, setUnpaywallData] = useState<Record<string, UnpaywallData>>({});
  const [semanticData, setSemanticData] = useState<Record<string, SemanticScholarData>>({});

  const [pubTypeFilters, setPubTypeFilters] = useState<string[]>([]);
  const [keywordFilters, setKeywordFilters] = useState<string[]>([]);
  const [pubTypeOp, setPubTypeOp] = useState<'AND' | 'OR'>('OR');
  const [keywordOp, setKeywordOp] = useState<'AND' | 'OR'>('OR');
  const [filterLoading, setFilterLoading] = useState(false);
  const [showFilters, setShowFilters] = useState(true);

  // --- ChatGPT enhanced features ---
  const [articleSummaries, setArticleSummaries] = useState<Record<string, string>>({});
  const [plainSummaries, setPlainSummaries] = useState<Record<string, string>>({});
  const [searchSummaries, setSearchSummaries] = useState<Record<number, string>>({});
  const [querySuggestions, setQuerySuggestions] = useState<Record<number, string[]>>({});
  const [researchQuestions, setResearchQuestions] = useState<Record<number, string[]>>({});
  const [bookmarkSummary, setBookmarkSummary] = useState<string>("");
  const [comparisonSummary, setComparisonSummary] = useState<string>("");
  const [loading, setLoading] = useState<Record<string, boolean>>({});
  const [contextDocs, setContextDocs] = useState<{ id: string; title: string; text: string }[]>([]);
  const [contextLoading, setContextLoading] = useState<string[]>([]);
  const [pdfModal, setPdfModal] = useState<{ url: string; title: string; articleUrl?: string } | null>(null);
  const [pageInputs, setPageInputs] = useState<Record<string, number>>({});
  const [viewablePdfLinks, setViewablePdfLinks] = useState<Record<string, string | null>>({});
  const [verifiedPdfUrls, setVerifiedPdfUrls] = useState<Record<string, boolean>>({});
  const comparisonRef = useRef<HTMLDivElement | null>(null);
  const sentinelRefs = useRef<(HTMLDivElement | null)[]>([]);

  useEffect(() => {
    if (pdfModal) bringPdfToFront();
  }, [pdfModal, bringPdfToFront]);


  const setLoad = (key: string, val: boolean) =>
    setLoading((prev) => ({ ...prev, [key]: val }));

  const fetchChat = async (prompt: string) => {
    const res = await fetch("/api/chat", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ messages: [{ role: "user", content: prompt }] }),
    });
    if (!res.ok) throw new Error("ChatGPT request failed");
    const data = await res.json();
    return data.content as string;
  };

  const fetchUnpaywall = async (doi: string, pmid: string) => {
    try {
      const res = await fetch(`/api/unpaywall?doi=${encodeURIComponent(doi)}`);
      if (!res.ok) throw new Error('Unpaywall request failed');
      const data = await res.json();
      const pdfUrl = data?.best_oa_location?.url_for_pdf || data?.best_oa_location?.url || null;
      setUnpaywallData(prev => ({ ...prev, [pmid]: { is_oa: data.is_oa, oa_status: data.oa_status, pdfUrl } }));
    } catch (err) {
      console.error('Unpaywall fetch error', err);
    }
  };

  const fetchSemanticScholar = async (doi: string, pmid: string, title?: string) => {
    try {
      console.log(`Fetching Semantic Scholar for PMID ${pmid}, DOI: ${doi}`);
      let url = `/api/semantic-scholar?doi=${encodeURIComponent(doi)}`;
      let res = await fetch(url);
      
      // If DOI search fails and we have a title, try title search
      if (!res.ok && title) {
        console.log(`DOI search failed, trying title search for: ${title}`);
        url = `/api/semantic-scholar?title=${encodeURIComponent(title)}`;
        res = await fetch(url);
      }
      
      if (!res.ok) throw new Error('Semantic Scholar request failed');
      const data = await res.json();
      console.log(`Semantic Scholar data for ${pmid}:`, data);
      
      // Only set data if we got meaningful results
      if (data.citationCount !== null || data.url) {
        setSemanticData(prev => ({
          ...prev,
          [pmid]: {
            citationCount: data.citationCount ?? 0,
            influentialCitationCount: data.influentialCitationCount ?? 0,
            fieldsOfStudy: data.fieldsOfStudy,
            tldr: data.tldr?.text,
            url: data.url,
            pdfUrl: data.openAccessPdf?.url || null,
          },
        }));
      }
    } catch (err) {
      console.error('Semantic Scholar fetch error', err);
    }
  };

  const fetchOpenAlex = async (pmid: string) => {
    try {
      const res = await fetch(`/api/openalex-work?id=${pmid}&type=pmid`);
      if (!res.ok) throw new Error("OpenAlex request failed");
      const data: OpenAlexWork = await res.json();
      const normalized = {
        ...data,
        id: data.id.replace('https://api.openalex.org/works/', 'https://openalex.org/'),
      } as OpenAlexWork;
      
      // Add null checks for referenced_works
      const refs = normalized.referenced_works || [];
      if (refs.length > 0) {
        const ids = refs.slice(0, 5).join('|');
        const refRes = await fetch(`/api/openalex-works?ids=${ids}`);
        if (refRes.ok) {
          const refData = await refRes.json();
          // Add null check for refData.results
          normalized.referencedWorkDetails = refData.results?.map((r: any) => ({ 
            id: r.id.replace('https://api.openalex.org/works/', 'https://openalex.org/'), 
            display_name: r.display_name 
          })) || [];
        }
      }
      
      // Add null check for normalized.id
      if (normalized.id) {
        const citeRes = await fetch(`/api/openalex-cited-by?id=${normalized.id.replace('https://openalex.org/', '')}&per_page=5`);
        if (citeRes.ok) {
          const citeData = await citeRes.json();
          // Add null check for citeData.results
          normalized.citingWorks = citeData.results?.map((r: any) => ({ 
            id: r.id.replace('https://api.openalex.org/works/', 'https://openalex.org/'), 
            display_name: r.display_name 
          })) || [];
        }
      }

      setOpenAlexData((prev) => ({ ...prev, [pmid]: normalized }));
      const doi = data.doi?.replace(/^https?:\/\/(dx\.)?doi.org\//, '');
      if (doi) {
        fetchUnpaywall(doi, pmid);
        fetchSemanticScholar(doi, pmid);
      }
    } catch (err) {
      console.error("OpenAlex fetch error", err);
    }
  };

  const getOpenAlexLink = (
    pmid: string,
    filter: 'cited_by' | 'cites' | 'related_to'
  ) => {
    const id = openAlexData[pmid]?.id?.replace('https://openalex.org/', '');
    if (!id) return '#';
    return `https://openalex.org/works?page=1&filter=${filter}:${id.toLowerCase()}`;
  };

  const parseBullets = (text: string) =>
    text
      .split(/\n+/)
      .map((l) => l.replace(/^[^\w]+|^[0-9]+\.?\s+/, "").trim())
      .filter(Boolean);

  const addContextDoc = async (pmid: string, title: string) => {
    if (contextDocs.some((d) => d.id === pmid) || contextLoading.includes(pmid)) return;
    setContextLoading((p) => [...p, pmid]);
    try {
      const res = await fetch(`/api/pmc-to-markdown?pmid=${pmid}`);
      if (res.ok) {
        const data = await res.json();
        if (data.text) {
          setContextDocs((prev) => [...prev, { id: pmid, title, text: data.text }]);
          console.log(`Context added for PMID ${pmid}`);
        }
      } else {
        console.error('Failed to fetch PMC markdown', res.status);
      }
    } catch (err) {
      console.error('PMC markdown error', err);
    } finally {
      setContextLoading((p) => p.filter((id) => id !== pmid));
    }
  };

  const removeContextDoc = (pmid: string) => {
    setContextDocs((prev) => prev.filter((d) => d.id !== pmid));
  };

  // Set mounted to true after initial render
  useEffect(() => {
    setMounted(true);
  }, []);

  const saveSelection = () => {
    if (!popup) return;
    // Use the article URL if available, otherwise fall back to the page URL
    const sourceUrl = popup.articleUrl || window.location.href;
    addNote(popup.text, sourceUrl, undefined, reference);
    window.getSelection()?.removeAllRanges();
    setPopup(null);
    setReference('');
  };

  const closePopup = () => {
    setPopup(null);
    setReference('');
  };

  useEffect(() => {
    const handler = (e: MouseEvent) => {
      const sel = window.getSelection();
      const text = sel?.toString().trim();
      if (text) {
        e.preventDefault();
        setReference('');
        
        // Find the parent Card element to get the article ID
        let element = e.target as HTMLElement;
        let articleUrl: string | undefined;
        while (element && element !== document.body) {
          if (element.hasAttribute('data-pmid')) {
            const pmid = element.getAttribute('data-pmid');
            if (pmid) {
              articleUrl = `https://pubmed.ncbi.nlm.nih.gov/${pmid}/`;
            }
            break;
          }
          element = element.parentElement as HTMLElement;
        }
        
        setPopup({ text, x: e.clientX, y: e.clientY, articleUrl });
      }
    };
    document.addEventListener('contextmenu', handler);
    return () => document.removeEventListener('contextmenu', handler);
  }, [addNote]);

  useEffect(() => {
    if (queueProcessing.current || searchQueue.length === 0) return;
    const { idx, query } = searchQueue[0];
    queueProcessing.current = true;
    search(idx, 1, query).finally(() => {
      setSearchQueue((q) => q.slice(1));
      queueProcessing.current = false;
    });
  }, [searchQueue]);

  const decodeHtmlEntities = (text: string) => {
    const textarea = document.createElement('textarea');
    textarea.innerHTML = text;
    return textarea.value;
  };

  const highlightText = (text: string, q: string) => {
    // Decode HTML entities first
    const decodedText = decodeHtmlEntities(text);
    
    const words = q
      .split(/\s+/)
      .map((w) => w.replace(/[^\w]/g, ""))
      .filter(Boolean);
    if (words.length === 0) return decodedText;
    const pattern = new RegExp(`(${words.join("|")})`, "gi");
    return decodedText.split(pattern).map((part, i) =>
      pattern.test(part) ? (
        <mark key={i} className="bg-amber-200 dark:bg-amber-700">
          {part}
        </mark>
      ) : (
        part
      ),
    );
  };

  const highlightTextWithLinks = (text: string, q: string) => {
    // Decode HTML entities first
    const decodedText = decodeHtmlEntities(text);
    
    const words = q
      .split(/\s+/)
      .map((w) => w.replace(/[^\w]/g, ""))
      .filter(Boolean);
    if (words.length === 0) return linkClinicalTrials(decodedText);
    const splitPattern = new RegExp(`(${words.join("|")})`, "gi");
    const testPattern = new RegExp(`^(${words.join("|")})$`, "i");
    return decodedText.split(splitPattern).map((part, i) =>
      testPattern.test(part) ? (
        <mark key={i} className="bg-amber-200 dark:bg-amber-700">
          {linkClinicalTrials(part) as React.ReactNode}
        </mark>
      ) : (
        <React.Fragment key={i}>{linkClinicalTrials(part)}</React.Fragment>
      ),
    );
  };

  const extractNctIds = (text: string) => {
    const matches = text.match(/NCT\d{8}/gi);
    return matches ? Array.from(new Set(matches.map((m) => m.toUpperCase()))) : [];
  };

  const showHoverTooltip = (
    label: string,
    articles: PubMedResult[],
    e: any,
  ) => {
    if (hideTooltipTimeout.current) {
      clearTimeout(hideTooltipTimeout.current);
      hideTooltipTimeout.current = null;
    }
    setHoverTooltip({ label, articles, x: e.clientX, y: e.clientY });
  };

  const scheduleHideTooltip = () => {
    hideTooltipTimeout.current = setTimeout(() => setHoverTooltip(null), 500);
  };

  const cancelHideTooltip = () => {
    if (hideTooltipTimeout.current) {
      clearTimeout(hideTooltipTimeout.current);
      hideTooltipTimeout.current = null;
    }
  };

  // ChatGPT feature helpers
  const summarizeArticle = async (item: PubMedResult) => {
    if (!item.abstract) return;
    setLoad(`article-${item.id}`, true);
    const prompt = `Summarize the following PubMed abstract in three short bullet points using the \u2022 symbol. Do not number the points.\n\n${item.abstract}`;
    try {
      const summary = await fetchChat(prompt);
      setArticleSummaries((prev) => ({ ...prev, [item.id]: summary }));
    } finally {
      setLoad(`article-${item.id}`, false);
    }
  };


  const plainLanguageSummary = async (item: PubMedResult) => {
    if (!item.abstract) return;
    setLoad(`plain-${item.id}`, true);
    const prompt = `Explain the following PubMed abstract in plain language using three \u2022 bullet points without numbers.\n\n${item.abstract}`;
    try {
      const summary = await fetchChat(prompt);
      setPlainSummaries((prev) => ({ ...prev, [item.id]: summary }));
    } finally {
      setLoad(`plain-${item.id}`, false);
    }
  };


  const summarizeSearchResults = async (idx: number) => {
    const articles = searches[idx]?.results || [];
    if (articles.length === 0) return;
    setLoad(`search-${idx}`, true);
    const content = articles
      .slice(0, 10)
      .map((a) => `Title: ${a.title}\n${a.abstract}`)
      .join("\n\n");
    const prompt = `Provide a high level summary of these PubMed articles in five \u2022 bullet points. Do not use numbers.\n\n${content}`;
    try {
      const summary = await fetchChat(prompt);
      setSearchSummaries((prev) => ({ ...prev, [idx]: summary }));
    } finally {
      setLoad(`search-${idx}`, false);
    }
  };

  const suggestQueries = async (idx: number) => {
    const q = searches[idx]?.query || "";
    if (!q.trim()) return;
    setLoad(`query-${idx}`, true);
    const prompt = `Suggest three PubMed search queries related to: ${q}. Present each suggestion on a new line without any bullet points, numbers, or other markers.`;
    try {
      const resp = await fetchChat(prompt);
      setQuerySuggestions((prev) => ({ ...prev, [idx]: resp.split(/\n+/).filter(Boolean) }));
    } finally {
      setLoad(`query-${idx}`, false);
    }
  };

  const suggestResearchQuestions = async (idx: number) => {
    const articles = searches[idx]?.results || [];
    if (articles.length === 0) return;
    setLoad(`research-${idx}`, true);
    const text = articles.slice(0, 5).map((a) => a.title).join("; ");
    const prompt = `Based on these PubMed articles: ${text}, suggest three follow up research questions. Present each question on a new line without any bullet points, numbers, or other markers.`;
    try {
      const resp = await fetchChat(prompt);
      setResearchQuestions((prev) => ({ ...prev, [idx]: resp.split(/\n+/).filter(Boolean) }));
    } finally {
      setLoad(`research-${idx}`, false);
    }
  };

  const summarizeBookmarks = async () => {
    if (bookmarks.length === 0) return;
    setLoad("bookmark", true);
    const text = bookmarks
      .slice(0, 10)
      .map((b) => `Title: ${b.title}\n${b.abstract}`)
      .join("\n\n");
    const prompt = `Summarize these bookmarked PubMed articles in five \u2022 bullet points without numbers.\n\n${text}`;
    try {
      const resp = await fetchChat(prompt);
      setBookmarkSummary(resp);
    } finally {
      setLoad("bookmark", false);
    }
  };

  const summarizeComparison = async () => {
    if (compareItems.length < 2) return;
    setLoad("comparison", true);
    const text = compareItems
      .slice(0, 5)
      .map((c) => `Title: ${c.title}\n${c.abstract}`)
      .join("\n\n");
    const docContext = contextDocs
      .map((d) => `SOURCE: ${d.title}\n${d.text}`)
      .join("\n\n");
    const prompt =
      `Compare these PubMed articles and summarize their key similarities and differences in up to five \u2022 bullet points without numbers.\n\n${text}` +
      (docContext ? `\n\nContext from documents:\n${docContext}` : "");
    try {
      const resp = await fetchChat(prompt);
      setComparisonSummary(resp);
    } finally {
      setLoad("comparison", false);
    }
  };

  const renderAuthorTick = ({ x, y, payload }: any) => (
    <text x={x} y={y} textAnchor="end" dominantBaseline="middle" className="whitespace-nowrap">
      {payload.value}
    </text>
  );

  const allResults = useMemo(() => searches.flatMap((s) => s.results), [searches]);

  const hasResults = allResults.length > 0;

  const allPubTypes = useMemo(() => {
    const set = new Set<string>();
    allResults.forEach((r) => (r.publicationTypes || []).forEach((t) => set.add(t)));
    return Array.from(set).sort();
  }, [allResults]);

  const allKeywords = useMemo(() => {
    const set = new Set<string>();
    allResults.forEach((r) => (r.meshTerms || []).forEach((m) => set.add(m)));
    return Array.from(set).sort();
  }, [allResults]);

  useEffect(() => {
    allResults.forEach((r) => {
      if (!openAlexData[r.id]) {
        fetchOpenAlex(r.id);
      }
    });
  }, [allResults]);

  // Chart data will be calculated individually for each search

  const getYearData = (results: PubMedResult[]) => {
    const counts: Record<string, number> = {};
    results.forEach((r) => {
      const match = r.pubdate?.match(/\d{4}/);
      if (match) {
        counts[match[0]] = (counts[match[0]] || 0) + 1;
      }
    });
    return Object.entries(counts)
      .map(([year, count]) => ({ year, count }))
      .sort((a, b) => parseInt(a.year) - parseInt(b.year));
  };

  const getJournalData = (results: PubMedResult[]) => {
    const counts: Record<string, number> = {};
    results.forEach((r) => {
      if (r.journal) counts[r.journal] = (counts[r.journal] || 0) + 1;
    });
    return Object.entries(counts)
      .map(([journal, value]) => ({ journal, value }))
      .sort((a, b) => b.value - a.value);
  };

  const getAuthorData = (results: PubMedResult[]) => {
    const counts: Record<string, number> = {};
    results.forEach((r) => {
      if (r.authors) {
        r.authors.split(",").forEach((a) => {
          const name = a.trim();
          if (name) counts[name] = (counts[name] || 0) + 1;
        });
      }
    });
    return Object.entries(counts)
      .map(([author, value]) => ({ author, value }))
      .sort((a, b) => b.value - a.value);
  };

  const getArticlesByYear = (results: PubMedResult[], year: string) =>
    results.filter((r) => r.pubdate && r.pubdate.includes(year));

  const getArticlesByJournal = (results: PubMedResult[], journal: string) =>
    results.filter((r) => r.journal === journal);

  const getArticlesByAuthor = (results: PubMedResult[], author: string) =>
    results.filter(
      (r) =>
        r.authors &&
        r.authors
          .split(",")
          .map((a) => a.trim())
          .includes(author),
    );

  const applyResultFilters = (results: PubMedResult[]) =>
    results.filter((r) => {
      const pubTypes = r.publicationTypes || [];
      const mesh = r.meshTerms || [];
      const pubMatch =
        pubTypeFilters.length === 0 ||
        (pubTypeOp === 'AND'
          ? pubTypeFilters.every((pt) => pubTypes.includes(pt))
          : pubTypes.some((pt) => pubTypeFilters.includes(pt)));
      const keyMatch =
        keywordFilters.length === 0 ||
        (keywordOp === 'AND'
          ? keywordFilters.every((kw) => mesh.includes(kw))
          : mesh.some((kw) => keywordFilters.includes(kw)));
      return pubMatch && keyMatch;
    });

  const compareItems = useMemo(
    () => {
      if (!mounted) return [];
      return allResults.filter((r) => compareIds.includes(r.id));
    },
    [allResults, compareIds, mounted]
  );

  const saveCurrentQuery = (idx: number) => {
    const s = searches[idx];
    if (!s.query.trim()) {
      toast({
        title: "Search query required",
        description: "Enter a query before saving",
        variant: "destructive",
      });
      return;
    }
    const newQuery: SavedQuery = {
      query: s.query,
      startYear: s.startYear || undefined,
      endYear: s.endYear || undefined,
      sort: s.sort,
      author: s.author || undefined,
      journal: s.journal || undefined,
    };
    const exists = savedQueries.some(
      (q) =>
        q.query === newQuery.query &&
        (q.startYear || "") === (newQuery.startYear || "") &&
        (q.endYear || "") === (newQuery.endYear || "") &&
        q.sort === newQuery.sort &&
        (q.author || "") === (newQuery.author || "") &&
        (q.journal || "") === (newQuery.journal || ""),
    );
    if (exists) return;
    setSavedQueries([...savedQueries, newQuery]);
    toast({ title: "Search saved" });
  };

  const loadSavedQuery = (sq: SavedQuery) => {
    const emptyIndex = searches.findIndex((s) => !s.query.trim());
    
    if (emptyIndex !== -1) {
      // For existing empty search slot, update and then search
      const updatedSearches = [...searches];
      updatedSearches[emptyIndex] = {
        ...updatedSearches[emptyIndex],
        query: sq.query,
        submittedQuery: "",
        author: sq.author || "",
        journal: sq.journal || "",
        startYear: sq.startYear || "",
        endYear: sq.endYear || "",
        sort: sq.sort || "relevance",
        page: 1,
        isSearching: true
      };
      
      // Update state with the modified array
      setSearches(updatedSearches);
      
      // Queue the search once the state update is applied
      setSearchQueue(q => [...q, { idx: emptyIndex, query: sq.query }]);
    } else if (searches.length < 3) {
      // For new search, create the search object first
      const newSearch = {
        id: Date.now(),
        query: sq.query,
        submittedQuery: "",
        author: sq.author || "",
        journal: sq.journal || "",
        startYear: sq.startYear || "",
        endYear: sq.endYear || "",
        sort: sq.sort || "relevance",
        results: [],
        page: 1,
        totalCount: 0,
        isSearching: true,
        hasRun: false,
      };
      
      // Calculate the new index
      const newSearchIndex = searches.length;
      
      // Update state with the new search added
      setSearches(prev => [...prev, newSearch]);
      setJournalCounts(prev => [...prev, 5]);
      setAuthorCounts(prev => [...prev, 5]);
      
      // Queue the new search to run after the state update
      setSearchQueue(q => [...q, { idx: newSearchIndex, query: sq.query }]);
    } else {
      // If already at 3 searches, show a toast notification
      toast({
        title: "Maximum searches reached",
        description: "Please remove a search before adding a new one",
        variant: "destructive",
      });
    }
  };

  const removeSavedQuery = (index: number) => {
    setSavedQueries(savedQueries.filter((_, i) => i !== index));
  };

  const exportCsv = () => {
    if (allResults.length === 0) return;
    const header = [
      "PMID",
      "Title",
      "Publication Date",
      "Journal",
      "Authors",
      "Abstract",
    ];
    const rows = allResults.map((r) =>
      [
        r.id,
        r.title.replace(/\"/g, '""'),
        r.pubdate || "",
        r.journal || "",
        r.authors ? r.authors.replace(/\"/g, '""') : "",
        r.abstract ? r.abstract.replace(/\"/g, '""') : "",
      ]
        .map((v) => `"${v}"`)
        .join(","),
    );
    const csvContent = [header.join(","), ...rows].join("\n");
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `pubmed-results-${Date.now()}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const exportSearchCsv = (idx: number) => {
    const results = searches[idx]?.results || [];
    if (results.length === 0) return;
    const header = [
      "PMID",
      "Title",
      "Publication Date",
      "Journal",
      "Authors",
      "Abstract",
    ];
    const rows = results.map((r) =>
      [
        r.id,
        r.title.replace(/\"/g, '""'),
        r.pubdate || "",
        r.journal || "",
        r.authors ? r.authors.replace(/\"/g, '""') : "",
        r.abstract ? r.abstract.replace(/\"/g, '""') : "",
      ]
        .map((v) => `"${v}"`)
        .join(","),
    );
    const csvContent = [header.join(","), ...rows].join("\n");
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `pubmed-results-${idx + 1}-${Date.now()}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const exportComparisonCsv = () => {
    if (compareItems.length === 0) return;
    const header = ["PMID", "Title", "Publication Date", "Journal", "Authors", "Abstract"];
    const rows = compareItems.map((r) =>
      [
        r.id,
        r.title.replace(/\"/g, '""'),
        r.pubdate || "",
        r.journal || "",
        r.authors ? r.authors.replace(/\"/g, '""') : "",
        r.abstract ? r.abstract.replace(/\"/g, '""') : "",
      ]
        .map((v) => `"${v}"`)
        .join(","),
    );
    const csvContent = [header.join(","), ...rows].join("\n");
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `pubmed-comparison-${Date.now()}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const copyCitation = (item: PubMedResult, id: string) => {
    const citation = `${item.authors ? item.authors + ". " : ""}${item.title}. ${
      item.journal ? toTitleCase(item.journal) + ". " : ""
    }${item.pubdate ? item.pubdate + ". " : ""}PMID: ${item.id}.`;
    navigator.clipboard.writeText(citation).then(() => {
      setCopiedId(id);
      toast({ title: "Citation copied" });
      setTimeout(() => setCopiedId(null), 2000);
    });
  };

  const toggleBookmark = (item: PubMedResult) => {
    const exists = bookmarks.find((p) => p.id === item.id);
    if (exists) {
      setBookmarks(bookmarks.filter((p) => p.id !== item.id));
      setSelectedBookmarkIds((prev) => prev.filter((id) => id !== item.id));
    } else {
      setBookmarks([...bookmarks, item]);
    }
  };

  const toggleCompare = (id: string) => {
    if (compareIds.includes(id)) {
      setCompareIds(compareIds.filter((c) => c !== id));
    } else {
      setCompareIds([...compareIds, id]);
    }
  };

  const removeSearch = (id: number) => {
    setSearches((prev) => {
      const idx = prev.findIndex((s) => s.id === id);
      if (idx === -1) return prev;
      setJournalCounts((c) => c.filter((_, i) => i !== idx));
      setAuthorCounts((c) => c.filter((_, i) => i !== idx));
      return prev.filter((s) => s.id !== id);
    });
  };

  const clearSearchResults = (id: number) => {
    const idx = searches.findIndex((s) => s.id === id);
    if (idx === -1) return;
    const ids = searches[idx].results.map((r) => r.id);

    setSearches((prev) =>
      prev.map((s, i) =>
        i === idx
          ? {
              ...s,
              query: "",
              submittedQuery: "",
              author: "",
              journal: "",
              startYear: "",
              endYear: "",
              sort: "relevance",
              results: [],
              page: 1,
              totalCount: 0,
              isSearching: false,
              hasRun: false,
            }
          : s,
      ),
    );

    setJournalCounts((c) => c.map((v, i) => (i === idx ? 5 : v)));
    setAuthorCounts((c) => c.map((v, i) => (i === idx ? 5 : v)));

    setSearchSummaries((prev) => {
      const copy = { ...prev };
      delete copy[idx];
      return copy;
    });
    setQuerySuggestions((prev) => {
      const copy = { ...prev };
      delete copy[idx];
      return copy;
    });
    setResearchQuestions((prev) => {
      const copy = { ...prev };
      delete copy[idx];
      return copy;
    });

    setCompareIds((prev) => prev.filter((cid) => !ids.includes(cid)));
    setArticleSummaries((prev) => {
      const copy = { ...prev };
      ids.forEach((cid) => delete copy[cid]);
      return copy;
    });
    setPlainSummaries((prev) => {
      const copy = { ...prev };
      ids.forEach((cid) => delete copy[cid]);
      return copy;
    });
    setOpenAlexData((prev) => {
      const copy: Record<string, OpenAlexWork> = { ...prev };
      ids.forEach((cid) => delete copy[cid]);
      return copy;
    });
    setPdfLinks((prev) => {
      const copy = { ...prev };
      ids.forEach((cid) => delete copy[cid]);
      return copy;
    });
    setPubmedLinks((prev) => {
      const copy = { ...prev };
      ids.forEach((cid) => delete copy[cid]);
      return copy;
    });
  };

  const toggleAbstract = (id: string) => {
    if (expandedIds.includes(id)) {
      setExpandedIds(expandedIds.filter((i) => i !== id));
    } else {
      setExpandedIds([...expandedIds, id]);
    }
  };

  const toggleCompareAbstract = (id: string) => {
    if (expandedCompareIds.includes(id)) {
      setExpandedCompareIds(expandedCompareIds.filter((i) => i !== id));
    } else {
      setExpandedCompareIds([...expandedCompareIds, id]);
    }
  };

  const applyFilterDelay = () => {
    setFilterLoading(true);
    setTimeout(() => setFilterLoading(false), 300);
  };

  const addPubTypeFilter = (type: string) => {
    setPubTypeFilters((prev) => (prev.includes(type) ? prev : [...prev, type]));
    applyFilterDelay();
  };

  const togglePubTypeFilter = (type: string) => {
    setPubTypeFilters((prev) =>
      prev.includes(type) ? prev.filter((t) => t !== type) : [...prev, type]
    );
    applyFilterDelay();
  };

  const addKeywordFilter = (kw: string) => {
    setKeywordFilters((prev) => (prev.includes(kw) ? prev : [...prev, kw]));
    applyFilterDelay();
  };

  const toggleKeywordFilter = (kw: string) => {
    setKeywordFilters((prev) =>
      prev.includes(kw) ? prev.filter((k) => k !== kw) : [...prev, kw]
    );
    applyFilterDelay();
  };

  const changePubTypeOp = (op: 'AND' | 'OR') => {
    setPubTypeOp(op);
    applyFilterDelay();
  };

  const changeKeywordOp = (op: 'AND' | 'OR') => {
    setKeywordOp(op);
    applyFilterDelay();
  };

  const removePubTypeFilter = (type: string) => {
    setPubTypeFilters((prev) => prev.filter((t) => t !== type));
    applyFilterDelay();
  };

  const removeKeywordFilter = (kw: string) => {
    setKeywordFilters((prev) => prev.filter((k) => k !== kw));
    applyFilterDelay();
  };

  const clearResultFilters = () => {
    setPubTypeFilters([]);
    setKeywordFilters([]);
    applyFilterDelay();
  };

  const clearFilters = (idx: number) => {
    setSearches((prev) =>
      prev.map((s, i) =>
        i === idx
          ? { ...s, author: "", journal: "", startYear: "", endYear: "", sort: "relevance" }
          : s,
      ),
    );
  };

  const clearComparison = () => {
    setCompareIds([]);
  };

  const scrollToComparison = () => {
    if (comparisonRef.current) {
      comparisonRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  };

  const clearBookmarks = () => {
    setBookmarks([]);
  };

  const toggleBookmarkSelection = (id: string) => {
    setSelectedBookmarkIds((prev) =>
      prev.includes(id) ? prev.filter((b) => b !== id) : [...prev, id],
    );
  };

  const toggleSelectAllBookmarks = () => {
    if (selectedBookmarkIds.length !== bookmarks.length) {
      setSelectedBookmarkIds(bookmarks.map((b) => b.id));
    } else {
      setSelectedBookmarkIds([]);
    }
  };

  const exportBookmarksCsv = () => {
    const selected = bookmarks.filter((b) => selectedBookmarkIds.includes(b.id));
    if (selected.length === 0) return;
    const header = ["PMID", "Title", "Publication Date", "Journal", "Authors", "Abstract"];
    const rows = selected.map((r) =>
      [
        r.id,
        r.title.replace(/\"/g, '""'),
        r.pubdate || "",
        r.journal || "",
        r.authors ? r.authors.replace(/\"/g, '""') : "",
        r.abstract ? r.abstract.replace(/\"/g, '""') : "",
      ]
        .map((v) => `"${v}"`)
        .join(","),
    );
    const csvContent = [header.join(","), ...rows].join("\n");
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `pubmed-bookmarks-${Date.now()}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const exportBookmarksWord = () => {
    const selected = bookmarks.filter(b => selectedBookmarkIds.includes(b.id));
    if (selected.length === 0) return;
    const html = `<!DOCTYPE html><html><head><meta charset="utf-8"></head><body>${selected
      .map(b => `<p><a href="https://pubmed.ncbi.nlm.nih.gov/${b.id}/">${b.title}</a></p>`)
      .join('')}</body></html>`;
    const blob = new Blob([html], { type: 'application/msword' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `pubmed-bookmarks-${Date.now()}.doc`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const exportBookmarksExcel = () => {
    const selected = bookmarks.filter(b => selectedBookmarkIds.includes(b.id));
    if (selected.length === 0) return;
    const header = 'Title,Link';
    const rows = selected
      .map(b => `"${b.title.replace(/"/g, '""')}","https://pubmed.ncbi.nlm.nih.gov/${b.id}/"`)
      .join('\n');
    const csvContent = [header, rows].join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `pubmed-bookmarks-${Date.now()}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const emailBookmarkLinks = () => {
    const selected = bookmarks.filter(b => selectedBookmarkIds.includes(b.id));
    if (selected.length === 0) return;
    const body = encodeURIComponent(selected.map(b => `https://pubmed.ncbi.nlm.nih.gov/${b.id}/`).join('\n'));
    window.location.href = `mailto:?subject=PubMed%20Bookmarks&body=${body}`;
  };

  const resetAll = () => {
    setSearches([
      {
        id: Date.now(),
        query: "",
        submittedQuery: "",
        author: "",
        journal: "",
        startYear: "",
        endYear: "",
        sort: "relevance",
        results: [],
        page: 1,
        totalCount: 0,
        isSearching: false,
        hasRun: false,
      },
    ]);
    setJournalCounts([5]);
    setAuthorCounts([5]);
    setCompareIds([]);
    setExpandedIds([]);
    setExpandedCompareIds([]);
    
    // Clear all AI-generated summaries
    setSearchSummaries({});
    setQuerySuggestions({});
    setResearchQuestions({});
    setComparisonSummary("");
    setBookmarkSummary("");
    setArticleSummaries({});
    setPlainSummaries({});
  };
  const search = async (
    idx: number,
    pageOverride?: number,
    queryOverride?: string,
    sortOverride?: string,
  ) => {
    const item = searches[idx];
    if (!item) {
      console.error(`Search item at index ${idx} is undefined`);
      return;
    }
    
    const q = queryOverride ?? item.query;
    if (!q.trim()) return;
    
    setSearches((prev) => prev.map((s, i) => (i === idx ? { ...s, isSearching: true } : s)));
    
    try {
      const response = await axios.get("/api/pubmed-search", {
        params: {
          query: q,
          page: pageOverride || item.page,
          pageSize: 20,
          startYear: item.startYear || undefined,
          endYear: item.endYear || undefined,
          sort: sortOverride || item.sort || "relevance",
          author: item.author || undefined,
          journal: item.journal || undefined,
        },
      });
      
      setSearches((prev) =>
        prev.map((s, i) => {
          if (i !== idx) return s;
          const newResults = pageOverride && pageOverride > 1 ? [...s.results, ...response.data.results] : response.data.results;
          return {
            ...s,
            results: newResults,
            totalCount: response.data.count,
            page: pageOverride || s.page,
            isSearching: false,
            hasRun: true,
            submittedQuery: q,
          };
        }),
      );
    } catch (error) {
      console.error("PubMed search error", error);
      setSearches((prev) =>
        prev.map((s, i) =>
          i === idx
            ? {
                ...s,
                results: pageOverride && pageOverride > 1 ? s.results : [],
                isSearching: false,
                hasRun: true,
                submittedQuery: q,
              }
            : s,
        ),
      );
    }
  };

  const handleSubmit = (e: React.FormEvent, idx: number) => {
    e.preventDefault();
    setSearches((prev) => prev.map((s, i) => (i === idx ? { ...s, page: 1 } : s)));
    search(idx, 1);
  };

  const loadMore = (idx: number) => {
    const nextPage = searches[idx].page + 1;
    search(idx, nextPage);
  };

  const updateSort = (idx: number, sort: string) => {
    setSearches((prev) =>
      prev.map((s, i) => (i === idx ? { ...s, sort, page: 1 } : s)),
    );
    search(idx, 1, undefined, sort);
  };

  const formatCount = (num: number | undefined) => {
    if (num === undefined) return "";
    return num >= 1000 ? num.toLocaleString() : String(num);
  };

  const displayedCount = searches.filter((s) => s.results.length > 0).length;
  const gridColumns =
    displayedCount === 1
      ? "grid grid-cols-1 gap-8"
      : displayedCount === 2
        ? "grid grid-cols-1 md:grid-cols-2 gap-8"
        : "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8";
  const isCompact = displayedCount === 3;

  // Add this function to fetch PDF links for search results
  // The API already returns direct links, just return the URL as-is
  const ensureDirectPdfUrl = (url: string | null): string | null => url;

  const getPdfLink = (id: string): string | null => {
    return (
      pdfLinks[id] ||
      semanticData[id]?.pdfUrl ||
      unpaywallData[id]?.pdfUrl ||
      pubmedLinks[id]?.pdf ||
      null
    );
  };

  const getViewablePdfLink = (id: string): string | null => viewablePdfLinks[id] || null;

  const getDownloadPdfLink = (id: string): string | null => {
    // Only offer downloads for verified PDF links
    const link = getViewablePdfLink(id);
    if (!link) return null;
    return link.startsWith('/api/')
      ? link
      : `/api/drugs-docs?download=1&url=${encodeURIComponent(link)}`;
  };

  const verifyPdfUrl = async (url: string): Promise<boolean> => {
    try {
      const proxied = url.startsWith("/api/")
        ? url
        : `/api/drugs-docs?download=1&url=${encodeURIComponent(url)}`;
      const res = await fetch(proxied);
      if (!res.ok) return false;
      
      // Check content type
      const contentType = res.headers.get('content-type');
      if (contentType && !contentType.includes('application/pdf')) {
        return false;
      }
      
      const bytes = await res.arrayBuffer();
      
      // Check if it starts with PDF header
      const uint8Array = new Uint8Array(bytes);
      const header = String.fromCharCode(...uint8Array.slice(0, 4));
      if (header !== '%PDF') {
        return false;
      }
      
      await PDFDocument.load(bytes);
      return true;
    } catch (err) {
      console.error("PDF verify error", err);
      if (
        err instanceof Error &&
        err.message.includes("No PDF header found")
      ) {
        return false;
      }
      return false;
    }
  };

  const fetchPdfLinks = async (results: PubMedResult[]) => {
    if (results.length === 0) return;

    const pmcLinks: Record<string, string | null> = {};
    const pmLinks: Record<string, { pdf: string | null; link: string | null }> = {};
    
    // Process in smaller batches to avoid overwhelming the API
    const batchSize = 10;
    for (let i = 0; i < results.length; i += batchSize) {
      const batch = results.slice(i, i + batchSize);

      await Promise.all(
        batch.map(async (r) => {
          // PMC PDF link fetch
          try {
            const res = await fetch(`/api/pmc-pdf-link?pmid=${r.id}`);
            if (!res.ok) throw new Error('Failed to fetch PDF link');

            const data = await res.json();
            if (data.pdfUrl) {
              pmcLinks[r.id] = ensureDirectPdfUrl(data.pdfUrl);
            } else {
              pmcLinks[r.id] = null;
            }
          } catch (error) {
            console.error(`Error fetching PDF link for PMID ${r.id}:`, error);
            pmcLinks[r.id] = null;
          }

          // PubMed full text link fetch
          try {
            const res2 = await fetch(`/api/pubmed-fulltext-link?pmid=${r.id}`);
            if (!res2.ok) {
              console.warn(`Failed to fetch full text link for PMID ${r.id}: ${res2.status}`);
              pmLinks[r.id] = { pdf: null, link: null };
              return;
            }
            const data2 = await res2.json();
            pmLinks[r.id] = {
              pdf: data2.pdfLink || null,
              link: data2.pageLink || null,
            };
          } catch (error) {
            console.error(`Error fetching full text link for PMID ${r.id}:`, error);
            pmLinks[r.id] = { pdf: null, link: null };
          }
        })
      );

      setPdfLinks(prev => ({ ...prev, ...pmcLinks }));
      setPubmedLinks(prev => ({ ...prev, ...pmLinks }));
      
      // Small delay between batches
      if (i + batchSize < results.length) {
        await new Promise(resolve => setTimeout(resolve, 300));
      }
    }
  };

  // Add this effect to fetch PDF links when search results change
  useEffect(() => {
    if (searches.every(s => s.results.length === 0)) {
      setPdfLinks({});
      setPubmedLinks({});
      return;
    }
    searches.forEach(search => {
      if (search.results.length > 0) {
        fetchPdfLinks(search.results);
      }
    });
  }, [searches.map(s => s.results.length).join(',')]);

  useEffect(() => {
    const ids: string[] = [];
    searches.forEach((s) => s.results.forEach((r) => ids.push(r.id)));
    const unique = Array.from(new Set(ids));
    if (unique.length === 0) {
      setViewablePdfLinks({});
      return;
    }
    const checkLinks = async () => {
      const results: Record<string, string | null> = {};
      const verified: Record<string, boolean> = {};
      for (const id of unique) {
        const links = [
          pdfLinks[id],
          semanticData[id]?.pdfUrl || null,
          unpaywallData[id]?.pdfUrl || null,
          pubmedLinks[id]?.pdf || null,
        ].filter(Boolean) as string[];
        let found: string | null = null;
        for (const l of links) {
          const ok = await verifyPdfUrl(l);
          verified[l] = ok;
          if (ok && !found) {
            found = l;
          }
        }
        results[id] = found;
      }
      setViewablePdfLinks((prev) => ({ ...prev, ...results }));
      setVerifiedPdfUrls((prev) => ({ ...prev, ...verified }));
    };
    checkLinks();
  }, [
    searches.map((s) => s.results.map((r) => r.id).join(',')).join(';'),
    pdfLinks,
    semanticData,
    unpaywallData,
    pubmedLinks,
  ]);

  useEffect(() => {
    setSelectedBookmarkIds((prev) => prev.filter(id => bookmarks.some(b => b.id === id)));
  }, [bookmarks]);

  useEffect(() => {
    const observers: IntersectionObserver[] = [];
    sentinelRefs.current.forEach((ref, idx) => {
      if (!ref) return;
      const obs = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting) {
            const s = searches[idx];
            if (
              s &&
              s.results.length < s.totalCount &&
              !s.isSearching
            ) {
              loadMore(idx);
            }
          }
        },
        { rootMargin: '100px' },
      );
      obs.observe(ref);
      observers.push(obs);
    });
    return () => {
      observers.forEach((o) => o.disconnect());
    };
  }, [searches.map((s) => `${s.page}-${s.results.length}-${s.totalCount}-${s.isSearching}`).join('|')]);

  // Only render the full component when mounted
  if (!mounted) {
    return null;
  }

  return (
    <>
      {popup && (
        <SelectionPopup
          className={cn("fixed", pdfOnTop ? "z-[99999]" : "z-[900001]")}
          data={popup}
          reference={reference}
          source={popup.articleUrl || window.location.href}
          onReferenceChange={setReference}
          onSave={saveSelection}
          onCancel={closePopup}
        />
      )}
      <div className="container mx-auto py-8 px-4">
      <SubpageHeader current="pubmed-explorer" />

      <h1 className="text-3xl font-bold mb-6">PubMed Explorer</h1>

      <div className="prose max-w-none mb-8 dark:text-gray-200">
        <p>Search the PubMed database like never before.</p>
      </div>

      <div className="space-y-4 mb-8">
        {searches.map((s, idx) => (
          <form
            key={s.id}
            onSubmit={(e) => handleSubmit(e, idx)}
            className="flex flex-wrap gap-2 items-end"
          >
            <Input
              type="search"
              placeholder="Enter keywords..."
              value={s.query}
              onChange={(e) =>
                setSearches((prev) =>
                  prev.map((p, i) => (i === idx ? { ...p, query: e.target.value } : p)),
                )
              }
              className="flex-1 min-w-[200px]"
            />
            <Input
              type="number"
              placeholder="Start Year"
              value={s.startYear}
              onChange={(e) =>
                setSearches((prev) => prev.map((p, i) => (i === idx ? { ...p, startYear: e.target.value } : p)))
              }
              className="w-32"
            />
            <Input
              type="number"
              placeholder="End Year"
              value={s.endYear}
              onChange={(e) =>
                setSearches((prev) => prev.map((p, i) => (i === idx ? { ...p, endYear: e.target.value } : p)))
              }
              className="w-32"
            />
            <Input
              type="text"
              placeholder="Author"
              value={s.author}
              onChange={(e) =>
                setSearches((prev) => prev.map((p, i) => (i === idx ? { ...p, author: e.target.value } : p)))
              }
              className="w-40"
            />
            <Input
              type="text"
              placeholder="Journal"
              value={s.journal}
              onChange={(e) =>
                setSearches((prev) => prev.map((p, i) => (i === idx ? { ...p, journal: e.target.value } : p)))
              }
              className="w-40"
            />
            <select
              value={s.sort}
              onChange={(e) =>
                setSearches((prev) => prev.map((p, i) => (i === idx ? { ...p, sort: e.target.value } : p)))
              }
              className="border rounded-md px-2 py-2 text-sm h-10 bg-background text-foreground"
            >
              <option value="relevance">Best Match</option>
              <option value="date">Most Recent</option>
            </select>
            <Button
              type="submit"
              className="bg-amber-400 text-black hover:bg-amber-500"
              disabled={s.isSearching}
            >
              {s.isSearching ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Search className="h-4 w-4 mr-2" />
              )}
              Search
            </Button>
            <Button
              type="button"
              onClick={() => saveCurrentQuery(idx)}
              variant="outline"
              className="flex items-center gap-1"
            >
              <Save className="h-4 w-4" /> Save
            </Button>
            {hasResults && (
              <Button type="button" onClick={() => clearFilters(idx)} variant="outline">
                Clear Filters
              </Button>
            )}
            {searches.length > 1 && (
              <Button type="button" variant="ghost" onClick={() => removeSearch(s.id)}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </form>
        ))}
        <div className="flex items-center gap-2">
          {searches.length < 3 && (
            <Button
              variant="outline"
              onClick={() => {
                setSearches((prev) => [
                  ...prev,
                  {
                    id: Date.now(),
                    query: "",
                    submittedQuery: "",
                    author: "",
                    journal: "",
                    startYear: "",
                    endYear: "",
                    sort: "relevance",
                    results: [],
                    page: 1,
                    totalCount: 0,
                    isSearching: false,
                    hasRun: false,
                  },
                ]);
                setJournalCounts((prev) => [...prev, 5]);
                setAuthorCounts((prev) => [...prev, 5]);
                // Remove viewTypes update
              }}
              className="h-10"
            >
              <Plus className="h-4 w-4 mr-2" /> Add Search
            </Button>
          )}
          <Button variant="outline" onClick={resetAll} className="h-10">
            Reset All
          </Button>
        </div>
      </div>

      {savedQueries.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-bold mb-2">Saved Searches</h2>
          <div className="flex flex-wrap gap-2">
            {savedQueries.map((sq, idx) => (
              <div key={idx} className="flex items-center gap-1 border rounded px-2 py-0">
                {/* adjust py value above to change saved search box height */}
                <Button
                  variant="link"
                  className="p-0 text-sm"
                  onClick={() => loadSavedQuery(sq)}
                >
                  {sq.query}
                </Button>
                <button
                  onClick={() => removeSavedQuery(idx)}
                  className="text-muted-foreground hover:text-red-500"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {bookmarks.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-bold mb-2">Bookmarked Articles</h2>
          <div className="flex flex-wrap gap-2 mb-2">
            <div className="flex items-center gap-1">
              <Button
                variant="outline"
                size="sm"
                onClick={summarizeBookmarks}
                disabled={loading.bookmark}
                className="gap-1"
              >
                {loading.bookmark ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" /> Loading...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4" /> Summarize Bookmarks
                  </>
                )}
              </Button>
            </div>
            <Button variant="outline" size="sm" onClick={toggleSelectAllBookmarks}>
              {selectedBookmarkIds.length === bookmarks.length ? 'Deselect All' : 'Select All'}
            </Button>
            <Button variant="outline" size="sm" onClick={exportBookmarksWord} disabled={selectedBookmarkIds.length===0}>
              Word
            </Button>
            <Button variant="outline" size="sm" onClick={exportBookmarksExcel} disabled={selectedBookmarkIds.length===0}>
              Excel
            </Button>
            <Button variant="outline" size="sm" onClick={exportBookmarksCsv} disabled={selectedBookmarkIds.length===0}>
              CSV
            </Button>
            <Button variant="outline" size="sm" onClick={emailBookmarkLinks} disabled={selectedBookmarkIds.length===0}>
              Email
            </Button>
          </div>
          {bookmarkSummary && (
            <div className="mb-4 p-2 rounded-md bg-blue-50 dark:bg-blue-900/40 relative">
              <button 
                onClick={() => setBookmarkSummary("")}
                className="absolute top-1 right-1 text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-800 rounded-full p-1"
                aria-label="Close bookmark summary"
              >
                <X className="h-3 w-3" />
              </button>
              <p className="font-semibold text-blue-700 dark:text-blue-300 flex items-center gap-1 mb-1">
                <Sparkles className="h-4 w-4" /> Bookmark Summary
              </p>
              <ul className="list-[circle] pl-4 text-sm text-blue-700 dark:text-blue-300">
                {parseBullets(bookmarkSummary).map((b, i) => (
                  <li key={i}>{b}</li>
                ))}
              </ul>
            </div>
          )}
          <div className="space-y-2">
            {sortedBookmarks.map((b, idx) => (
              <div key={idx} className="flex items-center gap-2">
                <Checkbox
                  checked={selectedBookmarkIds.includes(b.id)}
                  onCheckedChange={() => toggleBookmarkSelection(b.id)}
                />
                <a
                  href={`https://pubmed.ncbi.nlm.nih.gov/${b.id}/`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                  title={`${b.authors?.split(",")[0]?.trim() || ""}${
                    b.journal ? ` - ${toTitleCase(b.journal)}` : ""
                  }${
                    b.pubdate ? ` (${b.pubdate.match(/\d{4}/)?.[0] || b.pubdate})` : ""
                  }`}
                >
                  {b.title}
                  {getPdfLink(b.id) ? (
                    <>
                      <a
                        href={getPdfLink(b.id)!}
                        target="_blank"
                        rel="noopener noreferrer"
                        aria-label="Open PDF"
                        className="text-primary hover:text-primary/80 inline-block ml-1"
                        title="View PDF"
                      >
                        <FileText className="h-3 w-3 inline-block align-text-bottom" />
                      </a>
                      {getViewablePdfLink(b.id) && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-4 w-4 p-0 ml-1"
                          onClick={() => setPdfModal({ 
                            url: getViewablePdfLink(b.id)!, 
                            title: b.title,
                            articleUrl: `https://pubmed.ncbi.nlm.nih.gov/${b.id}/`
                          })}
                          aria-label="Preview PDF"
                        >
                          <Eye className="h-3 w-3" />
                        </Button>
                      )}
                      {getDownloadPdfLink(b.id) && (
                        <a
                          href={getDownloadPdfLink(b.id)!}
                          className="text-primary hover:text-primary/80 inline-block ml-1"
                          aria-label="Download PDF"
                          title="Download PDF"
                          download
                        >
                          <Download className="h-3 w-3 inline-block align-text-bottom" />
                        </a>
                      )}
                    </>
                  ) : (
                    pubmedLinks[b.id]?.link && (
                      <a
                        href={pubmedLinks[b.id]!.link!}
                        target="_blank"
                        rel="noopener noreferrer"
                        aria-label="Open link"
                        className="text-muted-foreground hover:text-primary inline-block ml-1"
                        title="Visit full text page"
                      >
                        <ExternalLink className="h-3 w-3 inline-block align-text-bottom" />
                      </a>
                    )
                  )}
                </a>
                <button
                  onClick={() => toggleBookmark(b)}
                  className="text-muted-foreground hover:text-red-500"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {hasResults && (pubTypeFilters.length > 0 || keywordFilters.length > 0) && (
        <div className="mb-8 flex flex-wrap gap-2">
          {pubTypeFilters.map((pt) => (
            <Badge key={`pt-${pt}`} variant="outline" className="flex items-center gap-1">
              <span>{pt}</span>
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                onClick={() => removePubTypeFilter(pt)}
              >
                <span className="sr-only">Remove</span>
                <span aria-hidden>×</span>
              </Button>
            </Badge>
          ))}
          {keywordFilters.map((kw) => (
            <Badge key={`kw-${kw}`} variant="outline" className="flex items-center gap-1">
              <span>{kw}</span>
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                onClick={() => removeKeywordFilter(kw)}
              >
                <span className="sr-only">Remove</span>
                <span aria-hidden>×</span>
              </Button>
            </Badge>
          ))}
          <Button
            variant="ghost"
            size="sm"
            className="text-xs text-muted-foreground hover:text-amber-600"
            onClick={clearResultFilters}
          >
            Clear all filters
          </Button>
        </div>
      )}


      {hasResults && !showFilters && (
        <div className="mb-4">
          <Button variant="outline" size="sm" onClick={() => setShowFilters(true)}>
            <Filter className="h-4 w-4 mr-1" /> Show Filters
          </Button>
        </div>
      )}

      <div className="md:flex gap-6">
        {hasResults && showFilters && (
        <aside className="w-full md:w-64 shrink-0 mb-8 md:mb-0 sticky top-24 space-y-4">
          <div>
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-medium text-sm">Publication Types</h3>
              <div className="flex items-center gap-1">
                <select
                  value={pubTypeOp}
                  onChange={(e) => changePubTypeOp(e.target.value as 'AND' | 'OR')}
                  className="border rounded px-1 text-xs"
                >
                  <option value="OR">Any</option>
                  <option value="AND">All</option>
                </select>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3 w-3 text-muted-foreground cursor-pointer" />
                    </TooltipTrigger>
                    <TooltipContent className="max-w-xs text-xs">
                      <p><strong>Any</strong> matches at least one selected type. <strong>All</strong> requires every selected type.</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
            <div className="max-h-48 overflow-y-auto space-y-1">
              {allPubTypes.map((pt) => (
                <label key={pt} className="flex items-center gap-2 text-sm">
                  <Checkbox
                    checked={pubTypeFilters.includes(pt)}
                    onCheckedChange={() => togglePubTypeFilter(pt)}
                  />
                  <span>{pt}</span>
                </label>
              ))}
            </div>
          </div>
          <div>
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-medium text-sm">Keywords</h3>
              <div className="flex items-center gap-1">
                <select
                  value={keywordOp}
                  onChange={(e) => changeKeywordOp(e.target.value as 'AND' | 'OR')}
                  className="border rounded px-1 text-xs"
                >
                  <option value="OR">Any</option>
                  <option value="AND">All</option>
                </select>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3 w-3 text-muted-foreground cursor-pointer" />
                    </TooltipTrigger>
                    <TooltipContent className="max-w-xs text-xs">
                      <p><strong>Any</strong> shows articles containing at least one selected keyword. <strong>All</strong> requires every selected keyword.</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
            <div className="max-h-48 overflow-y-auto space-y-1">
              {allKeywords.map((kw) => (
                <label key={kw} className="flex items-center gap-2 text-sm">
                  <Checkbox
                    checked={keywordFilters.includes(kw)}
                    onCheckedChange={() => toggleKeywordFilter(kw)}
                  />
                  <span>{kw}</span>
                </label>
              ))}
            </div>
          </div>
          <div className="flex justify-between items-center">
            <Button variant="ghost" size="sm" onClick={clearResultFilters} className="text-xs">
              Clear Filters
            </Button>
            <Button variant="ghost" size="sm" onClick={() => setShowFilters(false)} className="text-xs">
              Hide Filters
            </Button>
          </div>
        </aside>
        )}

        <div className="flex-1">

      {/* {allResults.length > 0 && (
        <div className="mb-8 space-y-4">
          <div className="flex justify-between">
            <Button variant="outline" onClick={exportCsv} className="gap-1">
              <Download className="h-4 w-4" /> Export CSV
            </Button>
          </div>
        </div>
      )} */}

      {compareItems.length > 1 && (
        <div ref={comparisonRef} className="mb-8 overflow-auto space-y-2">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold">Comparison</h2>
            <div className="flex gap-2 items-center">
              <Button variant="outline" size="sm" onClick={exportComparisonCsv} className="gap-1">
                <Download className="h-4 w-4" /> Export
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={summarizeComparison}
                disabled={loading.comparison}
                className="gap-1"
              >
                {loading.comparison ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" /> Loading...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4" /> Summarize
                  </>
                )}
              </Button>
              <Button variant="outline" size="sm" onClick={clearComparison}>
                Clear
              </Button>
            </div>
          </div>
          {contextDocs.length > 0 && (
            <div className="flex flex-wrap gap-2 text-xs mt-1">
              {contextDocs.map((d) => (
                <span key={d.id} className="px-2 py-1 bg-slate-100 dark:bg-slate-800 rounded inline-flex items-center gap-1">
                  {d.title}
                  <button onClick={() => removeContextDoc(d.id)} className="hover:text-red-600">
                    <X className="h-3 w-3" />
                  </button>
                </span>
              ))}
            </div>
          )}
          <table className="min-w-full text-sm border-2 border-gray-300 dark:border-gray-600 rounded-md overflow-hidden">
            <thead className="bg-muted">
              <tr>
                <th className="border border-gray-300 dark:border-gray-600 px-3 py-2 w-32 text-left align-top">Title</th>
                {compareItems.map((c) => (
                  <th key={c.id} className="border border-gray-300 dark:border-gray-600 px-3 py-2 w-60 text-left align-top">
                    <div className="flex items-start justify-between">
                      <span>{decodeHtmlEntities(c.title)}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 px-2"
                        onClick={() => toggleCompare(c.id)}
                        aria-label="Remove from comparison"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-border">
              <tr>
                <td className="border border-gray-300 dark:border-gray-600 px-3 py-2 font-medium w-32 text-left align-top">Journal</td>
                {compareItems.map((c) => (
                  <td
                    key={c.id}
                    className="border border-gray-300 dark:border-gray-600 px-3 py-2 w-60 text-left align-top"
                  >
                    {c.journal ? toTitleCase(decodeHtmlEntities(c.journal)) : ''}
                  </td>
                ))}
              </tr>
              <tr>
                <td className="border border-gray-300 dark:border-gray-600 px-3 py-2 font-medium w-32 text-left align-top">Pub Date</td>
                {compareItems.map((c) => (
                  <td key={c.id} className="border border-gray-300 dark:border-gray-600 px-3 py-2 w-60 text-left align-top">{c.pubdate}</td>
                ))}
              </tr>
              <tr>
                <td className="border border-gray-300 dark:border-gray-600 px-3 py-2 font-medium w-32 text-left align-top">PMID</td>
                {compareItems.map((c) => (
                  <td key={c.id} className="border border-gray-300 dark:border-gray-600 px-3 py-2 w-60 text-left align-top">
                    <a
                      href={`https://pubmed.ncbi.nlm.nih.gov/${c.id}/`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-amber-600 underline"
                    >
                      {c.id}
                    </a>
                    {getPdfLink(c.id) ? (
                      <>
                        <a
                          href={getPdfLink(c.id)!}
                          target="_blank"
                          rel="noopener noreferrer"
                          aria-label="Open PDF"
                          className="text-primary hover:text-primary/80 inline-block ml-1"
                          title="View PDF"
                        >
                          <FileText className="h-4 w-4 inline-block align-text-bottom" />
                        </a>
                        {getViewablePdfLink(c.id) && (
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-5 w-5 p-0 ml-1"
                            onClick={() => setPdfModal({ 
                              url: getViewablePdfLink(c.id)!, 
                              title: c.title,
                              articleUrl: `https://pubmed.ncbi.nlm.nih.gov/${c.id}/`
                            })}
                            aria-label="Preview PDF"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        )}
                        {getDownloadPdfLink(c.id) && (
                          <a
                            href={getDownloadPdfLink(c.id)!}
                            className="text-primary hover:text-primary/80 inline-block ml-1"
                            aria-label="Download PDF"
                            title="Download PDF"
                            download
                          >
                            <Download className="h-4 w-4 inline-block align-text-bottom" />
                          </a>
                        )}
                      </>
                    ) : (
                      pubmedLinks[c.id]?.link && (
                        <a
                          href={pubmedLinks[c.id]!.link!}
                          target="_blank"
                          rel="noopener noreferrer"
                          aria-label="Open link"
                          className="text-muted-foreground hover:text-primary inline-block ml-1"
                          title="Visit full text page"
                        >
                          <ExternalLink className="h-4 w-4 inline-block align-text-bottom" />
                        </a>
                      )
                    )}
                  </td>
                ))}
              </tr>
              <tr>
                <td className="border border-gray-300 dark:border-gray-600 px-3 py-2 font-medium w-32 text-left align-top">Authors</td>
                {compareItems.map((c) => (
                  <td key={c.id} className="border border-gray-300 dark:border-gray-600 px-3 py-2 w-60 text-left align-top">{c.authors}</td>
                ))}
              </tr>
              <tr>
                <td className="border border-gray-300 dark:border-gray-600 px-3 py-2 font-medium w-32 text-left align-top">Abstract</td>
                {compareItems.map((c) => (
                  <td key={c.id} className="border border-gray-300 dark:border-gray-600 px-3 py-2 w-60 text-left align-top">
                    {c.abstract && decodeHtmlEntities(c.abstract).length > 250 && !expandedCompareIds.includes(c.id) ? (
                      <>
                        {decodeHtmlEntities(c.abstract).substring(0, 250)}...
                        <Button variant="link" size="sm" className="h-4 px-1" onClick={() => toggleCompareAbstract(c.id)}>
                          Show More
                        </Button>
                      </>
                    ) : (
                      <>
                        {c.abstract ? decodeHtmlEntities(c.abstract) : ''}
                        {c.abstract && decodeHtmlEntities(c.abstract).length > 250 && (
                          <Button variant="link" size="sm" className="h-4 px-1" onClick={() => toggleCompareAbstract(c.id)}>
                            Show Less
                          </Button>
                        )}
                      </>
                    )}
                  </td>
                ))}
              </tr>
            </tbody>
          </table>
          {comparisonSummary && (
            <div className="mt-4 p-2 rounded-md bg-blue-50 dark:bg-blue-900/40 relative">
              <button
                onClick={() => setComparisonSummary("")}
                className="absolute top-1 right-1 text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-800 rounded-full p-1"
                aria-label="Close comparison summary"
              >
                <X className="h-3 w-3" />
              </button>
              <p className="font-semibold text-blue-700 dark:text-blue-300 flex items-center gap-1 mb-1">
                <Sparkles className="h-4 w-4" /> Comparison Summary
              </p>
              <ul className="list-[circle] pl-4 text-sm text-blue-700 dark:text-blue-300">
                {parseBullets(comparisonSummary).map((b, i) => (
                  <li key={i}>{b}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      <div className={gridColumns}>
        {searches.map((search, sidx) => {
          if (search.results.length > 0) {
            return (
              <div key={search.id} className="relative space-y-8 border p-6 pt-0 rounded-lg">
                <button
                  onClick={() => clearSearchResults(search.id)}
                  className="absolute top-2 right-2 text-muted-foreground hover:text-red-500"
                  aria-label="Clear results"
                >
              <X className="h-4 w-4" />
            </button>
            <div className="font-bold text-lg mb-4 truncate pr-6" title={search.submittedQuery}>
              {search.submittedQuery}
            </div>
            
            {/* Charts */}
            <div
              className={
                displayedCount === 1
                  ? "md:grid md:grid-cols-3 gap-8"
                  : ""
              }
            >
              {/* Year distribution chart for this search */}
              <div className="h-64 w-full mb-8 md:mb-0">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-sm font-medium">Publication Years</h3>
                  {/* Remove chart type selection buttons */}
                </div>
                <ResponsiveContainer width="100%" height="85%">
                  {/* Always use bar chart */}
                    <BarChart
                      data={getYearData(applyResultFilters(search.results))}
                    margin={{ top: 5, right: 10, left: 0, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="year" />
                    <YAxis allowDecimals={false} />
                    <Bar
                      dataKey="count"
                      fill="#f59e0b"
                      onMouseEnter={(data, _idx, e) =>
                          showHoverTooltip(
                            data.payload.year,
                            getArticlesByYear(
                              applyResultFilters(search.results),
                              data.payload.year,
                            ),
                            e,
                          )
                      }
                      onMouseLeave={scheduleHideTooltip}
                      onClick={(data) => {
                        if (data && data.payload?.year) {
                          window.open(
                            `https://pubmed.ncbi.nlm.nih.gov/?term=${data.payload.year}[dp]`,
                            "_blank",
                          );
                        }
                      }}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>

              {/* Journal chart for this search */}
                {getJournalData(applyResultFilters(search.results)).length > 0 && (
                <div className="mb-8 md:mb-0">
                  <div className="flex justify-between items-center mb-2">
                  <h3 className="text-sm font-medium">Top Journals</h3>
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      className="h-7 px-2 text-xs"
                      onClick={() =>
                          setJournalCounts((c) =>
                            c.map((v, i) =>
                              i === sidx
                                ? Math.min(
                                    v + 5,
                                    getJournalData(applyResultFilters(search.results)).length,
                                  )
                                : v,
                            ),
                          )
                        }
                      disabled={
                        journalCounts[sidx] >=
                        getJournalData(applyResultFilters(search.results)).length
                      }
                    >
                      Show 5 More
                    </Button>
                    {journalCounts[sidx] > 5 && (
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-7 px-2 text-xs"
                        onClick={() =>
                          setJournalCounts((c) => c.map((v, i) => (i === sidx ? 5 : v)))
                        }
                      >
                        Top 5
                      </Button>
                    )}
                  </div>
                </div>
                <div style={{ height: `${Math.max(200, (journalCounts[sidx] || 5) * 40)}px` }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                        data={
                          getJournalData(applyResultFilters(search.results)).slice(
                            0,
                            journalCounts[sidx] || 5,
                          )
                        }
                      layout="vertical"
                      margin={{ top: 5, right: 100, left: 40, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" allowDecimals={false} />
                      <YAxis 
                        dataKey="journal" 
                        type="category" 
                        width={140}
                        tick={(props) => {
                          const { x, y, payload } = props;
                          return (
                            <g transform={`translate(${x},${y})`}>
                              <title>{toTitleCase(payload.value)}</title>
                              <text
                                x={0}
                                y={0}
                                dy={4}
                                textAnchor="end"
                                fontSize={12}
                                fontWeight="500"
                                fill="#444"
                              >
                                {payload.value.length > 22
                                  ? `${toTitleCase(payload.value.substring(0, 20))}...`
                                  : toTitleCase(payload.value)}
                              </text>
                            </g>
                          );
                        }}
                        interval={0}
                      />
                      <Bar
                        dataKey="value"
                        fill="#3b82f6"
                        onMouseEnter={(data, _idx, e) =>
                            showHoverTooltip(
                              data.payload.journal,
                              getArticlesByJournal(
                                applyResultFilters(search.results),
                                data.payload.journal,
                              ),
                              e,
                            )
                        }
                        onMouseLeave={scheduleHideTooltip}
                        onClick={(data) => {
                          if (data && data.payload?.journal) {
                            window.open(
                              `https://pubmed.ncbi.nlm.nih.gov/?term=${encodeURIComponent(data.payload.journal)}[jour]`,
                              "_blank",
                            );
                          }
                        }}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                  </div>
                </div>
              )}

              {/* Author chart for this search */}
              {getAuthorData(applyResultFilters(search.results)).length > 0 && (
                <div className="mb-8 md:mb-0">
                  <div className="flex justify-between items-center mb-2">
                  <h3 className="text-sm font-medium">Top Authors</h3>
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      className="h-7 px-2 text-xs"
                      onClick={() =>
                          setAuthorCounts((c) =>
                            c.map((v, i) =>
                              i === sidx
                                ? Math.min(
                                    v + 5,
                                    getAuthorData(applyResultFilters(search.results)).length,
                                  )
                                : v,
                            ),
                          )
                        }
                      disabled={
                        authorCounts[sidx] >=
                        getAuthorData(applyResultFilters(search.results)).length
                      }
                    >
                      Show 5 More
                    </Button>
                    {authorCounts[sidx] > 5 && (
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-7 px-2 text-xs"
                        onClick={() =>
                          setAuthorCounts((c) => c.map((v, i) => (i === sidx ? 5 : v)))
                        }
                      >
                        Top 5
                      </Button>
                    )}
                  </div>
                </div>
                <div style={{ height: `${Math.max(200, (authorCounts[sidx] || 5) * 40)}px` }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                        data={
                          getAuthorData(applyResultFilters(search.results)).slice(
                            0,
                            authorCounts[sidx] || 5,
                          )
                        }
                      layout="vertical"
                      margin={{ top: 5, right: 100, left: 40, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" allowDecimals={false} />
                      <YAxis 
                        dataKey="author" 
                        type="category" 
                        width={140}
                        tick={(props) => {
                          const { x, y, payload } = props;
                          return (
                            <g transform={`translate(${x},${y})`}>
                              <title>{payload.value}</title>
                              <text 
                                x={0} 
                                y={0} 
                                dy={4} 
                                textAnchor="end" 
                                fontSize={12}
                                fontWeight="500"
                                fill="#444"
                              >
                                {payload.value.length > 22 ? `${payload.value.substring(0, 20)}...` : payload.value}
                              </text>
                            </g>
                          );
                        }}
                        interval={0}
                      />
                      <Bar
                        dataKey="value"
                        fill="#10b981"
                        onMouseEnter={(data, _idx, e) =>
                            showHoverTooltip(
                              data.payload.author,
                              getArticlesByAuthor(
                                applyResultFilters(search.results),
                                data.payload.author,
                              ),
                              e,
                            )
                        }
                        onMouseLeave={scheduleHideTooltip}
                        onClick={(data) => {
                          if (data && data.payload?.author) {
                            window.open(
                              `https://pubmed.ncbi.nlm.nih.gov/?term=${encodeURIComponent(data.payload.author)}[auth]`,
                              "_blank",
                            );
                          }
                        }}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                  </div>
                </div>
              )}
            </div>
            
            {/* Export and AI actions */}
            <div className="flex flex-wrap justify-end mb-6 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => exportSearchCsv(sidx)}
                className="gap-1"
              >
                <Download className="h-4 w-4" /> Export Results
              </Button>
              <div className="flex items-center gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => summarizeSearchResults(sidx)}
                  disabled={loading[`search-${sidx}`]}
                  className="gap-1"
                >
                  {loading[`search-${sidx}`] ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" /> Summarizing...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4" /> Summarize Search
                    </>
                  )}
                </Button>
              </div>
              <div className="flex items-center gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => suggestQueries(sidx)}
                  disabled={loading[`query-${sidx}`]}
                  className="gap-1"
                >
                  {loading[`query-${sidx}`] ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" /> Generating...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4" /> Query Ideas
                    </>
                  )}
                </Button>
              </div>
              <div className="flex items-center gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => suggestResearchQuestions(sidx)}
                  disabled={loading[`research-${sidx}`]}
                  className="gap-1"
                >
                  {loading[`research-${sidx}`] ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" /> Thinking...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4" /> Research Questions
                    </>
                  )}
                </Button>
              </div>
            </div>
            {searchSummaries[sidx] && (
              <div className="mb-4 p-2 rounded-md bg-blue-50 dark:bg-blue-900/40 relative">
                <button 
                  onClick={() => setSearchSummaries(prev => ({...prev, [sidx]: ""}))}
                  className="absolute top-1 right-1 text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-800 rounded-full p-1"
                  aria-label="Close summary"
                >
                  <X className="h-3 w-3" />
                </button>
                <p className="font-semibold text-blue-700 dark:text-blue-300 flex items-center gap-1 mb-1">
                  <Sparkles className="h-4 w-4" /> Search Summary
                </p>
                <ul className="list-[circle] pl-4 text-sm text-blue-700 dark:text-blue-300">
                  {parseBullets(searchSummaries[sidx]).map((b, i) => (
                    <li key={i}>{b}</li>
                  ))}
                </ul>
              </div>
            )}
            {querySuggestions[sidx] && (
              <div className="mb-4 p-2 rounded-md bg-amber-50 dark:bg-amber-900/40 relative">
                <button
                  onClick={() =>
                    setQuerySuggestions(prev => {
                      const copy = { ...prev } as Record<number, string[]>;
                      delete copy[sidx];
                      return copy;
                    })
                  }
                  className="absolute top-1 right-1 text-amber-700 dark:text-amber-300 hover:bg-amber-100 dark:hover:bg-amber-800 rounded-full p-1"
                  aria-label="Close query suggestions"
                >
                  <X className="h-3 w-3" />
                </button>
                <p className="font-semibold text-amber-700 dark:text-amber-300 flex items-center gap-1 mb-1">
                  <Lightbulb className="h-4 w-4" /> Query Suggestions
                </p>
                <ul className="list-[circle] pl-4 text-sm text-amber-700 dark:text-amber-300">
                  {querySuggestions[sidx].map((q, i) => (
                    <li key={i} className="cursor-pointer hover:underline" onClick={() => {
                      setSearches(prev => prev.map((s, idx) => 
                        idx === sidx ? {...s, query: q.replace(/^[•\s-]+/, '')} : s
                      ));
                    }}>
                      {q}
                    </li>
                  ))}
                </ul>
              </div>
            )}
            {researchQuestions[sidx] && (
              <div className="mb-4 p-2 rounded-md bg-green-50 dark:bg-green-900/40 relative">
                <button
                  onClick={() =>
                    setResearchQuestions(prev => {
                      const copy = { ...prev } as Record<number, string[]>;
                      delete copy[sidx];
                      return copy;
                    })
                  }
                  className="absolute top-1 right-1 text-green-700 dark:text-green-300 hover:bg-green-100 dark:hover:bg-green-800 rounded-full p-1"
                  aria-label="Close research questions"
                >
                  <X className="h-3 w-3" />
                </button>
                <p className="font-semibold text-green-700 dark:text-green-300 flex items-center gap-1 mb-1">
                  <Lightbulb className="h-4 w-4" /> Research Questions
                </p>
                <ul className="list-[circle] pl-4 text-sm text-green-700 dark:text-green-300">
                  {researchQuestions[sidx].map((q, i) => (
                    <li key={i}>{q}</li>
                  ))}
                </ul>
              </div>
            )}
            
            {/* Results for this search */}
            <div className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className={cn("font-medium", isCompact ? "text-sm" : "text-lg")}>
                  Search Results ({applyResultFilters(search.results).length}
                  {search.totalCount
                    ? ` of ${formatCount(search.totalCount)}`
                    : ""})
                </h3>
                {search.results.length > 0 && (
                  <div className="flex items-center gap-2 relative">
                    {/* Load more handled via IntersectionObserver */}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          size="sm"
                          variant="outline"
                          className={cn(
                            "flex items-center gap-1 px-2 h-10",
                            isCompact && "h-8 px-1 text-xs"
                          )}
                        >
                          {search.sort === "date" ? "Most Recent" : "Best Match"}
                          <ChevronDown className={cn("h-4 w-4", isCompact && "h-3 w-3")} />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="center" side="top">
                        <DropdownMenuItem
                          onSelect={() => updateSort(sidx, "relevance")}
                        >
                          Best Match
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onSelect={() => updateSort(sidx, "date")}
                        >
                          Most Recent
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                )}
              </div>
              {filterLoading
                ? Array.from({ length: 3 }).map((_, idx) => (
                    <ResultCardSkeleton key={idx} />
                  ))
                : applyResultFilters(search.results).map((item, idx: number) => (
                <Card key={`${sidx}-${idx}`} data-pmid={item.id}>
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <CardTitle className="text-lg flex-1 pr-20">
                        <a
                          href={`https://pubmed.ncbi.nlm.nih.gov/${item.id}/`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="hover:underline visited:text-blue-700 dark:visited:text-blue-400"
                        >
                          {highlightText(item.title.replace(/\.$/, ''), search.submittedQuery)}
                        </a>
                        <span className="inline-block">
                          {getPdfLink(item.id) ? (
                            <>
                              <a
                                href={getPdfLink(item.id)!}
                                target="_blank"
                                rel="noopener noreferrer"
                                aria-label="Open PDF"
                                className="text-primary hover:text-primary/80 visited:text-blue-700 inline-block ml-1"
                                title="View PDF"
                              >
                                <FileText className="h-4 w-4 mb-1 inline-block align-text-bottom" />
                              </a>
                              {getViewablePdfLink(item.id) && (
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-5 w-5 p-0 ml-1"
                                  onClick={() => setPdfModal({ 
                                    url: getViewablePdfLink(item.id)!, 
                                    title: item.title,
                                    articleUrl: `https://pubmed.ncbi.nlm.nih.gov/${item.id}/`
                                  })}
                                  aria-label="Preview PDF"
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                              )}
                              {getDownloadPdfLink(item.id) && (
                                <a
                                  href={getDownloadPdfLink(item.id)!}
                                  className="text-primary hover:text-primary/80 visited:text-blue-700 inline-block ml-1"
                                  aria-label="Download PDF"
                                  title="Download PDF"
                                  download
                                >
                                  <Download className="h-4 w-4 mb-1 inline-block align-text-bottom" />
                                </a>
                              )}
                            </>
                          ) : (
                            pubmedLinks[item.id]?.link && (
                              <a
                                href={pubmedLinks[item.id]!.link!}
                                target="_blank"
                                rel="noopener noreferrer"
                                aria-label="Open link"
                                className="text-muted-foreground hover:text-primary visited:text-blue-700 inline-block ml-1"
                                title="Visit full text page"
                              >
                                <ExternalLink className="h-4 w-4 mb-1 inline-block align-text-bottom" />
                              </a>
                            )
                          )}
                        </span>
                      </CardTitle>
                      <div className="flex items-center gap-2">
                        <Checkbox
                          checked={compareIds.includes(item.id)}
                          onCheckedChange={() => toggleCompare(item.id)}
                        />
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleBookmark(item)}
                          className="h-6 px-2 hover:bg-transparent"
                        >
                          {bookmarks.some((b) => b.id === item.id) ? (
                            <BookmarkCheck className="h-4 w-4 text-amber-500" />
                          ) : (
                            <Bookmark className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {(item.journal || item.pubdate) && (
                      <p className="text-sm text-muted-foreground mb-1">
                        {item.journal ? toTitleCase(item.journal) : ""}
                        {item.pubdate ? ` • ${item.pubdate}` : ""}
                      </p>
                    )}
                    {item.authors && (
                      <p className="text-sm text-muted-foreground mb-2">
                        {item.authors}
                      </p>
                    )}
                    {item.abstract && (
                      <div className="text-sm mb-2">
                        {highlightTextWithLinks(
                          expandedIds.includes(`${sidx}-${idx}`)
                            ? item.abstract
                            : `${item.abstract.substring(0, 250)}...`,
                          search.submittedQuery,
                        )}
                        <Button
                          variant="link"
                          size="sm"
                          className="h-4 px-1"
                          onClick={() => toggleAbstract(`${sidx}-${idx}`)}
                        >
                          {expandedIds.includes(`${sidx}-${idx}`) ? "Show Less" : "Show More"}
                        </Button>
                      </div>
                    )}
                    {(item.publicationTypes?.length || item.meshTerms?.length) && (
                      <div className="text-xs text-muted-foreground mb-2 space-y-1">
                        {item.publicationTypes && item.publicationTypes.length > 0 && (
                          <p>
                            <strong>Publication types:</strong>{' '}
                            {item.publicationTypes.map((pt, i) => (
                              <span
                                key={pt}
                                className="cursor-pointer underline"
                                onClick={() => addPubTypeFilter(pt)}
                              >
                                {pt}
                                {i < item.publicationTypes!.length - 1 && '; '}
                              </span>
                            ))}
                          </p>
                        )}
                        {item.meshTerms && item.meshTerms.length > 0 && (
                          <p>
                            <strong>Keywords:</strong>{' '}
                            {item.meshTerms.map((kw, i) => (
                              <span
                                key={kw}
                                className="cursor-pointer underline"
                                onClick={() => addKeywordFilter(kw)}
                              >
                                {kw}
                                {i < item.meshTerms!.length - 1 && '; '}
                              </span>
                            ))}
                          </p>
                        )}
                        {openAlexData[item.id]?.authorships && openAlexData[item.id]!.authorships!.length > 0 && (
                          <p>
                            <strong>Institutions:</strong>{' '}
                            {(() => {
                              const institutionsText = Array.from(
                                new Set(
                                  openAlexData[item.id]!.authorships!
                                    .flatMap((a) => a.institutions.map((ins) => ins.display_name))
                                    .filter(Boolean),
                                ),
                              ).join('; ');
                              
                              const institutionsId = `institutions-${sidx}-${idx}`;
                              const isExpanded = expandedIds.includes(institutionsId);
                              
                              if (institutionsText.length > 200 && !isExpanded) {
                                return (
                                  <>
                                    {institutionsText.substring(0, 200)}...
                                    <Button
                                      variant="link"
                                      size="sm"
                                      className="h-4 px-1 text-xs"
                                      onClick={() => toggleAbstract(institutionsId)}
                                    >
                                      Show More
                                    </Button>
                                  </>
                                );
                              } else {
                                return (
                                  <>
                                    {institutionsText}
                                    {institutionsText.length > 200 && (
                                      <Button
                                        variant="link"
                                        size="sm"
                                        className="h-4 px-1 text-xs"
                                        onClick={() => toggleAbstract(institutionsId)}
                                      >
                                        Show Less
                                      </Button>
                                    )}
                                  </>
                                );
                              }
                            })()}
                          </p>
                        )}
                      </div>
                    )}
                    {item.abstract && (
                      <div className="text-xs flex items-center gap-2 mb-2">
                        <div className="flex items-center gap-1">
                          <Button
                            variant="link"
                            size="sm"
                            onClick={() => summarizeArticle(item)}
                            disabled={loading[`article-${item.id}`]}
                            className="gap-1"
                          >
                            {loading[`article-${item.id}`] ? (
                              <>
                                <Loader2 className="h-3 w-3 animate-spin" /> Working...
                              </>
                            ) : (
                              <>
                                <Sparkles className="h-3 w-3" /> Summarize
                              </>
                            )}
                          </Button>
                        </div>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="link"
                            size="sm"
                            onClick={() => plainLanguageSummary(item)}
                            disabled={loading[`plain-${item.id}`]}
                            className="gap-1"
                          >
                            {loading[`plain-${item.id}`] ? (
                              <>
                                <Loader2 className="h-3 w-3 animate-spin" /> Simplifying...
                              </>
                            ) : (
                              <>
                                <Sparkles className="h-3 w-3" /> Plain Language Summary
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    )}
                    {articleSummaries[item.id] && (
                      <div className="mb-2 p-2 rounded bg-blue-50 dark:bg-blue-900/40 relative">
                        <button
                          onClick={() =>
                            setArticleSummaries((prev) => {
                              const copy = { ...prev } as Record<string, string>;
                              delete copy[item.id];
                              return copy;
                            })
                          }
                          className="absolute top-1 right-1 text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-800 rounded-full p-1"
                          aria-label="Close article summary"
                        >
                          <X className="h-3 w-3" />
                        </button>
                        <p className="font-semibold text-blue-700 dark:text-blue-300 flex items-center gap-1 text-xs mb-1">
                          <Sparkles className="h-3 w-3" /> Article Summary
                        </p>
                        <ul className="list-[circle] pl-4 text-xs text-blue-700 dark:text-blue-300">
                          {parseBullets(articleSummaries[item.id]).map((b, i) => (
                            <li key={i}>{b}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    {plainSummaries[item.id] && (
                      <div className="mb-2 p-2 rounded bg-orange-50 dark:bg-orange-900/40 relative">
                        <button
                          onClick={() =>
                            setPlainSummaries((prev) => {
                              const copy = { ...prev } as Record<string, string>;
                              delete copy[item.id];
                              return copy;
                            })
                          }
                          className="absolute top-1 right-1 text-orange-700 dark:text-orange-300 hover:bg-orange-100 dark:hover:bg-orange-800 rounded-full p-1"
                          aria-label="Close plain summary"
                        >
                          <X className="h-3 w-3" />
                        </button>
                        <p className="font-semibold text-orange-700 dark:text-orange-300 flex items-center gap-1 text-xs mb-1">
                          <Lightbulb className="h-3 w-3" /> Plain Language Summary
                        </p>
                        <ul className="list-[circle] pl-4 text-xs text-orange-700 dark:text-orange-300">
                          {parseBullets(plainSummaries[item.id]).map((b, i) => (
                            <li key={i}>{b}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    {openAlexData[item.id] && (
                      <div className="text-xs text-muted-foreground mb-2 space-y-1">
                        <div className="flex flex-wrap gap-4">
                          <span>
                            Cites:{' '}
                            <a
                              href={getOpenAlexLink(item.id, 'cited_by')}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="underline"
                              title="Works cited by this article"
                            >
                              {openAlexData[item.id]?.referenced_works?.length ?? 0}
                            </a>
                          </span>
                          <span>
                            Cited by:{' '}
                            <a
                              href={getOpenAlexLink(item.id, 'cites')}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="underline"
                              title="Works citing this article"
                            >
                              {openAlexData[item.id]?.cited_by_count}
                            </a>
                          </span>
                          {openAlexData[item.id]?.related_works && (
                            <span>
                              Related to:{' '}
                              <a
                                href={getOpenAlexLink(item.id, 'related_to')}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="underline"
                                title="Related works"
                              >
                                {openAlexData[item.id]!.related_works!.length}
                              </a>
                            </span>
                          )}
                        </div>
                        {(openAlexData[item.id]?.field_citation_ratio || openAlexData[item.id]?.cited_by_percentile_year) && (
                          <div>
                            {typeof openAlexData[item.id]?.field_citation_ratio === 'number' && (
                              <span className="mr-4">FWCI: {openAlexData[item.id]!.field_citation_ratio!.toFixed(3)}</span>
                            )}
                            {typeof openAlexData[item.id]?.cited_by_percentile_year === 'number' && (
                              <span>Citation percentile: {openAlexData[item.id]!.cited_by_percentile_year!.toFixed(2)}</span>
                            )}
                          </div>
                        )}
                        {/* {openAlexData[item.id]?.authorships && openAlexData[item.id]!.authorships!.length > 0 && (
                          <p>
                            Institutions:{' '}
                            {(() => {
                              const institutionsText = openAlexData[item.id]!.authorships!
                                .flatMap((a) => a.institutions.map((ins) => ins.display_name))
                                .filter(Boolean)
                                .join('; ');
                              
                              const institutionsId = `institutions-${sidx}-${idx}`;
                              const isExpanded = expandedIds.includes(institutionsId);
                              
                              if (institutionsText.length > 200 && !isExpanded) {
                                return (
                                  <>
                                    {institutionsText.substring(0, 200)}...
                                    <Button
                                      variant="link"
                                      size="sm"
                                      className="h-4 px-1"
                                      onClick={() => toggleAbstract(institutionsId)}
                                    >
                                      Show More
                                    </Button>
                                  </>
                                );
                              } else {
                                return (
                                  <>
                                    {institutionsText}
                                    {institutionsText.length > 200 && (
                                      <Button
                                        variant="link"
                                        size="sm"
                                        className="h-4 px-1"
                                        onClick={() => toggleAbstract(institutionsId)}
                                      >
                                        Show Less
                                      </Button>
                                    )}
                                  </>
                                );
                              }
                            })()}
                          </p>
                        )} */}
                        {openAlexData[item.id]?.referencedWorkDetails && (
                          <p>
                            References:{' '}
                            {openAlexData[item.id]?.referencedWorkDetails?.map((r, idx) => (
                              <span key={r.id} className="mr-2">
                                <a href={r.id} target="_blank" rel="noopener noreferrer" className="underline" title={r.display_name}>
                                  {r.display_name}
                                </a>
                                {idx < (openAlexData[item.id]?.referencedWorkDetails?.length || 0) - 1 && '; '}
                              </span>
                            ))}
                          </p>
                        )}
                        {/* {openAlexData[item.id]?.citingWorks && (
                          <p>
                            Cited by works:{' '}
                            {openAlexData[item.id]?.citingWorks?.map((r, idx) => (
                              <span key={r.id} className="mr-2">
                                <a href={r.id} target="_blank" rel="noopener noreferrer" className="underline" title={r.display_name}>
                                  {r.display_name}
                                </a>
                                {idx < (openAlexData[item.id]?.citingWorks?.length || 0) - 1 && '; '}
                              </span>
                            ))}
                          </p>
                        )} */}
                      </div>
                    )}
                    {semanticData[item.id] && (
                      <div className="text-xs text-muted-foreground mb-2 -mt-1 space-y-1">
                        <div className="flex items-center gap-2">
                          <span>
                            Semantic Scholar citations: {
                              semanticData[item.id]!.citationCount !== null 
                                ? (
                                  <a
                                    href={semanticData[item.id]!.url!}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="underline"
                                    title="View on Semantic Scholar"
                                  >
                                    {semanticData[item.id]!.citationCount}
                                  </a>
                                )
                                : 'Not found'
                            }
                            {typeof semanticData[item.id]?.influentialCitationCount === 'number' && 
                             semanticData[item.id]!.influentialCitationCount! > 0 && (
                              <span className="text-amber-600"> ({semanticData[item.id]!.influentialCitationCount} influential)</span>
                            )}
                          </span>
                          {semanticData[item.id]?.pdfUrl &&
                            verifiedPdfUrls[semanticData[item.id]!.pdfUrl!] && (
                              <>
                                <a
                                  href={semanticData[item.id]!.pdfUrl!}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-green-600 hover:text-green-800"
                                  title="Open access PDF"
                                >
                                  <FileText className="h-4 w-4" />
                                </a>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-5 w-5 p-0"
                                  onClick={() =>
                                    setPdfModal({
                                      url: semanticData[item.id]!.pdfUrl!,
                                      title: item.title,
                                      articleUrl: `https://pubmed.ncbi.nlm.nih.gov/${item.id}/`
                                    })
                                  }
                                  aria-label="Preview PDF"
                                >
                                  <Eye className="h-4 w-4 -ml-2" />
                                </Button>
                                <a
                                  href={getDownloadPdfLink(item.id)!}
                                  className="text-green-600 hover:text-green-800"
                                  aria-label="Download PDF"
                                  title="Download PDF"
                                  download
                                >
                                  <Download className="h-4 w-4 -ml-2" />
                                </a>
                              </>
                            )}
                        </div>
                        {semanticData[item.id] &&
                          semanticData[item.id]!.fieldsOfStudy &&
                          semanticData[item.id]!.fieldsOfStudy!.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {semanticData[item.id]!.fieldsOfStudy!
                              .filter((f) => f.toLowerCase() !== 'medicine')
                              .map((f) => (
                                <Badge key={f} variant="secondary" className="px-1">
                                  {f}
                                </Badge>
                              ))}
                          </div>
                        )}
                      </div>
                    )}
                    {unpaywallData[item.id] && (
                      <div className="text-xs text-muted-foreground mb-4 -mt-2 flex items-center gap-2">
                        <span>
                          Open Access Status (Unpaywall): {
                            openAlexData[item.id]?.doi ? (
                              <a
                                href={`https://unpaywall.org/${openAlexData[item.id]!.doi!.replace(/^https?:\/\/(dx\.)?doi\.org\//, '')}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="underline"
                                title="View on Unpaywall"
                              >
                                {unpaywallData[item.id]!.is_oa ? unpaywallData[item.id]!.oa_status : 'closed'}
                              </a>
                            ) : (
                              unpaywallData[item.id]!.is_oa ? unpaywallData[item.id]!.oa_status : 'closed'
                            )
                          }
                        </span>
                        {unpaywallData[item.id]?.pdfUrl &&
                          verifiedPdfUrls[unpaywallData[item.id]!.pdfUrl!] && (
                            <>
                              <a
                                href={unpaywallData[item.id]!.pdfUrl!}
                                target="_blank"
                                rel="noopener noreferrer"
                                className={cn(
                                  "hover:opacity-80 visited:text-blue-700",
                                  unpaywallData[item.id]!.is_oa ? "text-green-600" : "text-amber-600"
                                )}
                                title={unpaywallData[item.id]!.is_oa ? "Open access PDF" : "PDF available"}
                              >
                                <FileText className="h-4 w-4" />
                              </a>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-5 w-5 p-0"
                                onClick={() =>
                                  setPdfModal({
                                    url: unpaywallData[item.id]!.pdfUrl!,
                                    title: item.title,
                                    articleUrl: `https://pubmed.ncbi.nlm.nih.gov/${item.id}/`
                                  })
                                }
                                aria-label="Preview PDF"
                              >
                                <Eye className="h-4 w-4 -ml-2" />
                              </Button>
                              <a
                                href={getDownloadPdfLink(item.id)!}
                                className={cn(
                                  "hover:opacity-80 visited:text-blue-700",
                                  unpaywallData[item.id]!.is_oa ? "text-green-600" : "text-amber-600"
                                )}
                                aria-label="Download PDF"
                                title="Download PDF"
                                download
                              >
                                <Download className="h-4 w-4 -ml-2" />
                              </a>
                            </>
                          )}
                      </div>
                    )}
                    {semanticData[item.id]?.tldr && (
                      <p className="text-sm italic text-blue-500 dark:text-blue-400 mb-2">TLDR: {semanticData[item.id]!.tldr}</p>
                    )}
                    <div className="flex items-center gap-2">
                      <a
                        href={`https://pubmed.ncbi.nlm.nih.gov/${item.id}/`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="group flex items-center text-xs text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                        title="View article on PubMed"
                      >
                        <img
                          src="/icons/pubmed_logo.png"
                          alt="PubMed"
                          className="h-4 w-4"
                        />
                        <span className="ml-1 hidden group-hover:inline">
                          View on PubMed
                        </span>
                      </a>
                      <a
                        href={`https://scholar.google.com/scholar?q=${encodeURIComponent(item.title)}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="group flex items-center text-xs text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                        title="Search on Google Scholar"
                      >
                        <img
                          src="/icons/google_scholar_logo.png"
                          alt="Google Scholar"
                          className="h-4 w-4"
                        />
                        <span className="ml-1 hidden group-hover:inline">
                          View on Google Scholar
                        </span>
                      </a>
                      {openAlexData[item.id]?.id && (
                        <a
                          href={openAlexData[item.id].id}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="group flex items-center text-xs text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                          title="View record on OpenAlex"
                        >
                          <img
                            src="/icons/openalex_logo.png"
                            alt="OpenAlex"
                            className="h-4 w-4"
                          />
                          <span className="ml-1 hidden group-hover:inline">
                            View on OpenAlex
                          </span>
                        </a>
                      )}
                      {semanticData[item.id]?.url && (
                        <a
                          href={semanticData[item.id]!.url!}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="group flex items-center text-xs text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                          title="View on Semantic Scholar"
                        >
                          <img
                            src="/icons/semantic_scholar_logo.png"
                            alt="Semantic Scholar"
                            className="h-6 w-6"
                          />
                          <span className="ml-1 hidden group-hover:inline">
                            View on Semantic Scholar
                          </span>
                        </a>
                      )}
                      {openAlexData[item.id]?.doi && (
                        <a
                          href={`https://unpaywall.org/${openAlexData[item.id]!.doi!.replace(/^https?:\/(dx\.)?doi\.org\//, '')}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="group flex items-center text-xs text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                          title="View on Unpaywall"
                        >
                          <img
                            src="/icons/unpaywall_logo.png"
                            alt="Unpaywall"
                            className="h-4 w-4"
                          />
                          <span className="ml-1 hidden group-hover:inline">
                            View on Unpaywall
                          </span>
                        </a>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="ml-4 h-6 px-2 hover:bg-gray-200 hover:text-primary dark:hover:bg-gray-700"
                        onClick={() => copyCitation(item, `${sidx}-${idx}`)}
                      >
                        {copiedId === `${sidx}-${idx}` ? (
                          <span className="text-xs text-green-600 inline-flex items-center gap-1">
                            <Check className="h-3 w-3" /> Copied
                          </span>
                        ) : (
                          <span className="text-xs inline-flex items-center gap-1">
                            <Clipboard className="h-3 w-3" /> Copy Citation
                          </span>
                        )}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
              
              {search.results.length < search.totalCount && search.results.length > 0 && (
                <div
                  ref={(el) => {
                    sentinelRefs.current[sidx] = el;
                  }}
                  className="flex items-center justify-center h-8"
                >
                  {search.isSearching && (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span className="sr-only">Loading...</span>
                    </>
                  )}
                </div>
              )}
            </div>
          </div>
            );
          }
          if (search.hasRun) {
            return (
              <div key={search.id} className="border p-6 rounded-lg">
                <div className="font-bold text-lg mb-4 truncate pr-6" title={search.submittedQuery}>
                  {search.submittedQuery}
                </div>
                <p>0 results were found.</p>
              </div>
            );
          }
          return null;
        })}
      </div>
        </div>
      </div>
      {hoverTooltip && (
        <div
          className="fixed z-50 bg-white dark:bg-gray-800 border rounded p-2 text-xs max-w-xs"
          style={{ left: hoverTooltip.x + 10, top: hoverTooltip.y + 10 }}
          onMouseEnter={cancelHideTooltip}
          onMouseLeave={() => setHoverTooltip(null)}
        >
          <p className="mb-1">
            {hoverTooltip.label}: {hoverTooltip.articles.length}{" "}
            {hoverTooltip.articles.length === 1 ? "article" : "articles"}
          </p>
          <ul className="max-h-40 overflow-y-auto list-disc pl-4 space-y-1">
            {hoverTooltip.articles.map((a) => (
              <li key={a.id}>
                <a
                  href={`https://pubmed.ncbi.nlm.nih.gov/${a.id}/`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-amber-600 underline"
                >
                  {a.title}
                </a>
              </li>
            ))}
          </ul>
        </div>
      )}
      {compareItems.length > 1 && (
        <Button
          onClick={scrollToComparison}
          className="fixed bottom-4 right-4 z-50 rounded-full bg-amber-400 text-black hover:bg-amber-500 shadow-md"
          size="sm"
        >
          View Comparison
        </Button>
      )}
      <Dialog open={!!pdfModal} onOpenChange={(open) => !open && setPdfModal(null)}>
        <DialogContent
          className={cn(
            "max-w-screen-xl max-h-[95vh] w-full",
            pdfOnTop ? "z-[100001]" : "z-40"
          )}
          overlayClassName={pdfOnTop ? "z-[100000]" : "z-30"}
        >
          <DialogHeader>
            <DialogTitle>{pdfModal?.title}</DialogTitle>
          </DialogHeader>
          <div className="h-[80vh]">
            {pdfModal && (
              <PagePdfViewer
                url={pdfModal.url}
                title={pdfModal.title}
                articleUrl={pdfModal.articleUrl}
                pageInput={pageInputs[pdfModal.url] ?? 1}
                onPageChange={(val) =>
                  setPageInputs((p) => ({ ...p, [pdfModal.url]: val }))
                }
                onSave={() => {}}
                onClose={() => setPdfModal(null)}
              />
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
    </>
  );
}

// Main component with Suspense boundary
export default function PubMedExplorerPage() {
  const [top, setTop] = useState<'clipboard' | 'pdf' | null>(null);

  return (
    <ClipboardProvider storageKey="pubmedNotes">
      <Suspense fallback={null}>
        <PubMedExplorerContent
          bringPdfToFront={() => setTop('pdf')}
          pdfOnTop={top === 'pdf'}
        />
      </Suspense>
      <ClipboardPanel
        zIndex={top === 'clipboard' ? 100001 : 99999}
        onFocus={() => setTop('clipboard')}
      />
    </ClipboardProvider>
  );
}
  
