

"use client";

import React, { useState, useMemo, useEffect } from "react";
import Link from "next/link";
import {
  ArrowLeft,
  Search,
  Save,
  Download,
  Clipboard,
  X,
  Bookmark,
  BookmarkCheck,
  Plus,
} from "lucide-react";
import axios from "axios";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ThemeToggle } from "@/components/ThemeToggle";
import { useLocalStorage } from "@/hooks/use-local-storage";
import { useToast } from "@/components/ui/use-toast";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as ReTooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  LabelList,
  Treemap,
} from "recharts";

interface PubMedResult {
  id: string;
  title: string;
  pubdate?: string;
  journal?: string;
  authors?: string;
  abstract?: string;
}

interface SavedQuery {
  query: string;
  startYear?: string;
  endYear?: string;
  sort: string;
  author?: string;
  journal?: string;
}

interface JournalDataItem {
  journal: string;
  value: number;
}

interface KeywordTreeDataItem {
  name: string;
  size: number;
  fill: string;
}

interface SearchItem {
  id: number;
  query: string;
  author: string;
  journal: string;
  startYear: string;
  endYear: string;
  sort: string;
  results: PubMedResult[];
  page: number;
  totalCount: number;
  isSearching: boolean;
  hasRun: boolean;
}

export default function PubMedExplorerPage() {
  // Add this to prevent hydration mismatches
  const [mounted, setMounted] = useState(false);
  
  // Search items state - allow up to three simultaneous searches
  const [searches, setSearches] = useState<SearchItem[]>([
    {
      id: Date.now(),
      query: "",
      author: "",
      journal: "",
      startYear: "",
      endYear: "",
      sort: "relevance",
      results: [],
      page: 1,
      totalCount: 0,
      isSearching: false,
      hasRun: false,
    },
  ]);
  const [savedQueries, setSavedQueries] = useLocalStorage<SavedQuery[]>(
    "pubmedSavedQueries",
    [],
  );
  const [bookmarks, setBookmarks] = useLocalStorage<PubMedResult[]>(
    "pubmedBookmarks",
    [],
  );
  const [expandedIds, setExpandedIds] = useState<string[]>([]);
  const [compareIds, setCompareIds] = useState<string[]>([]);
  const [expandedCompareIds, setExpandedCompareIds] = useState<string[]>([]);
  const [viewType, setViewType] = useState<"bar" | "line">("bar");
  const [keywordView, setKeywordView] = useState<"badges" | "treemap">("badges");
  const [copiedId, setCopiedId] = useState<string | null>(null);
  const [journalCounts, setJournalCounts] = useState<number[]>([5]);
  const [authorCounts, setAuthorCounts] = useState<number[]>([5]);
  const { toast } = useToast();

  // Set mounted to true after initial render
  useEffect(() => {
    setMounted(true);
  }, []);

  const highlightText = (text: string, q: string) => {
    const words = q
      .split(/\s+/)
      .map((w) => w.replace(/[^\w]/g, ""))
      .filter(Boolean);
    if (words.length === 0) return text;
    const pattern = new RegExp(`(${words.join("|")})`, "gi");
    return text.split(pattern).map((part, i) =>
      pattern.test(part) ? (
        <mark key={i} className="bg-amber-200 dark:bg-amber-700">
          {part}
        </mark>
      ) : (
        part
      ),
    );
  };

  const YearTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const url = `https://pubmed.ncbi.nlm.nih.gov/?term=${label}[dp]`;
      return (
        <div className="bg-white p-2 border rounded text-xs dark:bg-gray-800">
          <p>
            {label}: {payload[0].value}
          </p>
          <a
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            className="text-amber-600 underline"
          >
            View articles
          </a>
        </div>
      );
    }
    return null;
  };

  const JournalTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const { journal, value } = payload[0].payload;
      const url = `https://pubmed.ncbi.nlm.nih.gov/?term=${encodeURIComponent(journal)}[jour]`;
      return (
        <div className="bg-white p-2 border rounded text-xs dark:bg-gray-800">
          <p>
            {journal}: {value}
          </p>
          <a
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            className="text-amber-600 underline"
          >
            View articles
          </a>
        </div>
      );
    }
    return null;
  };

  const AuthorTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const { author, value } = payload[0].payload;
      const url = `https://pubmed.ncbi.nlm.nih.gov/?term=${encodeURIComponent(author)}[auth]`;
      return (
        <div className="bg-white p-2 border rounded text-xs dark:bg-gray-800">
          <p>
            {author}: {value}
          </p>
          <a
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            className="text-amber-600 underline"
          >
            View articles
          </a>
        </div>
      );
    }
    return null;
  };

  const KeywordTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const { name, size } = payload[0].payload;
      const url = `https://pubmed.ncbi.nlm.nih.gov/?term=${encodeURIComponent(name)}`;
      return (
        <div className="bg-white p-2 border rounded text-xs dark:bg-gray-800">
          <p>
            {name}: {size}
          </p>
          <a
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            className="text-amber-600 underline"
          >
            View articles
          </a>
        </div>
      );
    }
    return null;
  };

  const renderAuthorTick = ({ x, y, payload }: any) => (
    <text x={x} y={y} textAnchor="end" dominantBaseline="middle" className="whitespace-nowrap">
      {payload.value}
    </text>
  );

  const allResults = useMemo(() => searches.flatMap((s) => s.results), [searches]);

  // Chart data will be calculated individually for each search

  const getYearData = (results: PubMedResult[]) => {
    const counts: Record<string, number> = {};
    results.forEach((r) => {
      const match = r.pubdate?.match(/\d{4}/);
      if (match) {
        counts[match[0]] = (counts[match[0]] || 0) + 1;
      }
    });
    return Object.entries(counts)
      .map(([year, count]) => ({ year, count }))
      .sort((a, b) => parseInt(a.year) - parseInt(b.year));
  };

  const getJournalData = (results: PubMedResult[]) => {
    const counts: Record<string, number> = {};
    results.forEach((r) => {
      if (r.journal) counts[r.journal] = (counts[r.journal] || 0) + 1;
    });
    return Object.entries(counts)
      .map(([journal, value]) => ({ journal, value }))
      .sort((a, b) => b.value - a.value);
  };

  const getAuthorData = (results: PubMedResult[]) => {
    const counts: Record<string, number> = {};
    results.forEach((r) => {
      if (r.authors) {
        r.authors.split(",").forEach((a) => {
          const name = a.trim();
          if (name) counts[name] = (counts[name] || 0) + 1;
        });
      }
    });
    return Object.entries(counts)
      .map(([author, value]) => ({ author, value }))
      .sort((a, b) => b.value - a.value);
  };

  const compareItems = useMemo(
    () => {
      if (!mounted) return [];
      return allResults.filter((r) => compareIds.includes(r.id));
    },
    [allResults, compareIds, mounted]
  );

  const saveCurrentQuery = (idx: number) => {
    const s = searches[idx];
    if (!s.query.trim()) {
      toast({
        title: "Search query required",
        description: "Enter a query before saving",
        variant: "destructive",
      });
      return;
    }
    const newQuery: SavedQuery = {
      query: s.query,
      startYear: s.startYear || undefined,
      endYear: s.endYear || undefined,
      sort: s.sort,
      author: s.author || undefined,
      journal: s.journal || undefined,
    };
    setSavedQueries([...savedQueries, newQuery]);
    toast({ title: "Search saved" });
  };

  const loadSavedQuery = (sq: SavedQuery) => {
    const emptyIndex = searches.findIndex((s) => !s.query.trim());
    if (emptyIndex !== -1) {
      setSearches((prev) =>
        prev.map((p, i) => (i === emptyIndex ? { ...p, query: sq.query } : p)),
      );
      search(emptyIndex, 1, sq.query);
    } else if (searches.length < 3) {
      setSearches((prev) => [
        ...prev,
        {
          id: Date.now(),
          query: sq.query,
          author: "",
          journal: "",
          startYear: "",
          endYear: "",
          sort: sq.sort || "relevance",
          results: [],
          page: 1,
          totalCount: 0,
          isSearching: false,
          hasRun: false,
        },
      ]);
      setJournalCounts((prev) => [...prev, 5]);
      setAuthorCounts((prev) => [...prev, 5]);
      search(searches.length, 1, sq.query);
    }
  };

  const removeSavedQuery = (index: number) => {
    setSavedQueries(savedQueries.filter((_, i) => i !== index));
  };

  const exportCsv = () => {
    if (allResults.length === 0) return;
    const header = [
      "PMID",
      "Title",
      "Publication Date",
      "Journal",
      "Authors",
      "Abstract",
    ];
    const rows = allResults.map((r) =>
      [
        r.id,
        r.title.replace(/\"/g, '""'),
        r.pubdate || "",
        r.journal || "",
        r.authors ? r.authors.replace(/\"/g, '""') : "",
        r.abstract ? r.abstract.replace(/\"/g, '""') : "",
      ]
        .map((v) => `"${v}"`)
        .join(","),
    );
    const csvContent = [header.join(","), ...rows].join("\n");
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `pubmed-results-${Date.now()}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const exportSearchCsv = (idx: number) => {
    const results = searches[idx]?.results || [];
    if (results.length === 0) return;
    const header = [
      "PMID",
      "Title",
      "Publication Date",
      "Journal",
      "Authors",
      "Abstract",
    ];
    const rows = results.map((r) =>
      [
        r.id,
        r.title.replace(/\"/g, '""'),
        r.pubdate || "",
        r.journal || "",
        r.authors ? r.authors.replace(/\"/g, '""') : "",
        r.abstract ? r.abstract.replace(/\"/g, '""') : "",
      ]
        .map((v) => `"${v}"`)
        .join(","),
    );
    const csvContent = [header.join(","), ...rows].join("\n");
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `pubmed-results-${idx + 1}-${Date.now()}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const exportComparisonCsv = () => {
    if (compareItems.length === 0) return;
    const header = ["PMID", "Title", "Publication Date", "Journal", "Authors", "Abstract"];
    const rows = compareItems.map((r) =>
      [
        r.id,
        r.title.replace(/\"/g, '""'),
        r.pubdate || "",
        r.journal || "",
        r.authors ? r.authors.replace(/\"/g, '""') : "",
        r.abstract ? r.abstract.replace(/\"/g, '""') : "",
      ]
        .map((v) => `"${v}"`)
        .join(","),
    );
    const csvContent = [header.join(","), ...rows].join("\n");
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `pubmed-comparison-${Date.now()}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const copyCitation = (item: PubMedResult, id: string) => {
    const citation = `${item.authors ? item.authors + ". " : ""}${item.title}. ${
      item.journal ? item.journal + ". " : ""
    }${item.pubdate ? item.pubdate + ". " : ""}PMID: ${item.id}.`;
    navigator.clipboard.writeText(citation).then(() => {
      setCopiedId(id);
      toast({ title: "Citation copied" });
      setTimeout(() => setCopiedId(null), 2000);
    });
  };

  const toggleBookmark = (item: PubMedResult) => {
    const exists = bookmarks.find((p) => p.id === item.id);
    if (exists) {
      setBookmarks(bookmarks.filter((p) => p.id !== item.id));
    } else {
      setBookmarks([...bookmarks, item]);
    }
  };

  const toggleCompare = (id: string) => {
    if (compareIds.includes(id)) {
      setCompareIds(compareIds.filter((c) => c !== id));
    } else {
      setCompareIds([...compareIds, id]);
    }
  };

  const removeSearch = (id: number) => {
    setSearches((prev) => {
      const idx = prev.findIndex((s) => s.id === id);
      if (idx === -1) return prev;
      setJournalCounts((c) => c.filter((_, i) => i !== idx));
      setAuthorCounts((c) => c.filter((_, i) => i !== idx));
      return prev.filter((s) => s.id !== id);
    });
  };

  const toggleAbstract = (id: string) => {
    if (expandedIds.includes(id)) {
      setExpandedIds(expandedIds.filter((i) => i !== id));
    } else {
      setExpandedIds([...expandedIds, id]);
    }
  };

  const toggleCompareAbstract = (id: string) => {
    if (expandedCompareIds.includes(id)) {
      setExpandedCompareIds(expandedCompareIds.filter((i) => i !== id));
    } else {
      setExpandedCompareIds([...expandedCompareIds, id]);
    }
  };

  const clearFilters = (idx: number) => {
    setSearches((prev) =>
      prev.map((s, i) =>
        i === idx
          ? { ...s, author: "", journal: "", startYear: "", endYear: "", sort: "relevance" }
          : s,
      ),
    );
  };

  const clearComparison = () => {
    setCompareIds([]);
  };
  const search = async (idx: number, pageOverride?: number, queryOverride?: string) => {
    const item = searches[idx];
    const q = queryOverride ?? item.query;
    if (!q.trim()) return;
    setSearches((prev) => prev.map((s, i) => (i === idx ? { ...s, isSearching: true } : s)));
    try {
      const response = await axios.get("/api/pubmed-search", {
        params: {
          query: q,
          page: pageOverride || item.page,
          pageSize: 20,
          startYear: item.startYear || undefined,
          endYear: item.endYear || undefined,
          sort: item.sort,
          author: item.author || undefined,
          journal: item.journal || undefined,
        },
      });
      setSearches((prev) =>
        prev.map((s, i) => {
          if (i !== idx) return s;
          const newResults = pageOverride && pageOverride > 1 ? [...s.results, ...response.data.results] : response.data.results;
          return {
            ...s,
            results: newResults,
            totalCount: response.data.count,
            page: pageOverride || s.page,
            isSearching: false,
            hasRun: true,
          };
        }),
      );
    } catch (error) {
      console.error("PubMed search error", error);
      setSearches((prev) =>
        prev.map((s, i) =>
          i === idx ? { ...s, results: pageOverride && pageOverride > 1 ? s.results : [], isSearching: false, hasRun: true } : s,
        ),
      );
    }
  };

  const handleSubmit = (e: React.FormEvent, idx: number) => {
    e.preventDefault();
    setSearches((prev) => prev.map((s, i) => (i === idx ? { ...s, page: 1 } : s)));
    search(idx, 1);
  };

  const loadMore = (idx: number) => {
    const nextPage = searches[idx].page + 1;
    search(idx, nextPage);
  };

  // Only render the full component when mounted
  if (!mounted) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="flex justify-between items-center mb-6">
          <Link href="/">
            <Button variant="ghost" className="pl-0 hover:bg-transparent">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Evicenter
            </Button>
          </Link>
          <ThemeToggle />
        </div>
        <h1 className="text-3xl font-bold mb-6">PubMed Explorer</h1>
        <div className="prose max-w-none mb-8 dark:text-gray-200">
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex justify-between items-center mb-6">
        <Link href="/">
          <Button variant="ghost" className="pl-0 hover:bg-transparent">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Evicenter
          </Button>
        </Link>
        <ThemeToggle />
      </div>

      <h1 className="text-3xl font-bold mb-6">PubMed Explorer</h1>

      <div className="prose max-w-none mb-8 dark:text-gray-200">
        <p>Search the PubMed database and filter by publication year.</p>
      </div>

      <div className="space-y-4 mb-8">
        {searches.map((s, idx) => (
          <form
            key={s.id}
            onSubmit={(e) => handleSubmit(e, idx)}
            className="flex flex-wrap gap-2 items-end"
          >
            <Input
              type="search"
              placeholder="Enter keywords..."
              value={s.query}
              onChange={(e) =>
                setSearches((prev) =>
                  prev.map((p, i) => (i === idx ? { ...p, query: e.target.value } : p)),
                )
              }
              className="flex-1 min-w-[200px]"
            />
            <Input
              type="number"
              placeholder="Start year"
              value={s.startYear}
              onChange={(e) =>
                setSearches((prev) => prev.map((p, i) => (i === idx ? { ...p, startYear: e.target.value } : p)))
              }
              className="w-32"
            />
            <Input
              type="number"
              placeholder="End year"
              value={s.endYear}
              onChange={(e) =>
                setSearches((prev) => prev.map((p, i) => (i === idx ? { ...p, endYear: e.target.value } : p)))
              }
              className="w-32"
            />
            <Input
              type="text"
              placeholder="Author"
              value={s.author}
              onChange={(e) =>
                setSearches((prev) => prev.map((p, i) => (i === idx ? { ...p, author: e.target.value } : p)))
              }
              className="w-40"
            />
            <Input
              type="text"
              placeholder="Journal"
              value={s.journal}
              onChange={(e) =>
                setSearches((prev) => prev.map((p, i) => (i === idx ? { ...p, journal: e.target.value } : p)))
              }
              className="w-40"
            />
            <select
              value={s.sort}
              onChange={(e) =>
                setSearches((prev) => prev.map((p, i) => (i === idx ? { ...p, sort: e.target.value } : p)))
              }
              className="border rounded-md px-2 py-2 text-sm h-10 text-black dark:text-white"
            >
              <option value="relevance">Relevance</option>
              <option value="date">Most Recent</option>
            </select>
            <select
              value={viewType}
              onChange={(e) => setViewType(e.target.value as "bar" | "line")}
              className="border rounded-md px-2 py-2 text-sm h-10 text-black dark:text-white"
            >
              <option value="bar">Bar Chart</option>
              <option value="line">Line Chart</option>
            </select>
            <Button
              type="submit"
              className="bg-amber-400 text-black hover:bg-amber-500"
              disabled={s.isSearching}
            >
              {s.isSearching ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black" />
              ) : (
                <>
                  <Search className="h-4 w-4 mr-2" /> Search
                </>
              )}
            </Button>
            <Button
              type="button"
              onClick={() => saveCurrentQuery(idx)}
              variant="outline"
              className="flex items-center gap-1"
            >
              <Save className="h-4 w-4" /> Save
            </Button>
            <Button type="button" onClick={() => clearFilters(idx)} variant="outline">
              Clear Filters
            </Button>
            {searches.length > 1 && (
              <Button type="button" variant="ghost" onClick={() => removeSearch(s.id)}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </form>
        ))}
        {searches.length < 3 && (
          <Button
            variant="outline"
            onClick={() => {
              setSearches((prev) => [
                ...prev,
                {
                  id: Date.now(),
                  query: "",
                  author: "",
                  journal: "",
                  startYear: "",
                  endYear: "",
                  sort: "relevance",
                  results: [],
                  page: 1,
                  totalCount: 0,
                  isSearching: false,
                  hasRun: false,
                },
              ]);
              setJournalCounts((prev) => [...prev, 5]);
              setAuthorCounts((prev) => [...prev, 5]);
            }}
          >
            <Plus className="h-4 w-4 mr-2" /> Add Search
          </Button>
        )}
      </div>

      {savedQueries.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-bold mb-2">Saved Searches</h2>
          <div className="flex flex-wrap gap-2">
            {savedQueries.map((sq, idx) => (
              <div key={idx} className="flex items-center gap-1 border rounded px-2 py-0">
                {/* adjust py value above to change saved search box height */}
                <Button
                  variant="link"
                  className="p-0 text-sm"
                  onClick={() => loadSavedQuery(sq)}
                >
                  {sq.query}
                </Button>
                <button
                  onClick={() => removeSavedQuery(idx)}
                  className="text-muted-foreground hover:text-red-500"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {bookmarks.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-bold mb-2">Bookmarked Articles</h2>
          <div className="space-y-2">
            {bookmarks.map((b, idx) => (
              <div key={idx} className="flex items-center gap-2">
                <a
                  href={`https://pubmed.ncbi.nlm.nih.gov/${b.id}/`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                >
                  {b.title}
                </a>
                <button
                  onClick={() => toggleBookmark(b)}
                  className="text-muted-foreground hover:text-red-500"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}



      {allResults.length > 0 && (
        <div className="mb-8 space-y-4">
          <div className="flex justify-between">
            <Button variant="outline" onClick={exportCsv} className="gap-1">
              <Download className="h-4 w-4" /> Export CSV
            </Button>
          </div>
        </div>
      )}

      {compareItems.length > 1 && (
        <div className="mb-8 overflow-auto space-y-2">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold">Comparison</h2>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={exportComparisonCsv} className="gap-1">
                <Download className="h-4 w-4" /> Export
              </Button>
              <Button variant="outline" size="sm" onClick={clearComparison}>
                Clear
              </Button>
            </div>
          </div>
          <table className="min-w-full text-sm border rounded-md overflow-hidden">
            <thead className="bg-muted">
              <tr>
                <th className="border px-3 py-2 w-32 text-left">Field</th>
                {compareItems.map((c) => (
                  <th key={c.id} className="border px-3 py-2 w-60 text-left">
                    {c.title}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-border">
              <tr>
                <td className="border px-3 py-2 font-medium w-32">Journal</td>
                {compareItems.map((c) => (
                  <td key={c.id} className="border px-3 py-2 w-60">{c.journal}</td>
                ))}
              </tr>
              <tr>
                <td className="border px-3 py-2 font-medium w-32">Pub Date</td>
                {compareItems.map((c) => (
                  <td key={c.id} className="border px-3 py-2 w-60">{c.pubdate}</td>
                ))}
              </tr>
              <tr>
                <td className="border px-3 py-2 font-medium w-32">PMID</td>
                {compareItems.map((c) => (
                  <td key={c.id} className="border px-3 py-2 w-60">
                    <a
                      href={`https://pubmed.ncbi.nlm.nih.gov/${c.id}/`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-amber-600 underline"
                    >
                      {c.id}
                    </a>
                  </td>
                ))}
              </tr>
              <tr>
                <td className="border px-3 py-2 font-medium w-32">Authors</td>
                {compareItems.map((c) => (
                  <td key={c.id} className="border px-3 py-2 w-60">{c.authors}</td>
                ))}
              </tr>
              <tr>
                <td className="border px-3 py-2 font-medium w-32">Abstract</td>
                {compareItems.map((c) => (
                  <td key={c.id} className="border px-3 py-2 w-60">
                    {c.abstract && c.abstract.length > 250 && !expandedCompareIds.includes(c.id) ? (
                      <>
                        {c.abstract.substring(0, 250)}...
                        <Button variant="link" size="sm" className="h-4 px-1" onClick={() => toggleCompareAbstract(c.id)}>
                          Show More
                        </Button>
                      </>
                    ) : (
                      <>
                        {c.abstract}
                        {c.abstract && c.abstract.length > 250 && (
                          <Button variant="link" size="sm" className="h-4 px-1" onClick={() => toggleCompareAbstract(c.id)}>
                            Show Less
                          </Button>
                        )}
                      </>
                    )}
                  </td>
                ))}
              </tr>
              <tr>
                <td className="border px-3 py-2 font-medium w-32">Actions</td>
                {compareItems.map((c) => (
                  <td key={c.id} className="border px-3 py-2 w-60 space-x-1">
                    <Button variant="ghost" size="sm" className="h-6 px-2" onClick={() => copyCitation(c, c.id)}>
                      {copiedId === c.id ? <span className="text-xs text-green-600">Copied</span> : <Clipboard className="h-3 w-3" />}
                    </Button>
                    <Button variant="ghost" size="sm" className="h-6 px-2" onClick={() => toggleBookmark(c)}>
                      {bookmarks.some((b) => b.id === c.id) ? (
                        <BookmarkCheck className="h-4 w-4 text-amber-500" />
                      ) : (
                        <Bookmark className="h-4 w-4" />
                      )}
                    </Button>
                    <Button variant="ghost" size="sm" className="h-6 px-2" onClick={() => toggleCompare(c.id)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </td>
                ))}
              </tr>
            </tbody>
          </table>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {searches.map((search, sidx) => search.results.length > 0 && (
          <div key={search.id} className="space-y-8 border p-6 rounded-lg">
            <div className="font-bold text-lg mb-4 truncate" title={search.query}>
              {search.query}
            </div>
            
            {/* Year distribution chart for this search */}
            <div className="h-64 w-full mb-8">
              <h3 className="text-sm font-medium mb-2">Publication Years</h3>
              <ResponsiveContainer width="100%" height="85%">
                {viewType === "bar" ? (
                  <BarChart
                    data={getYearData(search.results)}
                    margin={{ top: 5, right: 10, left: 0, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="year" />
                    <YAxis allowDecimals={false} />
                    <ReTooltip content={YearTooltip} />
                    <Bar
                      dataKey="count"
                      fill="#f59e0b"
                      onClick={(data) => {
                        if (data && data.payload?.year) {
                          window.open(
                            `https://pubmed.ncbi.nlm.nih.gov/?term=${data.payload.year}[dp]`,
                            "_blank",
                          );
                        }
                      }}
                    />
                  </BarChart>
                ) : (
                  <LineChart
                    data={getYearData(search.results)}
                    margin={{ top: 5, right: 10, left: 0, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="year" />
                    <YAxis allowDecimals={false} />
                    <ReTooltip content={YearTooltip} />
                    <Line
                      type="monotone"
                      dataKey="count"
                      stroke="#f59e0b"
                      activeDot={{
                        onClick: (_e: any, payload: any) => {
                          if (payload?.payload?.year) {
                            window.open(
                              `https://pubmed.ncbi.nlm.nih.gov/?term=${payload.payload.year}[dp]`,
                              "_blank",
                            );
                          }
                        },
                      }}
                    />
                  </LineChart>
                )}
              </ResponsiveContainer>
            </div>
            
            {/* Journal chart for this search */}
            {getJournalData(search.results).length > 0 && (
              <div className="mb-8">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-sm font-medium">Top Journals</h3>
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() =>
                        setJournalCounts((c) =>
                          c.map((v, i) => (i === sidx ? Math.min(v + 5, getJournalData(search.results).length) : v)),
                        )
                      }
                      disabled={journalCounts[sidx] >= getJournalData(search.results).length}
                    >
                      Show 5 More
                    </Button>
                    {journalCounts[sidx] > 5 && (
                      <Button size="sm" variant="outline" onClick={() =>
                        setJournalCounts((c) => c.map((v, i) => (i === sidx ? 5 : v)))
                      }>
                        Top 5
                      </Button>
                    )}
                  </div>
                </div>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={getJournalData(search.results).slice(0, journalCounts[sidx] || 5)}
                      layout="vertical"
                      margin={{ top: 5, right: 30, left: 120, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" allowDecimals={false} />
                      <YAxis 
                        dataKey="journal" 
                        type="category" 
                        width={120}
                        tick={(props) => {
                          const { x, y, payload } = props;
                          return (
                            <g transform={`translate(${x},${y})`}>
                              <title>{payload.value}</title>
                              <text x={0} y={0} dy={4} textAnchor="end" fontSize={10} fill="#666">
                                {payload.value.length > 20 ? `${payload.value.substring(0, 18)}...` : payload.value}
                              </text>
                            </g>
                          );
                        }}
                      />
                      <ReTooltip content={JournalTooltip} />
                      <Bar
                        dataKey="value"
                        fill="#f59e0b"
                        onClick={(data) => {
                          if (data && data.payload?.journal) {
                            window.open(
                              `https://pubmed.ncbi.nlm.nih.gov/?term=${encodeURIComponent(data.payload.journal)}[jour]`,
                              "_blank",
                            );
                          }
                        }}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>
            )}
            
            {/* Author chart for this search */}
            {getAuthorData(search.results).length > 0 && (
              <div className="mb-8">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-sm font-medium">Top Authors</h3>
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() =>
                        setAuthorCounts((c) =>
                          c.map((v, i) => (i === sidx ? Math.min(v + 5, getAuthorData(search.results).length) : v)),
                        )
                      }
                      disabled={authorCounts[sidx] >= getAuthorData(search.results).length}
                    >
                      Show 5 More
                    </Button>
                    {authorCounts[sidx] > 5 && (
                      <Button size="sm" variant="outline" onClick={() =>
                        setAuthorCounts((c) => c.map((v, i) => (i === sidx ? 5 : v)))
                      }>
                        Top 5
                      </Button>
                    )}
                  </div>
                </div>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={getAuthorData(search.results).slice(0, authorCounts[sidx] || 5)}
                      layout="vertical"
                      margin={{ top: 5, right: 30, left: 120, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" allowDecimals={false} />
                      <YAxis 
                        dataKey="author" 
                        type="category" 
                        width={120}
                        tick={(props) => {
                          const { x, y, payload } = props;
                          return (
                            <g transform={`translate(${x},${y})`}>
                              <title>{payload.value}</title>
                              <text x={0} y={0} dy={4} textAnchor="end" fontSize={10} fill="#666">
                                {payload.value.length > 20 ? `${payload.value.substring(0, 18)}...` : payload.value}
                              </text>
                            </g>
                          );
                        }}
                      />
                      <ReTooltip content={AuthorTooltip} />
                      <Bar
                        dataKey="value"
                        fill="#10b981"
                        onClick={(data) => {
                          if (data && data.payload?.author) {
                            window.open(
                              `https://pubmed.ncbi.nlm.nih.gov/?term=${encodeURIComponent(data.payload.author)}[auth]`,
                              "_blank",
                            );
                          }
                        }}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>
            )}
            
            {/* Export Results button */}
            <div className="flex justify-end mb-6">
              <Button
                variant="outline"
                size="sm"
                onClick={() => exportSearchCsv(sidx)}
                className="gap-1"
              >
                <Download className="h-4 w-4" /> Export Results
              </Button>
            </div>
            
            {/* Results for this search */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium">Search Results ({search.results.length})</h3>
              {search.results.map((item, idx) => (
                <Card key={`${sidx}-${idx}`}>
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <CardTitle className="text-lg flex-1 pr-2">
                        {highlightText(item.title, search.query)}
                      </CardTitle>
                      <div className="flex items-center gap-2">
                        <Checkbox
                          checked={compareIds.includes(item.id)}
                          onCheckedChange={() => toggleCompare(item.id)}
                        />
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleBookmark(item)}
                          className="h-6 px-2 hover:bg-transparent"
                        >
                          {bookmarks.some((b) => b.id === item.id) ? (
                            <BookmarkCheck className="h-4 w-4 text-amber-500" />
                          ) : (
                            <Bookmark className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {(item.journal || item.pubdate) && (
                      <p className="text-sm text-muted-foreground mb-1">
                        {item.journal} {item.pubdate ? `• ${item.pubdate}` : ""}
                      </p>
                    )}
                    {item.authors && (
                      <p className="text-sm text-muted-foreground mb-2">
                        {item.authors}
                      </p>
                    )}
                    {item.abstract && (
                      <div className="text-sm mb-2">
                        {highlightText(
                          expandedIds.includes(`${sidx}-${idx}`)
                            ? item.abstract
                            : `${item.abstract.substring(0, 250)}...`,
                          search.query,
                        )}
                        <Button
                          variant="link"
                          size="sm"
                          className="h-4 px-1"
                          onClick={() => toggleAbstract(`${sidx}-${idx}`)}
                        >
                          {expandedIds.includes(`${sidx}-${idx}`) ? "Show Less" : "Show More"}
                        </Button>
                      </div>
                    )}
                    <div className="flex items-center gap-2">
                      <a
                        href={`https://pubmed.ncbi.nlm.nih.gov/${item.id}/`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-xs text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                      >
                        View on PubMed
                      </a>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 px-2 hover:bg-transparent"
                        onClick={() => copyCitation(item, `${sidx}-${idx}`)}
                      >
                        {copiedId === `${sidx}-${idx}` ? (
                          <span className="text-xs text-green-600">Copied</span>
                        ) : (
                          <span className="text-xs inline-flex items-center gap-1">
                            <Clipboard className="h-3 w-3" /> Copy Citation
                          </span>
                        )}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
              
              {search.results.length < search.totalCount && search.results.length > 0 && (
                <div className="mt-4 text-center">
                  <Button
                    onClick={() => loadMore(sidx)}
                    className="bg-amber-400 text-black hover:bg-amber-500"
                    disabled={search.isSearching}
                  >
                    Load More
                  </Button>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
