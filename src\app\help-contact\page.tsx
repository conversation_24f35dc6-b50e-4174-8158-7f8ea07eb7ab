"use client";

import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft, Mail, Phone, MessageSquare, HelpCircle, FileText } from "lucide-react";
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { useToast } from "@/components/ui/use-toast";

export default function HelpContactPage() {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    category: "",
    message: ""
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (value: string) => {
    setFormData(prev => ({ ...prev, category: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form submitted:", formData);
    
    // Show success toast
    toast({
      title: "Message sent",
      description: "We've received your message and will respond shortly.",
    });
    
    // Reset form
    setFormData({
      name: "",
      email: "",
      subject: "",
      category: "",
      message: ""
    });
  };

  const faqs = [
    {
      question: "How do I contact an agency?",
      answer: "To contact an agency, select them from the agency list on the main page, compose your message in the right panel, and click either 'Open in Native App' or 'Open Outlook (Web)' to send your email."
    },
    {
      question: "Can I contact multiple agencies at once?",
      answer: "Yes, you can select multiple agencies by checking the boxes next to their names. Your message will be sent to all selected agencies using BCC for privacy."
    },
    {
      question: "How do I become a featured agency?",
      answer: "To become a featured agency, visit our 'Featured Agencies' page."
    },
    // {
    //   question: "How can I update my agency information?",
    //   answer: "To update your agency information, log in to your vendor account, navigate to 'Profile Settings', and make the necessary changes. All updates will be reviewed by our team before going live."
    // },
    {
      question: "Is there a limit to how many agencies I can contact?",
      answer: "There is no limit to how many agencies you can contact. However, we recommend being selective and reaching out only to agencies whose services match your specific needs."
    }
  ];

  return (
    <div className="container max-w-4xl mx-auto py-8 px-4">
      <Link href="/">
        <Button variant="ghost" className="mb-6 pl-0 hover:bg-transparent">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Evicenter
        </Button>
      </Link>
      
      <h1 className="text-3xl font-bold mb-6">Help & Contact</h1>
      
      <div className="grid md:grid-cols-2 gap-8 mb-10">
        <div>
          <h2 className="text-2xl font-semibold mb-4">Contact Us</h2>
          <p className="mb-6">
            Have questions or need assistance? Fill out the form below and our team will get back to you as soon as possible.
          </p>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <Input 
                id="name" 
                name="name" 
                value={formData.name} 
                onChange={handleChange} 
                placeholder="Your name" 
                required 
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input 
                id="email" 
                name="email" 
                type="email" 
                value={formData.email} 
                onChange={handleChange} 
                placeholder="Your email address" 
                required 
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="subject">Subject</Label>
              <Input 
                id="subject" 
                name="subject" 
                value={formData.subject} 
                onChange={handleChange} 
                placeholder="Subject of your inquiry" 
                required 
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Select onValueChange={handleSelectChange} value={formData.category}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="general">General Inquiry</SelectItem>
                  <SelectItem value="technical">Technical Support</SelectItem>
                  <SelectItem value="billing">Billing & Payments</SelectItem>
                  <SelectItem value="feature">Feature Request</SelectItem>
                  <SelectItem value="vendor">Agency Support</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="message">Message</Label>
              <Textarea 
                id="message" 
                name="message" 
                value={formData.message} 
                onChange={handleChange} 
                placeholder="Please describe your issue or question in detail" 
                className="min-h-[120px]" 
                required 
              />
            </div>
            
            <Button type="submit" className="w-full bg-amber-400 hover:bg-amber-500 text-black">
              Send Message
            </Button>
          </form>
        </div>
        
        <div>
          <h2 className="text-2xl font-semibold mb-4">Frequently Asked Questions</h2>
          
          <Accordion type="single" collapsible className="mb-6">
            {faqs.map((faq, index) => (
              <AccordionItem key={index} value={`item-${index}`}>
                <AccordionTrigger className="text-left">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent>
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
          
          <h3 className="text-xl font-semibold mb-4">Other Ways to Reach Us</h3>
          
          <div className="space-y-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center">
                  <Mail className="mr-2 h-5 w-5 text-amber-500" />
                  Email Support
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm">
                  For general inquiries: <a href="mailto:<EMAIL>" className="text-amber-600 hover:text-amber-800"><EMAIL></a> or <a href="mailto:<EMAIL>" className="text-amber-600 hover:text-amber-800"><EMAIL></a>
                </p>
              </CardContent>
            </Card>
            
            {/* <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center">
                  <Phone className="mr-2 h-5 w-5 text-amber-500" />
                  Phone Support
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm">
                  Customer service: <a href="tel:+18005551234" className="text-amber-600 hover:text-amber-800">+****************</a>
                </p>
                <p className="text-sm text-muted-foreground">
                  Available Monday-Friday, 9am-5pm EST
                </p>
              </CardContent>
            </Card> */}
            
            {/* <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center">
                  <MessageSquare className="mr-2 h-5 w-5 text-amber-500" />
                  Live Chat
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm">
                  Chat with our support team in real-time during business hours.
                </p>
                <Button variant="outline" size="sm" className="mt-2">
                  Start Chat
                </Button>
              </CardContent>
            </Card> */}
          </div>
          
          {/* <h3 className="text-xl font-semibold mt-6 mb-4">Help Resources</h3> */}
          
          <div className="space-y-4">
              {/* <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg flex items-center">
                    <HelpCircle className="mr-2 h-5 w-5 text-amber-500" />
                    Knowledge Base
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm">
                    Browse our comprehensive knowledge base for answers to common questions.
                  </p>
                  <Button variant="link" className="p-0 h-auto mt-1 text-amber-600 hover:text-amber-800">
                    Visit Knowledge Base
                  </Button>
                </CardContent>
              </Card> */}
            
            {/* <Card> */}
              {/* <CardHeader className="pb-2"> */}
                {/* <CardTitle className="text-lg flex items-center">
                  <FileText className="mr-2 h-5 w-5 text-amber-500" />
                  User Guides
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm">
                  Step-by-step guides to help you make the most of Vendor Hub.
                </p>
                <Button variant="link" className="p-0 h-auto mt-1 text-amber-600 hover:text-amber-800">
                  View User Guides
                </Button> */}
              {/* </CardContent> */}
            {/* </Card> */}
          </div>
        </div>
      </div>
      
      <div>
        {/* <h2 className="text-xl font-semibold mb-4">Vendor Support</h2>
        <p className="mb-4">
          Are you a vendor looking for assistance with your listing or account? Our dedicated vendor 
          support team is here to help you optimize your presence on Vendor Hub.
        </p> */}
        {/* <div className="flex flex-col sm:flex-row gap-4">
          <Button className="bg-amber-400 hover:bg-amber-500 text-black">
            Vendor Support Portal
          </Button>
          <Button variant="outline">
            Schedule a Consultation
          </Button>
        </div> */}
      </div>
    </div>
  );
}
