import json

def add_data_provider_field(json_file_path, output_file_path, data_provider_companies):
    """
    Add a 'Data provider' field to JSON data based on company names.
    
    Args:
        json_file_path (str): Path to input JSON file
        output_file_path (str): Path to save updated JSON file
        data_provider_companies (list): List of company names that should be marked as data providers
    """
    # Read the JSON file
    with open(json_file_path, 'r', encoding='utf-8') as file:
        data = json.load(file)
    
    # Add the "Data provider" field to each item
    for item in data:
        if item.get("Company") in data_provider_companies:
            item["Data provider"] = 1
        else:
            item["Data provider"] = 0
    
    # Write the updated data to the output file
    with open(output_file_path, 'w', encoding='utf-8') as file:
        json.dump(data, file, indent=2, ensure_ascii=False)
    
    print(f"Updated JSON saved to {output_file_path}")
    print(f"Marked {sum(1 for item in data if item['Data provider'] == 1)} companies as data providers")

# Example usage:
if __name__ == "__main__":
    # Specify your data provider companies here
    data_providers = [
        "Komodo Health",
        "MarketScan by Merative",
        "Optum",
        "IQVIA",
        "Flatiron Health",
        "Symphony Health",
        "Premier Applied Sciences",
        "Veradigm"
    ]
    
    # Update the file paths as needed
    input_file = "/Users/<USER>/Desktop/vendorhub/heor_and_market_access_vendor_details_with_logos.json"
    output_file = "/Users/<USER>/Desktop/vendorhub/heor_and_market_access_vendor_details_with_logos.json"
    
    add_data_provider_field(input_file, output_file, data_providers)