"use client";

import React, { useState, useMemo, useEffect, useRef } from "react";
import {
  ArrowDown,
  BarChart2,
  BookmarkCheck,
  ChevronRight,
  Filter,
  <PERSON><PERSON>hart as PieChartIcon,
  X,
  Bookmark,
  ExternalLink,
  Eye,
  ChevronLeft,
  ChevronsLeft,
  ChevronsRight,
  Sparkles,
  Loader2,
  FilePlus2,
  FileCheck2
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { SubpageHeader } from "@/components/SubpageHeader";
import { useLocalStorage } from "@/hooks/use-local-storage";
import { cn } from "@/lib/utils";
import { 
  ResponsiveContainer, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip as ReTooltip,
  Legend as ReLegend,
  PieChart as RePieChart,
  Pie as RePie,
  Cell
} from "recharts";
import medicinesData from "../../../medicines_output_medicines_en_fixed.json";
import SelectionPopup from "@/components/SelectionPopup";
import { ClipboardProvider, useClipboard } from "@/contexts/ClipboardContext";
import { ClipboardPanel } from "@/components/Clipboard";
import PagePdfViewer from "@/components/PagePdfViewer";

interface Medicine {
  [key: string]: any;
  Category: string;
  "Name of medicine": string;
  "Medicine status": string;
  "International non-proprietary name (INN) / common name"?: string;
  "Active substance"?: string;
  "Therapeutic area (MeSH)"?: string;
  "Therapeutic indication"?: string;
  "Marketing authorisation date"?: string;
  "Medicine URL"?: string;
}


function MedicinesExplorerContent() {
  const medicines = medicinesData as Medicine[];
  const { addNote } = useClipboard();
  const [popup, setPopup] = useState<
    { text: string; x: number; y: number } | null
  >(null);
  const [reference, setReference] = useState("");

  const formatNumber = (num: number) => num.toLocaleString();
  const preserveScroll = () => {
    const y = window.scrollY;
    requestAnimationFrame(() => {
      window.scrollTo({ top: y });
    });
  };
  
  // Add state for alphabetical filtering
  const [alphabetFilter, setAlphabetFilter] = useState<string | null>(null);

  // Add state for name type toggle
  const [nameType, setNameType] = useState<"brand" | "generic">("brand");

  // Define state variables first
  const [search, setSearch] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [compareNames, setCompareNames] = useState<string[]>([]);
  const [favoriteNames, setFavoriteNames] = useLocalStorage<string[]>(
    "favoriteMedicines",
    [],
  );
  const [opinionStatusFilter, setOpinionStatusFilter] = useState("all");
  const [therapeuticAreaFilter, setTherapeuticAreaFilter] = useState("all");
  const [pharmGroupFilter, setPharmGroupFilter] = useState("all");
  const [acceleratedFilter, setAcceleratedFilter] = useState("all");
  const [advancedTherapyFilter, setAdvancedTherapyFilter] = useState("all");
  const [biosimilarFilter, setBiosimilarFilter] = useState("all");
  const [conditionalFilter, setConditionalFilter] = useState("all");
  const [exceptionalFilter, setExceptionalFilter] = useState("all");
  const [genericFilter, setGenericFilter] = useState("all");
  const [orphanFilter, setOrphanFilter] = useState("all");
  const [primeFilter, setPrimeFilter] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 100;
  const [openItems, setOpenItems] = useState<string[]>([]);
  const [savedOpenItems, setSavedOpenItems] = useState<string[]>([]);
  const [pdfLinks, setPdfLinks] = useState<Record<string, string[]>>({});
  const [mainChartType, setMainChartType] = useState<"bar" | "pie">("bar");
  const [chartFilters, setChartFilters] = useState<ChartFilter[]>([]);
  const [pdfModal, setPdfModal] = useState<{ url: string; title: string } | null>(null);
  const [comparisonDocs, setComparisonDocs] = useState<{ url: string; title: string }[]>([]);
  const [contextDocs, setContextDocs] = useState<{ url: string; title: string; text: string }[]>([]);
  const [contextLoading, setContextLoading] = useState<string[]>([]);
  const [savedPages, setSavedPages] = useState<{
    id: string;
    url: string;
    title: string;
    page: number;
  }[]>([]);
  const [pageInputs, setPageInputs] = useState<Record<string, number>>({});
  const [comparisonSummary, setComparisonSummary] = useState<string>("");
  const [loadingSummary, setLoadingSummary] = useState(false);

  const [top, setTop] = useState<'clipboard' | 'pdf' | null>(null);

  useEffect(() => {
    if (pdfModal) setTop('pdf');
  }, [pdfModal]);

  const saveSelection = () => {
    if (!popup) return;
    addNote(popup.text, window.location.href, undefined, reference);
    window.getSelection()?.removeAllRanges();
    setPopup(null);
    setReference('');
  };

  const closePopup = () => {
    setPopup(null);
    setReference('');
  };

  useEffect(() => {
    const handler = (e: MouseEvent) => {
      const sel = window.getSelection();
      const text = sel?.toString().trim();
      if (text) {
        e.preventDefault();
        setReference('');
        setPopup({ text, x: e.clientX, y: e.clientY });
      }
    };
    document.addEventListener('contextmenu', handler);
    return () => document.removeEventListener('contextmenu', handler);
  }, [addNote]);

  // Add state for main chart category
  const [mainChartCategory, setMainChartCategory] = useState<{
    id: string;
    label: string;
  }>({
    id: "Medicine status",
    label: "Medicine Status"
  });

  // Add state for filter section visibility
  const [showFilterSection, setShowFilterSection] = useState(false);

  // Define chart filter type
  interface ChartFilter {
    category: string;
    value: string;
  }

  // Define color array for charts
  const COLORS = [
    "#8884d8", "#83a6ed", "#8dd1e1", "#82ca9d", "#a4de6c", 
    "#d0ed57", "#ffc658", "#ff8042", "#ff6361", "#bc5090"
  ];

  // Generate alphabet list for navigation
  const alphabet = useMemo(() => {
    return "ABCDEFGHIJKLMNOPQRSTUVWXYZ".split("");
  }, []);

  const statuses = useMemo(
    () => Array.from(new Set(medicines.map((m) => m["Medicine status"]))),
    [medicines],
  );
  const categories = useMemo(
    () => Array.from(new Set(medicines.map((m) => m.Category))),
    [medicines],
  );
  const opinionStatuses = useMemo(
    () => Array.from(new Set(medicines.map((m) => m["Opinion status"]))).filter(Boolean),
    [medicines],
  );
  const therapeuticAreas = useMemo(
    () =>
      Array.from(new Set(medicines.map((m) => m["Therapeutic area (MeSH)"]))).filter(
        Boolean,
      ),
    [medicines],
  );
  const pharmGroups = useMemo(
    () =>
      Array.from(new Set(medicines.map((m) => m["Pharmacotherapeutic group\n(human)"]))).filter(
        Boolean,
      ),
    [medicines],
  );
  const yesNoOptions = ["Yes", "No"];

  const addComparisonDoc = (url: string, title: string) => {
    setComparisonDocs((prev) => {
      if (prev.length >= 2) return prev;
      if (prev.some((d) => d.url === url)) return prev;
      return [...prev, { url, title }];
    });
  };

  const removeComparisonDoc = (url: string) => {
    setComparisonDocs((prev) => prev.filter((d) => d.url !== url));
  };

  // Normalize URLs for duplicate detection (ignores query/hash)
  const normalizeUrl = (u: string) => u.split('#')[0].split('?')[0];

  const addContextDoc = async (url: string, title: string) => {
    const normalized = normalizeUrl(url);
    const alreadyAdded = contextDocs.some((d) => normalizeUrl(d.url) === normalized);
    const alreadyLoading = contextLoading.some((u) => normalizeUrl(u) === normalized);
    if (alreadyAdded || alreadyLoading) return;
    setContextLoading((p) => [...p, url]);
    try {
      const res = await fetch('/api/pdf-to-markdown', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url }),
      });
      if (!res.ok) {
        console.error('Failed to convert pdf', res.status);
      } else {
        const data = await res.json();
        
        // Log the preview and size to console
        console.log(`PDF Conversion Preview (${Math.round(data.totalLength/1024)}KB):`);
        console.log(data.preview);
        
        setContextDocs((prev) => [...prev, { url, title, text: data.text }]);
      }
    } catch (err) {
      console.error('Convert pdf error', err);
    } finally {
      setContextLoading((p) => p.filter((u) => u !== url));
    }
  };

  const removeContextDoc = (url: string) => {
    setContextDocs((prev) => prev.filter((d) => d.url !== url));
  };

  const saveCurrentPage = (doc: { url: string; title: string }) => {
    const page = pageInputs[doc.url] || 1;
    setSavedPages((prev) => [
      ...prev,
      {
        id: `${Date.now()}-${Math.random().toString(36).slice(2, 9)}`,
        url: doc.url,
        title: doc.title,
        page,
      },
    ]);
  };

  const downloadSavedPages = async () => {
    if (savedPages.length === 0) return;
    try {
      const res = await fetch("/api/combine-pdf-pages", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          selections: savedPages.map((p) => ({ url: p.url, page: p.page })),
        }),
      });
      if (!res.ok) {
        console.error("Combine request failed", res.status);
        return;
      }
      const blob = await res.blob();
      const a = document.createElement("a");
      a.href = URL.createObjectURL(blob);
      a.download = "saved_pages.pdf";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(a.href);
    } catch (err) {
      console.error("Failed to download combined PDF", err);
    }
  };

  const docsToShow = useMemo(() => {
    const docs = [...comparisonDocs];
    if (pdfModal) docs.push(pdfModal);
    return docs.slice(0, 3);
  }, [comparisonDocs, pdfModal]);

  const gridCols = useMemo(() => {
    if (docsToShow.length === 1) return "grid-cols-1";
    if (docsToShow.length === 2) return "grid-cols-2";
    return "grid-cols-3";
  }, [docsToShow.length]);

  const fetchChat = async (prompt: string) => {
    const res = await fetch("/api/chat", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ messages: [{ role: "user", content: prompt }] }),
    });
    if (!res.ok) throw new Error("ChatGPT request failed");
    const data = await res.json();
    return data.content as string;
  };

  const parseBullets = (text: string) =>
    text
      .split(/\n+/)
      .map((l) => l.replace(/^[^\w]+|^[0-9]+\.?\s+/, "").trim())
      .filter(Boolean);

  const summarizeComparison = async () => {
    if (compareMedicines.length === 0) return;
    setLoadingSummary(true);
    const text = compareMedicines
      .map((m) =>
        `Name: ${m["Name of medicine"]}\nStatus: ${m["Medicine status"]}\nINN: ${
          m["International non-proprietary name (INN) / common name"] || "-"
        }\nActive substance: ${m["Active substance"] || "-"}\nIndication: ${
          m["Therapeutic indication"] || "-"
        }`,
      )
      .join("\n\n");
    const docContext = contextDocs
      .map((d) => `SOURCE: ${d.title}\n${d.text}`)
      .join("\n\n");
    const prompt =
      `Compare these EMA treatments and summarize their key similarities and differences in up to five \u2022 bullet points without numbers.\n\n${text}` +
      (docContext ? `\n\nContext from documents:\n${docContext}` : "");
    try {
      const resp = await fetchChat(prompt);
      setComparisonSummary(resp);
    } finally {
      setLoadingSummary(false);
    }
  };

  // First define filteredMedicines 
  const filteredMedicines = useMemo(() => {
    // Start with basic filters
    let filtered = medicines.filter((m) => {
      const searchText = `${m["Name of medicine"]} ${m["International non-proprietary name (INN) / common name"] ?? ""} ${m["Active substance"] ?? ""}`.toLowerCase();
      const matchesSearch = searchText.includes(search.toLowerCase());
      const matchesStatus = statusFilter === "all" || m["Medicine status"] === statusFilter;
      const matchesCategory = categoryFilter === "all" || m.Category === categoryFilter;
      const matchesOpinion = opinionStatusFilter === "all" || m["Opinion status"] === opinionStatusFilter;
      const matchesTherapeutic =
        therapeuticAreaFilter === "all" || m["Therapeutic area (MeSH)"] === therapeuticAreaFilter;
      const matchesPharmGroup =
        pharmGroupFilter === "all" || m["Pharmacotherapeutic group\n(human)"] === pharmGroupFilter;
      const matchesAccelerated =
        acceleratedFilter === "all" || m["Accelerated assessment"] === acceleratedFilter;
      const matchesAdvanced =
        advancedTherapyFilter === "all" || m["Advanced therapy"] === advancedTherapyFilter;
      const matchesBiosimilar =
        biosimilarFilter === "all" || m["Biosimilar"] === biosimilarFilter;
      const matchesConditional =
        conditionalFilter === "all" || m["Conditional approval"] === conditionalFilter;
      const matchesExceptional =
        exceptionalFilter === "all" || m["Exceptional circumstances"] === exceptionalFilter;
      const matchesGeneric =
        genericFilter === "all" || m["Generic or hybrid"] === genericFilter;
      const matchesOrphan = orphanFilter === "all" || m["Orphan medicine"] === orphanFilter;
      const matchesPrime = primeFilter === "all" || m["PRIME: priority medicine"] === primeFilter;
      
      // Match alphabet filter based on name type
      let matchesAlphabet = true;
      if (alphabetFilter) {
        if (nameType === "brand") {
          matchesAlphabet = m["Name of medicine"].toUpperCase().startsWith(alphabetFilter);
        } else {
          const genericName = m["International non-proprietary name (INN) / common name"] || 
                             m["Active substance"] || "";
          matchesAlphabet = genericName.toUpperCase().startsWith(alphabetFilter);
        }
      }

      return (
        matchesSearch &&
        matchesStatus &&
        matchesCategory &&
        matchesOpinion &&
        matchesTherapeutic &&
        matchesPharmGroup &&
        matchesAccelerated &&
        matchesAdvanced &&
        matchesBiosimilar &&
        matchesConditional &&
        matchesExceptional &&
        matchesGeneric &&
        matchesOrphan &&
        matchesPrime &&
        matchesAlphabet
      );
    });

    // Then apply chart filters
    if (chartFilters.length > 0) {
      filtered = filtered.filter(medicine => 
        chartFilters.every(filter => {
          const value = medicine[filter.category];
          if (value === undefined) return false;
          
          // For boolean or yes/no fields, convert to "Yes" or "No"
          const displayValue = typeof value === 'boolean' 
            ? (value ? "Yes" : "No")
            : (value === "" ? "Not specified" : String(value));
          
          return displayValue === filter.value;
        })
      );
    }
    
    return filtered;
  }, [
    medicines,
    search,
    statusFilter,
    categoryFilter,
    opinionStatusFilter,
    therapeuticAreaFilter,
    pharmGroupFilter,
    acceleratedFilter,
    advancedTherapyFilter,
    biosimilarFilter,
    conditionalFilter,
    exceptionalFilter,
    genericFilter,
    orphanFilter,
    primeFilter,
    alphabetFilter,
    nameType,
    chartFilters, // Add chartFilters as a dependency
  ]);

  // Group medicines by first letter of name (brand or generic)
  const alphabeticalMedicines = useMemo(() => {
    const grouped: Record<string, Medicine[]> = {};
    alphabet.forEach(letter => {
      if (nameType === "brand") {
        grouped[letter] = medicines.filter(m => 
          m["Name of medicine"].toUpperCase().startsWith(letter)
        );
      } else {
        // For generic name, use INN or Active substance
        grouped[letter] = medicines.filter(m => {
          const genericName = m["International non-proprietary name (INN) / common name"] || 
                             m["Active substance"] || "";
          return genericName.toUpperCase().startsWith(letter);
        });
      }
    });
    return grouped;
  }, [medicines, alphabet, nameType]);

  // Function to generate chart data from medicines
  const generateChartData = (category: string, medicines: Medicine[]) => {
    const counts: Record<string, number> = {};
    
    medicines.forEach(medicine => {
      const value = medicine[category];
      if (value !== undefined) {
        // For boolean or yes/no fields, convert to "Yes" or "No"
        const displayValue = typeof value === 'boolean' 
          ? (value ? "Yes" : "No")
          : (value === "" ? "Not specified" : String(value));
        
        counts[displayValue] = (counts[displayValue] || 0) + 1;
      }
    });
    
    // For categories with many values, limit to top 15 plus "Others"
    const entries = Object.entries(counts)
      .map(([name, value]) => ({ name, value }))
      .sort((a, b) => b.value - a.value);
    
    if (entries.length > 15) {
      const top15 = entries.slice(0, 15);
      const others = entries.slice(15).reduce(
        (sum, entry) => sum + entry.value, 
        0
      );
      
      if (others > 0) {
        top15.push({ name: "Others", value: others });
      }
      
      return top15;
    }
    
    return entries;
  };

  // Function to handle chart element click
  const handleChartClick = (category: string, value: string) => {
    if (!category || !value) return;
    
    // Check if this filter is already applied
    const existingFilterIndex = chartFilters.findIndex(
      filter => filter.category === category && filter.value === value
    );
    
    // Use a single click handler that toggles the filter
    if (existingFilterIndex >= 0) {
      // Remove the filter if it's already applied
      const newFilters = [...chartFilters];
      newFilters.splice(existingFilterIndex, 1);
      setChartFilters(newFilters);
    } else {
      // Add the filter, replacing any existing filter for the same category
      const newFilters = chartFilters.filter(filter => filter.category !== category);
      newFilters.push({ category, value });
      setChartFilters(newFilters);
    }
  };

  // Function to clear a specific chart filter
  const clearChartFilter = (category: string) => {
    setChartFilters(chartFilters.filter(filter => filter.category !== category));
  };

  // Generate chart data for the main chart based on selected category
  const mainChartData = useMemo(() => 
    generateChartData(mainChartCategory.id, filteredMedicines), 
    [filteredMedicines, mainChartCategory.id]
  );

  // Then use filteredMedicines for pagination calculations
  const totalPages = useMemo(() => {
    return Math.ceil(filteredMedicines.length / itemsPerPage);
  }, [filteredMedicines.length, itemsPerPage]);

  // Get current page items
  const currentItems = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredMedicines.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredMedicines, currentPage, itemsPerPage]);

  // Reset to page 1 when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [search, statusFilter, categoryFilter, opinionStatusFilter, therapeuticAreaFilter, 
      pharmGroupFilter, acceleratedFilter, advancedTherapyFilter, biosimilarFilter, 
      conditionalFilter, exceptionalFilter, genericFilter, orphanFilter, primeFilter, 
      alphabetFilter, nameType]);

  // Define available chart categories - include all filter options
  const chartCategories = useMemo(() => [
    { id: "Medicine status", label: "Medicine Status" },
    { id: "Category", label: "Category" },
    { id: "Opinion status", label: "Opinion Status" },
    { id: "Therapeutic area (MeSH)", label: "Therapeutic Area" },
    { id: "Pharmacotherapeutic group\n(human)", label: "Pharmacotherapeutic Group" },
    { id: "Orphan medicine", label: "Orphan Medicine" },
    { id: "Generic or hybrid", label: "Generic/Hybrid" },
    { id: "Biosimilar", label: "Biosimilar" },
    { id: "Conditional approval", label: "Conditional Approval" },
    { id: "Exceptional circumstances", label: "Exceptional Circumstances" },
    { id: "Advanced therapy", label: "Advanced Therapy" },
    { id: "PRIME: priority medicine", label: "PRIME" },
    { id: "Accelerated assessment", label: "Accelerated Assessment" }
  ], []);

  // Get favorite medicines
  const favoriteMedicines = useMemo(() => {
    return medicines.filter(m => favoriteNames.includes(m["Name of medicine"]));
  }, [medicines, favoriteNames]);

  // Get medicines for comparison
  const compareMedicines = useMemo(() => {
    return medicines.filter(m => compareNames.includes(m["Name of medicine"]));
  }, [medicines, compareNames]);

  // Toggle favorite status
  const toggleFavorite = (medicineName: string) => {
    if (favoriteNames.includes(medicineName)) {
      setFavoriteNames(favoriteNames.filter(name => name !== medicineName));
    } else {
      setFavoriteNames([...favoriteNames, medicineName]);
    }
  };

  // Pagination component
  const Pagination = () => {
    if (totalPages <= 1) return null;
    
    // Calculate which page numbers to show
    const getPageNumbers = () => {
      const pageNumbers = [];
      const maxVisiblePages = 5;
      
      if (totalPages <= maxVisiblePages) {
        for (let i = 1; i <= totalPages; i++) {
          pageNumbers.push(i);
        }
      } else {
        pageNumbers.push(1);
        
        const startPage = Math.max(2, currentPage - 1);
        const endPage = Math.min(totalPages - 1, currentPage + 1);
        
        if (startPage > 2) {
          pageNumbers.push('ellipsis1');
        }
        
        for (let i = startPage; i <= endPage; i++) {
          pageNumbers.push(i);
        }
        
        if (endPage < totalPages - 1) {
          pageNumbers.push('ellipsis2');
        }
        
        pageNumbers.push(totalPages);
      }
      
      return pageNumbers;
    };
    
    // Function to handle page change
    const handlePageChange = (newPage: number) => {
      // Get current scroll position
      const scrollY = window.scrollY;
      
      // Update page
      setCurrentPage(newPage);
      
      // After React renders the new page, restore scroll position
      setTimeout(() => {
        window.scrollTo(0, document.body.scrollHeight);
      }, 10);
    };
    
    return (
      <div id="pagination-controls" className="flex justify-center items-center mt-4 gap-1">
        {/* First page button */}
        {currentPage > 1 && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(1)}
            className="px-2"
          >
            <ChevronsLeft className="h-4 w-4" />
          </Button>
        )}
        
        {/* Previous button */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(Math.max(currentPage - 1, 1))}
          disabled={currentPage === 1}
          className="px-2"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        
        {/* Page numbers */}
        {getPageNumbers().map((page, index) => {
          if (page === 'ellipsis1' || page === 'ellipsis2') {
            return (
              <span key={`ellipsis-${index}`} className="px-2">
                ...
              </span>
            );
          }
          
          return (
            <Button
              key={`page-${page}`}
              variant={currentPage === page ? "default" : "outline"}
              size="sm"
              onClick={() => handlePageChange(page as number)}
              className="w-8 h-8"
            >
              {page}
            </Button>
          );
        })}
        
        {/* Next button */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(Math.min(currentPage + 1, totalPages))}
          disabled={currentPage === totalPages}
          className="px-2"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
        
        {/* Last page button */}
        {currentPage < totalPages && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(totalPages)}
            className="px-2"
          >
            <ChevronsRight className="h-4 w-4" />
          </Button>
        )}
      </div>
    );
  };

  // Medicine item component
  const MedicineItem = ({
    value,
    med,
    isOpen
  }: {
    value: string;
    med: Medicine;
    isOpen: boolean;
  }) => {
    const [loading, setLoading] = useState(false);

    useEffect(() => {
      if (isOpen && med["Medicine URL"] && pdfLinks[value] === undefined) {
        setLoading(true);
        fetch(`/api/ema-pdfs?url=${encodeURIComponent(med["Medicine URL"]!)}`)
          .then(res => res.json())
          .then(data => {
            setPdfLinks(prev => ({ ...prev, [value]: data.pdfs || [] }));
          })
          .catch(err => {
            console.error("Failed to fetch PDFs", err);
            setPdfLinks(prev => ({ ...prev, [value]: [] }));
          })
          .finally(() => setLoading(false));
      }
    }, [isOpen, med, value]);

    const docs = pdfLinks[value];
    const [showOtherDocs, setShowOtherDocs] = useState(false);

    const handleTriggerClick = () => {
      preserveScroll();
    }

    return (
      <AccordionItem value={value} className="border-b">
        <AccordionTrigger className="hover:no-underline py-3" onClick={handleTriggerClick}>
          <div className="flex justify-between items-center w-full pr-4">
            <div className="flex-1 text-left">
              <span className={cn("font-medium", isOpen && "text-lg font-semibold")}>{med["Name of medicine"]}</span>
              <span className="text-sm text-muted-foreground ml-2">
                {med["International non-proprietary name (INN) / common name"] || med["Active substance"]}
                {isOpen && med["Medicine URL"] && (
                  <a
                    href={med["Medicine URL"]}
                    target="_blank"
                    rel="noopener noreferrer"
                    onClick={(e) => e.stopPropagation()}
                    className="ml-1 inline-flex"
                  >
                    <ExternalLink className="h-4 w-4" />
                  </a>
                )}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs bg-slate-100 dark:bg-slate-700 px-2 py-1 rounded">
                {med["Medicine status"]}
              </span>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2"
                onClick={(e) => {
                  e.stopPropagation();
                  toggleFavorite(med["Name of medicine"]);
                }}
              >
                {favoriteNames.includes(med["Name of medicine"]) ? (
                  <BookmarkCheck className="h-4 w-4 text-amber-500" />
                ) : (
                  <Bookmark className="h-4 w-4" />
                )}
              </Button>
              <Checkbox
                checked={compareNames.includes(med["Name of medicine"])}
                onCheckedChange={(checked) => {
                  if (checked) {
                    setCompareNames([...compareNames, med["Name of medicine"]]);
                  } else {
                    setCompareNames(compareNames.filter(n => n !== med["Name of medicine"]));
                  }
                  preserveScroll();
                }}
                onClick={(e) => e.stopPropagation()}
              />
            </div>
          </div>
        </AccordionTrigger>
        <AccordionContent>
          <div className="grid md:grid-cols-2 gap-4 py-2">
            <div>
              <h4 className="text-base font-semibold mb-2">Basic Information</h4>
              <dl className="space-y-1">
                <div className="flex">
                  <dt className="w-40 text-sm font-medium text-muted-foreground">Category:</dt>
                  <dd className="ml-0 flex-1 text-left">{med.Category}</dd>
                </div>
                <div className="flex">
                  <dt className="w-40 text-sm font-medium text-muted-foreground">Status:</dt>
                  <dd className="ml-0 flex-1 text-left">{med["Medicine status"]}</dd>
                </div>
                <div className="flex">
                  <dt className="w-40 text-sm font-medium text-muted-foreground">INN/Common name:</dt>
                  <dd className="ml-0 flex-1 text-left">{med["International non-proprietary name (INN) / common name"] || "-"}</dd>
                </div>
                <div className="flex">
                  <dt className="w-40 text-sm font-medium text-muted-foreground">Active substance:</dt>
                  <dd className="ml-0 flex-1 text-left">{med["Active substance"] || "-"}</dd>
                </div>
                <div className="flex">
                  <dt className="w-40 text-sm font-medium text-muted-foreground">Therapeutic area:</dt>
                  <dd className="ml-0 flex-1 text-left">{med["Therapeutic area (MeSH)"] || "-"}</dd>
                </div>
                <div className="flex">
                  <dt className="w-40 text-sm font-medium text-muted-foreground">Authorization date:</dt>
                  <dd className="ml-0 flex-1 text-left">{med["Marketing authorisation date"] || "-"}</dd>
                </div>
              </dl>
            </div>
            <div>
              <h4 className="text-base font-semibold mb-2">Additional Information</h4>
              <dl className="space-y-1">
                <div className="flex">
                  <dt className="w-40 text-sm font-medium text-muted-foreground">Orphan medicine:</dt>
                  <dd>{med["Orphan medicine"] || "No"}</dd>
                </div>
                <div className="flex">
                  <dt className="w-40 text-sm font-medium text-muted-foreground">Generic/Hybrid:</dt>
                  <dd>{med["Generic or hybrid"] || "No"}</dd>
                </div>
                <div className="flex">
                  <dt className="w-40 text-sm font-medium text-muted-foreground">Biosimilar:</dt>
                  <dd>{med.Biosimilar || "No"}</dd>
                </div>
                <div className="flex">
                  <dt className="w-40 text-sm font-medium text-muted-foreground">Conditional approval:</dt>
                  <dd>{med["Conditional approval"] || "No"}</dd>
                </div>
                <div className="flex">
                  <dt className="w-40 text-sm font-medium text-muted-foreground">Exceptional circumstances:</dt>
                  <dd>{med["Exceptional circumstances"] || "No"}</dd>
                </div>
                <div className="flex">
                  <dt className="w-40 text-sm font-medium text-muted-foreground">Advanced therapy:</dt>
                  <dd>{med["Advanced therapy"] || "No"}</dd>
                </div>
              </dl>
            </div>
          </div>
          {med["Therapeutic indication"] && (
            <div className="mt-4">
              <h4 className="text-base font-semibold mb-2">Therapeutic Indication</h4>
              <p className="text-sm">{med["Therapeutic indication"]}</p>
            </div>
          )}
          <div className="mt-4">
            {loading && <p>Loading documents...</p>}
            {docs && (
              <div>
                <h4 className="text-base font-semibold mb-2">Documents</h4>
                {docs.length > 0 ? (
                  <>
                    <ul className="space-y-1">
                      {docs
                        .filter((link) => /_en\.pdf(\?|$)/i.test(link))
                        .map((link, idx) => (
                          <li key={idx} className="flex items-center gap-1">
                            <span className="inline-block w-1 h-1 rounded-full bg-amber-600 mr-2"></span>
                            <a
                              href={link}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-amber-600 underline inline-flex items-center"
                            >
                              {link.split('/').pop()}
                              <ExternalLink className="ml-1 h-4 w-4" />
                            </a>
                            {/\.pdf(\?|$)/i.test(link) && (
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6 p-0"
                                onClick={() =>
                                  setPdfModal({
                                    url: link,
                                    title: link.split('/').pop() || 'Document',
                                  })
                                }
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            )}
                            {/\.pdf(\?|$)/i.test(link) && (
                              <Button
                                variant="outline"
                                size="sm"
                                className="h-6 px-1"
                                title={contextDocs.some((d) => d.url === link) ? 'Remove from context' : 'Add to context'}
                                onClick={() => {
                                  contextDocs.some((d) => d.url === link)
                                    ? removeContextDoc(link)
                                    : addContextDoc(link, link.split('/').pop() || 'Document');
                                  preserveScroll();
                                }}
                                disabled={contextLoading.includes(link)}
                              >
                                {contextLoading.includes(link) ? (
                                  <Loader2 className="mr-1 h-4 w-4 animate-spin" />
                                ) : contextDocs.some((d) => d.url === link) ? (
                                  <FileCheck2 className="mr-1 h-4 w-4 text-amber-500" />
                                ) : (
                                  <FilePlus2 className="mr-1 h-4 w-4" />
                                )}
                                {contextDocs.some((d) => d.url === link) ? 'Added' : 'Context'}
                              </Button>
                            )}
                          </li>
                        ))}
                    </ul>
                    {showOtherDocs && (
                      <>
                        <ul className="space-y-1 mt-1 mb-1">
                          {docs
                            .filter((link) => !/_en\.pdf(\?|$)/i.test(link))
                            .map((link, idx) => (
                              <li key={idx} className="flex items-center gap-1">
                                <span className="inline-block w-1 h-1 rounded-full bg-amber-600 mr-2"></span>
                                <a
                                  href={link}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-amber-600 underline inline-flex items-center"
                                >
                                  {link.split('/').pop()}
                                  <ExternalLink className="ml-1 h-4 w-4" />
                                </a>
                                {/\.pdf(\?|$)/i.test(link) && (
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-6 w-6 p-0"
                                    onClick={() =>
                                      setPdfModal({
                                        url: link,
                                        title: link.split('/').pop() || 'Document',
                                      })
                                    }
                                  >
                                    <Eye className="h-4 w-4" />
                                  </Button>
                                )}
                                {/\.pdf(\?|$)/i.test(link) && (
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="h-6 px-1"
                                    title={contextDocs.some((d) => d.url === link) ? 'Remove from context' : 'Add to context'}
                                    onClick={() => {
                                      contextDocs.some((d) => d.url === link)
                                        ? removeContextDoc(link)
                                        : addContextDoc(link, link.split('/').pop() || 'Document');
                                      preserveScroll();
                                    }}
                                    disabled={contextLoading.includes(link)}
                                  >
                                    {contextLoading.includes(link) ? (
                                      <Loader2 className="mr-1 h-4 w-4 animate-spin" />
                                    ) : contextDocs.some((d) => d.url === link) ? (
                                      <FileCheck2 className="mr-1 h-4 w-4 text-amber-500" />
                                    ) : (
                                      <FilePlus2 className="mr-1 h-4 w-4" />
                                    )}
                                    {contextDocs.some((d) => d.url === link) ? 'Added' : 'Context'}
                                  </Button>
                                )}
                              </li>
                            ))}
                        </ul>
                        <Button
                          variant="link"
                          className="p-0 h-auto text-sm"
                          onClick={() => setShowOtherDocs(false)}
                        >
                          Hide other languages
                        </Button>
                      </>
                    )}
                    {!showOtherDocs && (
                      <Button
                        variant="link"
                        className="p-0 h-auto text-sm mt-1"
                        onClick={() => setShowOtherDocs(true)}
                      >
                        Show other languages
                      </Button>
                    )}
                  </>
                ) : (
                  <p className="text-sm">No documents found.</p>
                )}
              </div>
            )}
          </div>
        </AccordionContent>
      </AccordionItem>
    );
  };

  // Define the ChartComponent before using it in the JSX
  const ChartComponent = ({ 
    title, 
    data, 
    category, 
    height = 200,
    chartType = "pie" 
  }: { 
    title: string; 
    data: { name: string; value: number }[]; 
    category: string; 
    height?: number;
    chartType?: "pie" | "bar"; 
  }) => {
    // Don't render if no data
    if (!data || data.length === 0) return null;
    
    // Check if this category has an active filter
    const hasActiveFilter = chartFilters.some(filter => filter.category === category);
    const activeValue = chartFilters.find(filter => filter.category === category)?.value;
    
    // Automatically determine best chart type based on data
    const hasManyCategories = data.length > 7;
    const effectiveChartType = hasManyCategories ? "bar" : chartType;
    
    // Calculate appropriate height for bar charts with many categories
    const barChartHeight = hasManyCategories ? Math.max(height, data.length * 30) : height;
    
    // Single click handler for all chart elements
    const handleClick = (value: string) => {
      if (value) {
        handleChartClick(category, value);
      }
    };
    
    // For categories with many items, show horizontal bar chart
    if (hasManyCategories) {
      return (
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium">{title}</h3>
            {hasActiveFilter && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => clearChartFilter(category)}
                className="h-6 px-2"
              >
                Clear <X className="h-3 w-3 ml-1" />
              </Button>
            )}
          </div>
          <div style={{ height: `${barChartHeight}px` }}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart 
                data={data}
                layout="vertical"
                margin={{ top: 5, right: 150, left: 30, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  type="number" 
                  allowDecimals={false}
                  tickMargin={5}
                  domain={[0, 'dataMax']}
                  tickCount={6}
                />
                <YAxis 
                  dataKey="name" 
                  type="category" 
                  width={145}
                  tick={(props) => {
                    const { x, y, payload } = props;
                    return (
                      <g transform={`translate(${x},${y})`}>
                        <title>{payload.value}</title>
                        <text 
                          x={0} 
                          y={0} 
                          dy={4} 
                          textAnchor="end" 
                          fontSize={10}
                          fill="#666"
                        >
                          {payload.value.length > 22 ? `${payload.value.substring(0, 20)}...` : payload.value}
                        </text>
                      </g>
                    );
                  }}
                  interval={0}
                />
                <ReTooltip 
                  formatter={(value: any) => [`${value} medicines`]}
                  labelFormatter={(name: any) => `${name}`}
                />
                <Bar 
                  dataKey="value" 
                  fill="#8884d8"
                  maxBarSize={20}
                  onClick={(data) => {
                    if (data && data.payload && data.payload.name) {
                      handleClick(data.payload.name);
                    }
                  }}
                  cursor="pointer"
                >
                  {data.map((entry, index) => (
                    <Cell 
                      key={`cell-${index}`} 
                      fill={COLORS[index % COLORS.length]} 
                      stroke={activeValue === entry.name ? "#000" : undefined}
                      strokeWidth={activeValue === entry.name ? 2 : undefined}
                    />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      );
    }
    
    // For categories with few items, show pie chart
    return (
      <div className="mb-4">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-sm font-medium">{title}</h3>
          {hasActiveFilter && (
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => clearChartFilter(category)}
              className="h-6 px-2"
            >
              Clear <X className="h-3 w-3 ml-1" />
            </Button>
          )}
        </div>
        <div style={{ height: `${height}px` }}>
          <ResponsiveContainer width="100%" height="100%">
            <RePieChart>
              <RePie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={height > 200 ? 80 : 60}
                fill="#8884d8"
                dataKey="value"
                nameKey="name"
                label={({ name, percent }: { name: string; percent: number }) => 
                  name && name.length > 10 
                    ? `${name.substring(0, 8)}...: ${(percent * 100).toFixed(0)}%` 
                    : `${name || ''}: ${(percent * 100).toFixed(0)}%`
                }
                onClick={(data) => {
                  if (data && data.name) {
                    handleClick(data.name);
                  }
                }}
                cursor="pointer"
              >
                {data.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={COLORS[index % COLORS.length]} 
                    stroke={activeValue === entry.name ? "#000" : undefined}
                    strokeWidth={activeValue === entry.name ? 2 : undefined}
                  />
                ))}
              </RePie>
              <ReTooltip formatter={(value: any, name: any) => [`${value} medicines`, name]} />
              <ReLegend 
                onClick={(data) => {
                  if (data && data.value) {
                    handleClick(data.value);
                  }
                }}
              />
            </RePieChart>
          </ResponsiveContainer>
        </div>
      </div>
    );
  };

  return (
    <>
      {popup && (
        <SelectionPopup
          className="fixed"
          data={popup}
          reference={reference}
          onReferenceChange={setReference}
          onSave={saveSelection}
          onCancel={closePopup}
        />
      )}
      <div className="container mx-auto py-8 px-4">
      <SubpageHeader current="ema-medicines-explorer" />

      <h1 className="text-3xl font-bold mb-6">EMA Medicines Explorer</h1>

      <div className="prose max-w-none mb-8 dark:text-gray-200">
        <p>
          Explore authorised medicines and related information. Use the search
          and filters to quickly find medicines and uncover insights.
        </p>
        <div className="flex items-center justify-between">
          <p className="font-medium mb-0">
            Total medicines in database: <span className="font-bold">{medicines.length.toLocaleString()}</span>
          </p>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              document.getElementById('medications-section')?.scrollIntoView({ behavior: 'smooth' });
            }}
            className="gap-1"
          >
            <ArrowDown className="h-4 w-4" /> Jump to Medications
          </Button>
        </div>
        {filteredMedicines.length !== medicines.length && (
          <p className="font-medium mt-2">
            Filtered: <span className="font-bold">{filteredMedicines.length}</span> (
            <span className="font-bold">
              {((filteredMedicines.length / medicines.length) * 100).toFixed(1)}%
            </span>)
          </p>
        )}
      </div>

      {/* Alphabetical navigation */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-lg font-medium">Filter by first letter:</h3>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-muted-foreground">Filter by:</span>
            <div className="flex border rounded-md overflow-hidden">
              <Button
                variant={nameType === "brand" ? "default" : "ghost"}
                size="sm"
                onClick={() => {
                  setNameType("brand");
                  setAlphabetFilter(null); // Reset filter when changing name type
                }}
                className="rounded-none border-r"
              >
                Brand Name
              </Button>
              <Button
                variant={nameType === "generic" ? "default" : "ghost"}
                size="sm"
                onClick={() => {
                  setNameType("generic");
                  setAlphabetFilter(null); // Reset filter when changing name type
                }}
                className="rounded-none"
              >
                Generic Name
              </Button>
            </div>
          </div>
        </div>
        <div className="flex flex-wrap gap-2">
          {alphabet.map((letter) => (
            <Button
              key={letter}
              variant={alphabetFilter === letter ? "default" : "outline"}
              size="sm"
              onClick={() => setAlphabetFilter(alphabetFilter === letter ? null : letter)}
              className="w-8 h-8 p-0"
              title={`${alphabeticalMedicines[letter].length} medicines`}
            >
              {letter}
            </Button>
          ))}
          {alphabetFilter && (
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => setAlphabetFilter(null)}
              className="ml-2"
            >
              Clear <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          )}
        </div>
        {alphabetFilter && (
          <p className="text-sm mt-2">
            Showing {alphabeticalMedicines[alphabetFilter].length} medicines with {nameType === "brand" ? "brand" : "generic"} name starting with "{alphabetFilter}"
          </p>
        )}
      </div>

      <div className={`${showFilterSection ? "bg-slate-50 dark:bg-slate-800 p-4" : "bg-white dark:bg-gray-900 py-3 px-4"} rounded-lg mb-6 border`}>
        <h3 className="text-lg font-medium flex items-center justify-between dark:text-white -mt-4 -mb-4">
          <div className="flex items-center mt-4">
            <Filter className="mr-2 h-4 w-4" /> Filter Medicines
          </div>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => setShowFilterSection(!showFilterSection)}
            className="text-xs border border-gray-300 dark:border-gray-600 mt-4"
          >
            {showFilterSection ? "Hide Filters" : "Show Filters"}
          </Button>
        </h3>
        
        {showFilterSection && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-8">
            <Input
              placeholder="Search name or INN"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="md:col-span-1"
            />
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((cat) => (
                  <SelectItem key={cat} value={cat}>
                    {cat}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                {statuses.map((st) => (
                  <SelectItem key={st} value={st}>
                    {st}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={opinionStatusFilter} onValueChange={setOpinionStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Opinion status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Opinions</SelectItem>
                {opinionStatuses.map((os) => (
                  <SelectItem key={os} value={os}>
                    {os}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={therapeuticAreaFilter} onValueChange={setTherapeuticAreaFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Therapeutic area" />
              </SelectTrigger>
              <SelectContent className="max-h-60 overflow-y-auto">
                <SelectItem value="all">All Areas</SelectItem>
                {therapeuticAreas.map((ta) => (
                  <SelectItem key={ta} value={String(ta)}>
                    {ta}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={pharmGroupFilter} onValueChange={setPharmGroupFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Pharmacotherapeutic group" />
              </SelectTrigger>
              <SelectContent className="max-h-60 overflow-y-auto">
                <SelectItem value="all">All Groups</SelectItem>
                {pharmGroups.map((pg) => (
                  <SelectItem key={pg} value={String(pg)}>
                    {pg}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={acceleratedFilter} onValueChange={setAcceleratedFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Accelerated assessment" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Accelerated?</SelectItem>
                {yesNoOptions.map((val) => (
                  <SelectItem key={val} value={val}>
                    {val}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={advancedTherapyFilter} onValueChange={setAdvancedTherapyFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Advanced therapy" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Advanced?</SelectItem>
                {yesNoOptions.map((val) => (
                  <SelectItem key={val} value={val}>
                    {val}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={biosimilarFilter} onValueChange={setBiosimilarFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Biosimilar" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Biosimilar?</SelectItem>
                {yesNoOptions.map((val) => (
                  <SelectItem key={val} value={val}>
                    {val}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={conditionalFilter} onValueChange={setConditionalFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Conditional approval" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Conditional?</SelectItem>
                {yesNoOptions.map((val) => (
                  <SelectItem key={val} value={val}>
                    {val}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={exceptionalFilter} onValueChange={setExceptionalFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Exceptional circumstances" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Exceptional?</SelectItem>
                {yesNoOptions.map((val) => (
                  <SelectItem key={val} value={val}>
                    {val}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={genericFilter} onValueChange={setGenericFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Generic or hybrid" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Generic/Hybrid?</SelectItem>
                {yesNoOptions.map((val) => (
                  <SelectItem key={val} value={val}>
                    {val}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={orphanFilter} onValueChange={setOrphanFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Orphan medicine" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Orphan?</SelectItem>
                {yesNoOptions.map((val) => (
                  <SelectItem key={val} value={val}>
                    {val}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={primeFilter} onValueChange={setPrimeFilter}>
              <SelectTrigger>
                <SelectValue placeholder="PRIME" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">PRIME?</SelectItem>
                {yesNoOptions.map((val) => (
                  <SelectItem key={val} value={val}>
                    {val}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
        
        {/* Show active filters as tags */}
        <div className="mt-4 flex flex-wrap gap-2">
          {search && (
            <div className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-100 text-xs px-2 py-1 rounded-full flex items-center">
              Search: {search}
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => setSearch("")}
                className="h-4 w-4 ml-1 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          )}
          
          {statusFilter !== "all" && (
            <div className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-100 text-xs px-2 py-1 rounded-full flex items-center">
              Status: {statusFilter}
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => setStatusFilter("all")}
                className="h-4 w-4 ml-1 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          )}
          
          {categoryFilter !== "all" && (
            <div className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-100 text-xs px-2 py-1 rounded-full flex items-center">
              Category: {categoryFilter}
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => setCategoryFilter("all")}
                className="h-4 w-4 ml-1 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          )}
          
          {/* Add more filter tags for other dropdown filters */}
          
          {/* Show chart filters */}
          {chartFilters.map((filter, index) => (
            <div 
              key={`${filter.category}-${filter.value}`}
              className="bg-amber-100 dark:bg-amber-900 text-amber-800 dark:text-amber-100 text-xs px-2 py-1 rounded-full flex items-center"
            >
              {filter.category}: {filter.value}
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => {
                  const newFilters = [...chartFilters];
                  newFilters.splice(index, 1);
                  setChartFilters(newFilters);
                }}
                className="h-4 w-4 ml-1 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          ))}
          
          {/* Show clear all button if any filters are active */}
          {(search || statusFilter !== "all" || categoryFilter !== "all" || 
            opinionStatusFilter !== "all" || therapeuticAreaFilter !== "all" || 
            pharmGroupFilter !== "all" || acceleratedFilter !== "all" || 
            advancedTherapyFilter !== "all" || biosimilarFilter !== "all" || 
            conditionalFilter !== "all" || exceptionalFilter !== "all" || 
            genericFilter !== "all" || orphanFilter !== "all" || 
            primeFilter !== "all" || alphabetFilter !== null || 
            chartFilters.length > 0) && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => {
                setSearch("");
                setStatusFilter("all");
                setCategoryFilter("all");
                setOpinionStatusFilter("all");
                setTherapeuticAreaFilter("all");
                setPharmGroupFilter("all");
                setAcceleratedFilter("all");
                setAdvancedTherapyFilter("all");
                setBiosimilarFilter("all");
                setConditionalFilter("all");
                setExceptionalFilter("all");
                setGenericFilter("all");
                setOrphanFilter("all");
                setPrimeFilter("all");
                setAlphabetFilter(null);
                setChartFilters([]);
              }}
              className="text-xs"
            >
              Clear All Filters
            </Button>
          )}
        </div>
      </div>

      {/* Main chart with category selector */}
      <div className="mb-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4 gap-2">
          <h2 className="text-xl font-semibold">Primary Insight</h2>
          
          <div className="flex flex-wrap gap-2">
            <Select 
              value={mainChartCategory.id} 
              onValueChange={(value) => {
                const category = chartCategories.find(c => c.id === value);
                if (category) {
                  setMainChartCategory(category);
                }
              }}
            >
              <SelectTrigger className="w-[220px] h-9.5">
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent className="max-h-[300px]">
                <SelectGroup>
                  <SelectLabel>Chart Categories</SelectLabel>
                  {chartCategories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectGroup>
              </SelectContent>
            </Select>
            
            <div className="flex border rounded-md overflow-hidden">
              <Button
                variant={mainChartType === "bar" ? "default" : "ghost"}
                size="sm"
                onClick={() => setMainChartType("bar")}
                className="rounded-none border-r"
                disabled={chartFilters.some(filter => filter.category === mainChartCategory.id)}
              >
                <BarChart2 className="h-4 w-4" />
              </Button>
              <Button
                variant={mainChartType === "pie" ? "default" : "ghost"}
                size="sm"
                onClick={() => setMainChartType("pie")}
                className="rounded-none"
                disabled={chartFilters.some(filter => filter.category === mainChartCategory.id)}
              >
                <PieChartIcon className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
        
        <div className="h-[300px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            {(() => {
              // Check if there's a filter applied to the current category
              const hasFilterForCurrentCategory = chartFilters.some(filter => filter.category === mainChartCategory.id);
              
              // Determine if we should use bar chart based on data length and filters
              const useBarChart = !hasFilterForCurrentCategory && (mainChartType === "bar" || mainChartData.length > 7);
              
              return useBarChart ? (
                <BarChart data={mainChartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="name" 
                    interval={0}
                    tick={({ x, y, payload }: { x: number; y: number; payload: any }) => (
                      <g transform={`translate(${x},${y})`}>
                        <text 
                          x={0} 
                          y={0} 
                          dy={16} 
                          textAnchor="middle" 
                          fill="#666"
                          fontSize={12}
                          transform={`rotate(${mainChartData.length > 5 ? -45 : 0})`}
                        >
                          {payload.value.length > 15 
                            ? `${payload.value.substring(0, 12)}...` 
                            : payload.value}
                        </text>
                      </g>
                    )}
                    height={mainChartData.length > 5 ? 70 : 30}
                    tickMargin={mainChartData.length > 5 ? 20 : 5}
                  />
                  <YAxis allowDecimals={false} />
                  <ReTooltip 
                    formatter={(value: any) => [`${value} medicines`]} 
                    labelFormatter={(label: any) => `${label}`}
                  />
                  <Bar 
                    dataKey="value" 
                    fill="#fb923c"
                    onClick={(data: any) => {
                      if (data && data.payload) {
                        handleChartClick(mainChartCategory.id, data.payload.name);
                      }
                    }}
                    cursor="pointer"
                  >
                    {mainChartData.map((entry, index) => {
                      const isActive = chartFilters.some(
                        filter => filter.category === mainChartCategory.id && filter.value === entry.name
                      );
                      return (
                        <Cell 
                          key={`cell-${index}`} 
                          fill={COLORS[index % COLORS.length]} 
                          stroke={isActive ? "#000" : undefined}
                          strokeWidth={isActive ? 2 : undefined}
                        />
                      );
                    })}
                  </Bar>
                </BarChart>
              ) : (
                <RePieChart>
                  <RePie
                    data={mainChartData.length > 10 ? mainChartData.slice(0, 10) : mainChartData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={120}
                    fill="#8884d8"
                    dataKey="value"
                    nameKey="name"
                    label={({ name, percent }: { name: string; percent: number }) => 
                      name.length > 15 
                        ? `${name.substring(0, 12)}...: ${(percent * 100).toFixed(0)}%` 
                        : `${name}: ${(percent * 100).toFixed(0)}%`
                    }
                    onClick={(data: any, index: number) => {
                      if (data && data.name) {
                        handleChartClick(mainChartCategory.id, data.name);
                      }
                    }}
                    cursor="pointer"
                  >
                    {(mainChartData.length > 10 ? mainChartData.slice(0, 10) : mainChartData).map((entry, index) => {
                      const isActive = chartFilters.some(
                        filter => filter.category === mainChartCategory.id && filter.value === entry.name
                      );
                      return (
                        <Cell 
                          key={`cell-${index}`} 
                          fill={COLORS[index % COLORS.length]} 
                          stroke={isActive ? "#000" : undefined}
                          strokeWidth={isActive ? 2 : undefined}
                        />
                      );
                    })}
                  </RePie>
                  <ReTooltip formatter={(value: any, name: any) => [`${value} medicines`, name]} />
                  <ReLegend 
                    onClick={(data: any) => {
                      if (data && data.value) {
                        handleChartClick(mainChartCategory.id, data.value);
                      }
                    }}
                  />
                </RePieChart>
              );
            })()}
          </ResponsiveContainer>
        </div>
      </div>

      {/* Secondary charts section */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Additional Insights</h2>
        <p className="text-sm text-muted-foreground mb-4">
          Click on chart segments to filter the medicines. Each chart shows data filtered by your previous selections.
        </p>
        
        {(() => {
          // Define chart data type
          type ChartDataItem = {
            id: string;
            label: string;
            data: { name: string; value: number }[];
            useBarChart: boolean;
            chartHeight: number;
            isPriority: boolean;
          };
          
          // Prepare all chart data
          const allChartData: ChartDataItem[] = chartCategories
            .filter(category => category.id !== mainChartCategory.id)
            .map(category => {
              const data = generateChartData(category.id, filteredMedicines);
              if (!data || data.length === 0) return null;
              
              const useBarChart = data.length > 7;
              const chartHeight = useBarChart ? Math.max(180, data.length * 30) : 180;
              
              return {
                id: category.id,
                label: category.label,
                data,
                useBarChart,
                chartHeight,
                isPriority: [
                  "Pharmacotherapeutic group\n(human)",
                  "Therapeutic area (MeSH)",
                  "Medicine status"
                ].includes(category.id)
              };
            })
            .filter((chart): chart is ChartDataItem => chart !== null);
          
          // Separate priority charts and other charts
          const priorityCharts = allChartData.filter(chart => chart.isPriority);
          const otherCharts = allChartData.filter(chart => !chart.isPriority);
          
          // Sort other charts to prioritize pie charts for the first row
          otherCharts.sort((a, b) => {
            if (a.useBarChart && !b.useBarChart) return 1;
            if (!a.useBarChart && b.useBarChart) return -1;
            return 0;
          });
          
          // Determine how many charts to move to the first row
          const chartsNeededForFirstRow = Math.max(0, 3 - priorityCharts.length);
          const firstRowOtherCharts = otherCharts.slice(0, chartsNeededForFirstRow);
          const remainingCharts = otherCharts.slice(chartsNeededForFirstRow);
          
          // Combine for first row, ensuring priority charts are on the left and center
          const firstRowCharts = [...priorityCharts, ...firstRowOtherCharts];
          
          return (
            <>
              {/* First row - priority charts and pie charts */}
              {firstRowCharts.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                  {firstRowCharts.map(chart => (
                    <ChartComponent 
                      key={chart.id}
                      title={chart.label} 
                      data={chart.data} 
                      category={chart.id} 
                      chartType={chart.useBarChart ? "bar" : "pie"}
                      height={chart.chartHeight}
                    />
                  ))}
                </div>
              )}
              
              {/* Remaining charts */}
              {remainingCharts.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {remainingCharts.map(chart => (
                    <ChartComponent 
                      key={chart.id}
                      title={chart.label} 
                      data={chart.data} 
                      category={chart.id} 
                      chartType={chart.useBarChart ? "bar" : "pie"}
                      height={chart.chartHeight}
                    />
                  ))}
                </div>
              )}
            </>
          );
        })()}
      </div>

      {favoriteMedicines.length > 0 && (
        <div className="mb-8" id="saved-medications-section">
          <h2 className="text-xl font-bold mb-2">Saved Medications</h2>
          <Accordion
            type="multiple"
            className="w-full"
            onValueChange={(v) => setSavedOpenItems(v as string[])}
          >
            {favoriteMedicines.map((med, idx) => {
              const value = `saved-${med["Name of medicine"]}-${idx}`;
              return (
                <MedicineItem
                  key={value}
                  value={value}
                  med={med}
                  isOpen={savedOpenItems.includes(value)}
                />
              );
            })}
          </Accordion>
        </div>
      )}

      {compareMedicines.length > 0 && (
        <div className="mb-8 overflow-auto space-y-2">
          <div className="flex items-center justify-between gap-2 flex-wrap">
            <h2 className="text-xl font-bold">Comparison</h2>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={summarizeComparison}
                disabled={loadingSummary}
                className="gap-1"
              >
                {loadingSummary ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" /> Summarizing...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4" /> Summarize
                  </>
                )}
              </Button>
              <Button variant="outline" size="sm" onClick={() => setCompareNames([])}>
                Clear
              </Button>
            </div>
          </div>
          {contextDocs.length > 0 && (
            <div className="flex flex-wrap gap-2 text-xs mt-1">
              {contextDocs.map((d) => (
                <span key={d.url} className="px-2 py-1 bg-slate-100 dark:bg-slate-800 rounded inline-flex items-center gap-1">
                  {d.title}
                  <button
                    onClick={() => removeContextDoc(d.url)}
                    className="hover:text-red-600"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </span>
              ))}
            </div>
          )}
          <table className="min-w-full text-sm border rounded-md overflow-hidden">
            <thead className="bg-muted">
              <tr>
                <th className="border px-3 py-2 w-32 text-left align-top">Treatment</th>
                {compareMedicines.map((m) => (
                  <th
                    key={m["Name of medicine"]}
                    className="border px-3 py-2 w-60 text-left align-top"
                  >
                    {m["Name of medicine"]}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-border">
              <tr>
                <td className="border px-3 py-2 font-medium w-32 align-top">Status</td>
                {compareMedicines.map((m) => (
                  <td key={m["Name of medicine"]} className="border px-3 py-2 w-60 align-top">
                    {m["Medicine status"]}
                  </td>
                ))}
              </tr>
              <tr>
                <td className="border px-3 py-2 font-medium w-32 align-top">INN</td>
                {compareMedicines.map((m) => (
                  <td key={m["Name of medicine"]} className="border px-3 py-2 w-60 align-top">
                    {m["International non-proprietary name (INN) / common name"] || "-"}
                  </td>
                ))}
              </tr>
              <tr>
                <td className="border px-3 py-2 font-medium w-32 align-top">Active substance</td>
                {compareMedicines.map((m) => (
                  <td key={m["Name of medicine"]} className="border px-3 py-2 w-60 align-top">
                    {m["Active substance"] || "-"}
                  </td>
                ))}
              </tr>
              <tr>
                <td className="border px-3 py-2 font-medium w-32 align-top">Indication</td>
                {compareMedicines.map((m) => (
                  <td key={m["Name of medicine"]} className="border px-3 py-2 w-60 align-top">
                    {m["Therapeutic indication"] || "-"}
                  </td>
                ))}
              </tr>
              <tr>
                <td className="border px-3 py-2 font-medium w-32 align-top">Actions</td>
                {compareMedicines.map((m) => (
                  <td key={m["Name of medicine"]} className="border px-3 py-2 w-60 align-top">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 px-2"
                      onClick={() =>
                        setCompareNames((prev) => prev.filter((n) => n !== m["Name of medicine"]))
                      }
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </td>
                ))}
              </tr>
            </tbody>
          </table>
          {comparisonSummary && (
            <div className="mt-4 p-2 rounded-md bg-blue-50 dark:bg-blue-900/40 relative">
              <button
                onClick={() => setComparisonSummary("")}
                className="absolute top-1 right-1 text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-800 rounded-full p-1"
                aria-label="Close comparison summary"
              >
                <X className="h-3 w-3" />
              </button>
              <p className="font-semibold text-blue-700 dark:text-blue-300 flex items-center gap-1 mb-1">
                <Sparkles className="h-4 w-4" /> Comparison Summary
              </p>
              <ul className="list-[circle] pl-4 text-sm text-blue-700 dark:text-blue-300">
                {parseBullets(comparisonSummary).map((b, i) => (
                  <li key={i}>{b}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {filteredMedicines.length === 0 ? (
        <div className="text-center py-12 text-muted-foreground">
          No medicines match your search.
        </div>
      ) : (
        <>
          <div id="medications-section" className="scroll-mt-16">
            <h2 className="text-xl font-semibold mb-4">Medications</h2>
            <div className="mb-4 text-sm">
              Showing {formatNumber(currentItems.length)} of {formatNumber(filteredMedicines.length)} {filteredMedicines.length === 1 ? 'medicine' : 'medicines'}
              {totalPages > 1 && ` (page ${currentPage} of ${totalPages})`}
            </div>
          </div>
          <Accordion
            type="multiple"
            className="w-full"
            onValueChange={(v) => setOpenItems(v as string[])}
          >
            {currentItems.map((med, idx) => {
              const value = `${med["Name of medicine"]}-${idx}`;
              return (
                <MedicineItem
                  key={value}
                  value={value}
                  med={med}
                  isOpen={openItems.includes(value)}
                />
              );
            })}
          </Accordion>
          
          <Pagination />
          <Dialog
            open={!!pdfModal}
            onOpenChange={(open) => !open && setPdfModal(null)}
          >
            <DialogContent
              className={cn(
                "max-w-screen-2xl max-h-[95vh] w-full",
                top === 'pdf' ? "z-[100001]" : "z-40"
              )}
              overlayClassName={top === 'pdf' ? "z-[100000]" : "z-30"}
            >
              <DialogHeader>
                <DialogTitle>PDF Comparison</DialogTitle>
              </DialogHeader>
              <div className={`h-[80vh] grid ${gridCols} gap-4`}>
                {docsToShow.map((doc, idx) => (
                  <PagePdfViewer
                    key={idx}
                    url={doc.url}
                    title={doc.title}
                    pageInput={pageInputs[doc.url] ?? 1}
                    onPageChange={(val) =>
                      setPageInputs((p) => ({
                        ...p,
                        [doc.url]: val,
                      }))
                    }
                    onSave={() => saveCurrentPage(doc)}
                    onClose={() => {
                      const isComparison = comparisonDocs.some((d) => d.url === doc.url);
                      if (isComparison) {
                        removeComparisonDoc(doc.url);
                        if (docsToShow.length === 1) setPdfModal(null);
                      } else {
                        setPdfModal(null);
                      }
                    }}
                  />
                ))}
              </div>
              <div className="mt-4 flex items-start justify-between gap-4 flex-wrap">
                {savedPages.length > 0 && (
                  <div className="flex items-center gap-2 overflow-x-auto flex-1">
                    {savedPages.map((p) => (
                      <span
                        key={p.id}
                        className="px-2 py-1 text-sm border rounded whitespace-nowrap"
                      >
                        {p.title} (p{p.page})
                      </span>
                    ))}
                    <Button size="sm" variant="ghost" onClick={() => setSavedPages([])}>
                      Reset
                    </Button>
                    <Button
                      size="sm"
                      onClick={downloadSavedPages}
                      className="bg-amber-400 text-black hover:bg-amber-500 whitespace-nowrap"
                    >
                      Download Saved Pages
                    </Button>
                  </div>
                )}
                <div className="flex items-center gap-2 ml-auto justify-end">
                  {pdfModal && comparisonDocs.length < 2 && (
                    <Button
                      size="sm"
                      onClick={() => {
                        if (pdfModal) {
                          addComparisonDoc(pdfModal.url, pdfModal.title);
                          setPdfModal(null);
                        }
                      }}
                      className="bg-amber-400 text-black hover:bg-amber-500"
                    >
                      Add Another Document
                    </Button>
                  )}
                  <Button size="sm" variant="outline" onClick={() => setPdfModal(null)}>
                    Close
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </>
      )}
      </div>
      <ClipboardPanel
        zIndex={top === 'clipboard' ? 100001 : 99999}
        onFocus={() => setTop('clipboard')}
      />
    </>
  );
}

export default function MedicinesExplorerPage() {
  return (
    <ClipboardProvider storageKey="emaNotes">
      <MedicinesExplorerContent />
    </ClipboardProvider>
  );
}
