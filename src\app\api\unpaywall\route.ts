import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const doi = request.nextUrl.searchParams.get('doi');
  if (!doi) {
    return NextResponse.json({ error: 'Missing doi parameter' }, { status: 400 });
  }
  try {
    const url = `https://api.unpaywall.org/v2/${encodeURIComponent(doi)}?email=<EMAIL>`;
    const res = await fetch(url);
    if (!res.ok) throw new Error('Unpaywall request failed');
    const data = await res.json();
    return NextResponse.json(data, { headers: { 'Cache-Control': 'no-store' } });
  } catch (err) {
    console.error('Unpaywall fetch error', err);
    // Return a minimal placeholder when external requests fail so the UI
    // doesn't break in offline environments.
    return NextResponse.json(
      { is_oa: false, oa_status: null, best_oa_location: null },
      { headers: { 'Cache-Control': 'no-store' } }
    );
  }
}
