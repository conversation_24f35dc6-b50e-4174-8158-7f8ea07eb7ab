"use client";

import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  ChangeEvent,
  DragEvent,
} from "react";
import { SubpageHeader } from "@/components/SubpageHeader";
import { Button } from "@/components/ui/button";
import { Toolt<PERSON>, Toolt<PERSON>Content, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import ReactDiffViewer from "react-diff-viewer-continued";
import {
  ArrowDownUp,
  ArrowLeftRight,
  Upload,
  Loader2,
  Plus,
  Minus,
} from "lucide-react";
import { getDocument, GlobalWorkerOptions, version as pdfjsVersion } from "pdfjs-dist";

GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsVersion}/pdf.worker.min.js`;

async function extractText(file: File): Promise<string> {
  const formData = new FormData();
  formData.append('file', file);
  const response = await fetch('/api/pdf-to-markdown', {
    method: 'POST',
    body: formData,
  });
  if (response.ok) {
    const data = await response.json();
    return data.text || '';
  }
  throw new Error('Text extractions failed');
}

interface ViewerState {
  file: File | null;
  url: string;
  text: string;
}

export default function PdfDiffPage() {
  const [viewers, setViewers] = useState<ViewerState[]>([
    { file: null, url: "", text: "" },
    { file: null, url: "", text: "" },
  ]);
  const viewersRef = useRef(viewers);
  const fileInputs = [useRef<HTMLInputElement>(null), useRef<HTMLInputElement>(null)];
  const [isComparing, setIsComparing] = useState(false);
  const [locked, setLocked] = useState<number | null>(null);
  const [diffFontSize, setDiffFontSize] = useState(12);
  const [diffLineHeight, setDiffLineHeight] = useState(1.2);
  const diffStyles = useMemo(
    () => ({ diffContainer: { pre: { lineHeight: diffLineHeight } } }),
    [diffLineHeight]
  );

  useEffect(() => {
    viewersRef.current = viewers;
  }, [viewers]);

  useEffect(() => {
    // revoke object URLs on unmount
    return () => {
      viewersRef.current.forEach((v) => v.url && URL.revokeObjectURL(v.url));
    };
  }, []);

  const applyFile = (index: number, file: File | null) => {
    setViewers((prev) => {
      const next = [...prev];
      const old = next[index];
      if (old.url) URL.revokeObjectURL(old.url);
      next[index] = {
        file,
        url: file ? URL.createObjectURL(file) : "",
        text: "",
      };
      return next;
    });
    setLocked(null);
  };

  const handleUpload = (index: number, e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    applyFile(index, file);
  };

  const handleDrop = (
    index: number,
    e: DragEvent<HTMLLabelElement | HTMLDivElement>
  ) => {
    e.preventDefault();
    const file = e.dataTransfer.files?.[0] || null;
    if (file && file.type === "application/pdf") {
      applyFile(index, file);
    }
  };

  const handleClear = (index: number) => {
    setViewers((prev) => {
      const next = [...prev];
      const old = next[index];
      if (old.url) URL.revokeObjectURL(old.url);
      next[index] = { file: null, url: "", text: "" };
      return next;
    });
    if (locked === index) setLocked(null);
  };

  const handleCompare = async () => {
    if (!viewers[0].file || !viewers[1].file) return;
    setIsComparing(true);
    try {
      const [t0, t1] = await Promise.all([
        extractText(viewers[0].file),
        extractText(viewers[1].file),
      ]);
      setViewers((prev) => [
        { ...prev[0], text: t0 },
        { ...prev[1], text: t1 },
      ]);
    } finally {
      setIsComparing(false);
    }
  };

  const diffReady = viewers[0].text !== "" && viewers[1].text !== "";

  const renderViewer = (index: number) => {
    const v = viewers[index];
    const isLocked = locked === index;
    const otherLocked = locked !== null && locked !== index;
    const icon = otherLocked ? <ArrowLeftRight className="h-4 w-4" /> : <ArrowDownUp className="h-4 w-4" />;
    const toggleLock = () => {
      setLocked(isLocked ? null : index);
    };

    if (!v.url) {
      return (
        <label
          htmlFor={`pdf-upload-${index}`}
          className="border rounded-lg flex flex-col items-center justify-center h-[70vh] bg-gray-100 dark:bg-gray-800 text-gray-500 gap-2 cursor-pointer"
          onDrop={(e) => handleDrop(index, e)}
          onDragOver={(e) => e.preventDefault()}
        >
          <Upload className="h-8 w-8" />
          <span className="text-center px-2">
            {index === 0
              ? "Drag and drop a PDF (previous version) here to upload"
              : "Drag and drop a PDF (current version) here to upload"}
          </span>
          <input
            id={`pdf-upload-${index}`}
            ref={fileInputs[index]}
            type="file"
            accept="application/pdf"
            onChange={(e) => handleUpload(index, e)}
            className="hidden"
          />
        </label>
      );
    }

    return (
      <div className="border rounded-lg p-2 flex flex-col h-[70vh] -mt-1">
        <div className="flex justify-end items-center mb-1">
          <div className="flex gap-2" />
        </div>
        <div
          className="flex-1 bg-gray-100 dark:bg-gray-800 rounded-md overflow-hidden -mt-1"
          onDrop={(e) => handleDrop(index, e)}
          onDragOver={(e) => e.preventDefault()}
        >
          <iframe
            src={`/pdf-frame?url=${encodeURIComponent(v.url)}&page=1&title=${encodeURIComponent(v.file?.name || "")}`}
            className="w-full h-full"
            style={{ display: "block" }}
          />
        </div>
        <div className="flex gap-2 -mt-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => fileInputs[index].current?.click()}
            className="flex-1"
          >
            <Upload className="h-4 w-4 mr-2" /> Upload
          </Button>
          {diffReady && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="secondary" size="sm" onClick={toggleLock}>
                    {icon}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{isLocked ? "Show original PDF" : otherLocked ? "Show diff here" : "Show diff here"}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
        <input
          id={`viewer-input-${index}`}
          ref={fileInputs[index]}
          type="file"
          accept="application/pdf"
          onChange={(e) => handleUpload(index, e)}
          className="hidden"
        />
      </div>
    );
  };

  const renderDiff = () => (
    <div
      className="h-[70vh] overflow-y-auto border rounded-lg p-4"
      style={{ fontSize: `${diffFontSize}px` }}
    >
      <ReactDiffViewer
        oldValue={viewers[0].text}
        newValue={viewers[1].text}
        splitView={true}
        showDiffOnly={false}
        styles={diffStyles}
      />
    </div>
  );

  const renderSingleDiff = (index: number) => {
    const other = index === 0 ? 1 : 0;
    const isLocked = locked === index;
    const otherLocked = locked !== null && locked !== index;
    const icon = otherLocked ? (
      <ArrowLeftRight className="h-4 w-4" />
    ) : (
      <ArrowDownUp className="h-4 w-4" />
    );
    const toggleLock = () => setLocked(isLocked ? null : index);

    return (
      <div className="border rounded-lg p-2 flex flex-col h-[70vh] -mt-1">
        <div className="flex justify-end items-center mb-1">
          <div className="flex gap-2" />
        </div>
        <div
          className="flex-1 bg-gray-100 dark:bg-gray-800 rounded-md overflow-y-auto -mt-1 pb-4"
          style={{ fontSize: `${diffFontSize}px` }}
        >
        <ReactDiffViewer
          oldValue={viewers[index].text}
          newValue={viewers[other].text}
          splitView={false}
          showDiffOnly={false}
          styles={diffStyles}
        />
        </div>
        <div className="flex gap-2 -mt-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => fileInputs[index].current?.click()}
            className="flex-1"
          >
            <Upload className="h-4 w-4 mr-2" /> Upload
          </Button>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="secondary" size="sm" onClick={toggleLock}>
                  {icon}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>
                  {isLocked
                    ? "Show original PDF"
                    : otherLocked
                    ? "Show diff here"
                    : "Show diff here"}
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <input
          id={`viewer-input-${index}`}
          ref={fileInputs[index]}
          type="file"
          accept="application/pdf"
          onChange={(e) => handleUpload(index, e)}
          className="hidden"
        />
      </div>
    );
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <SubpageHeader current="document-comparator" />
      <h1 className="text-3xl font-bold mb-6">Document Comparator</h1>
      <div className="flex gap-4 items-end mb-4">
        <Button
          onClick={handleCompare}
          disabled={!viewers[0].file || !viewers[1].file || isComparing}
          className="bg-amber-400 text-black hover:bg-amber-500"
        >
          {isComparing ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : null}
          {isComparing ? "Comparing" : "Compare"}
        </Button>
        <div className="flex flex-col items-center gap-1">
          <span className="text-xs">Font Size</span>
          <div className="flex gap-1">
            <Button
              variant="outline"
              size="icon"
              onClick={() => setDiffFontSize((v) => Math.max(8, v - 1))}
            >
              A-
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={() => setDiffFontSize((v) => v + 1)}
            >
              A+
            </Button>
          </div>
        </div>
        <div className="flex flex-col items-center gap-1">
          <span className="text-xs">Line Spacing</span>
          <div className="flex gap-1">
            <Button
              variant="outline"
              size="icon"
              onClick={() =>
                setDiffLineHeight((v) => Math.max(1, parseFloat((v - 0.1).toFixed(1))))
              }
            >
              <Minus className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={() =>
                setDiffLineHeight((v) => parseFloat((v + 0.1).toFixed(1)))
              }
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        {locked === 0 ? renderSingleDiff(0) : renderViewer(0)}
        {locked === 1 ? renderSingleDiff(1) : renderViewer(1)}
      </div>
      {diffReady && locked === null && renderDiff()}
    </div>
  );
}
