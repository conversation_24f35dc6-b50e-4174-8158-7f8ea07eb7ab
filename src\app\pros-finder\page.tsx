"use client";

import React, { useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { SubpageHeader } from "@/components/SubpageHeader";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip";

interface ProEntry {
  instrument: string;
  fullName: string;
  objective: string;
  disease: string;
}

export default function ProsFinderPage() {
  const [pros, setPros] = useState<ProEntry[]>([]);
  const [instrument, setInstrument] = useState("");
  const [objective, setObjective] = useState("");
  const [disease, setDisease] = useState("");

  useEffect(() => {
    fetch("/pros-sample.json")
      .then((res) => res.json())
      .then(setPros)
      .catch((err) => console.error("Failed to load PROs", err));
  }, []);

  const filtered = pros.filter((p) =>
    p.instrument.toLowerCase().includes(instrument.toLowerCase()) &&
    p.objective.toLowerCase().includes(objective.toLowerCase()) &&
    p.disease.toLowerCase().includes(disease.toLowerCase())
  );

  return (
    <div className="container mx-auto py-8 px-4">
      <SubpageHeader current="pros-finder" />
      <h1 className="text-3xl font-bold mb-6">Patient-Reported Outcomes Finder</h1>

      <div className="grid gap-4 sm:grid-cols-3 mb-6">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Input
                placeholder="Instrument"
                value={instrument}
                onChange={(e) => setInstrument(e.target.value)}
              />
            </TooltipTrigger>
            <TooltipContent>
              Search of instrument name and abbreviation
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Input
                placeholder="Objective"
                value={objective}
                onChange={(e) => setObjective(e.target.value)}
              />
            </TooltipTrigger>
            <TooltipContent>Purpose of the PRO measure</TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Input
                placeholder="Disease"
                value={disease}
                onChange={(e) => setDisease(e.target.value)}
              />
            </TooltipTrigger>
            <TooltipContent>
              Condition for which the PRO measure is intended.
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      <div className="space-y-4">
        {filtered.map((p, idx) => (
          <div key={idx} className="border rounded p-4">
            <h3 className="font-semibold">
              {p.instrument}
              {p.fullName && (
                <span className="ml-2 text-sm text-muted-foreground">
                  ({p.fullName})
                </span>
              )}
            </h3>
            <p className="text-sm">{p.objective}</p>
            <p className="text-sm text-muted-foreground">{p.disease}</p>
          </div>
        ))}
        {filtered.length === 0 && <p>No results found.</p>}
      </div>
    </div>
  );
}
