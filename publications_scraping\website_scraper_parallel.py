import asyncio
import os
import re
from crawl4ai import Async<PERSON><PERSON><PERSON><PERSON><PERSON>, CacheMode

folder_name = "publications_scraping/pub_scrape"
os.makedirs(folder_name, exist_ok=True)

def sanitize_filename(s: str) -> str:
    """
    Replace any character that isn't alphanumeric, dot, hyphen or underscore with '_'.
    This preserves the full URL text while guaranteeing a valid filename.
    """
    return re.sub(r'[^A-Za-z0-9._-]', '_', s)

async def process_site(site: str, crawler: AsyncWebCrawler) -> bool:
    filename = sanitize_filename(site)
    print(f"→ Processing {site!r}  (saving as `{filename}.md`)")
    try:
        result = await crawler.arun(url=site)
        if result and result.markdown:
            out_path = os.path.join(folder_name, f"{filename}.md")
            with open(out_path, "w", encoding="utf-8") as f:
                f.write(result.markdown)
            print(f"[OK] Content saved to {out_path}")
        else:
            print(f"[WARNING] No content extracted from {site}")
        return True
    except Exception as e:
        print(f"[ERROR] Failed to process {site}: {e}")
        return False

async def main():
    # load your URLs
    with open("publications_scraping/links_individual.txt", "r", encoding="utf-8") as f:
        drug_sites = [line.strip() for line in f if line.strip()]

    async with AsyncWebCrawler() as crawler:
        # limit to, say, 5 concurrent pages at once:
        sem = asyncio.Semaphore(5)

        async def sem_task(site):
            async with sem:
                return await process_site(site, crawler)

        # fire them all off in parallel
        tasks = [asyncio.create_task(sem_task(site)) for site in drug_sites]
        # wait until everything’s done
        await asyncio.gather(*tasks)

if __name__ == "__main__":
    asyncio.run(main())
