# Repository Guide for Codex Agents

This repo hosts a Next.js application (TypeScript) alongside Python utilities for document processing and scraping.

## Quick Start
- Node.js 18.18.0 is required (`package.json` specifies this).
- Install dependencies with `npm install` and run the dev server using `npm run dev`.
- Lint and type-check with `npm run lint` and `npm run typecheck`.
- Python tools depend on packages listed in `requirements.txt` – install with `pip install -r requirements.txt`.

## Key Directories
- `src/` – main Next.js app and components.
- `public/` – static assets and JSON datasets.
- `scripts/` – Python scripts (PDF to markdown, PowerPoint chart generator, workflow runner).
- `web_search_and_scraping/` and `publications_scraping/` – scraping utilities and data management.

## Python Utilities
- `scripts/pdf_to_markdown.py` converts PDFs to Markdown using `pymupdf4llm`.
- `scripts/ppt_chart_generator.py` builds PowerPoint charts from JSON.
- `scripts/workflow_runner.py` executes multi‑agent workflows defined via JSON.
- Several scripts require environment variables such as `OPENAI_API_KEY`.

## Do's
- Keep dependencies up to date and run lint/typecheck before committing.
- Use environment variables for credentials; never commit `.env` or secret keys.
- Start the workflow runner with `python scripts/workflow_runner.py` when using the web workflow designer (see README lines 66‑69).

## Don'ts
- Do not commit build artifacts (`.next`, `out`, `node_modules`).
- Avoid adding large generated files like PPTX or PDF outputs.
- Never commit secret keys or `.env` contents.
