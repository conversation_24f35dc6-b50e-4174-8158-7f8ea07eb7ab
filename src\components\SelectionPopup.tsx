"use client";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import React, { useEffect, useRef, useState } from "react";

export interface SelectionPopupData {
  text: string;
  x: number;
  y: number;
  image?: string;
}

export interface SelectionPopupProps
  extends React.HTMLAttributes<HTMLDivElement> {
  data: SelectionPopupData;
  reference: string;
  source?: string;
  onReferenceChange: (val: string) => void;
  onSourceChange?: (val: string) => void;
  onSave: () => void;
  onCancel: () => void;
}

export default function SelectionPopup({
  data,
  reference,
  source,
  onReferenceChange,
  onSourceChange,
  onSave,
  onCancel,
  className,
  style,
  ...props
}: SelectionPopupProps) {
  const popupRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState({ top: data.y, left: data.x });

  useEffect(() => {
    if (popupRef.current) {
      const rect = popupRef.current.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const viewportWidth = window.innerWidth;
      
      let newTop = data.y;
      let newLeft = data.x;
      
      // Check if popup goes beyond viewport bottom
      if (rect.bottom > viewportHeight) {
        // Position popup above the cursor if it fits, otherwise cap at bottom
        if (data.y - rect.height > 0) {
          newTop = data.y - rect.height;
        } else {
          newTop = viewportHeight - rect.height - 20; // 20px padding from bottom
        }
      }
      
      // Check if popup goes beyond viewport right
      if (rect.right > viewportWidth) {
        newLeft = viewportWidth - rect.width - 20; // 20px padding from right
      }
      
      // Ensure popup doesn't go beyond viewport top or left
      if (newTop < 10) newTop = 10;
      if (newLeft < 10) newLeft = 10;
      
      setPosition({ top: newTop, left: newLeft });
    }
  }, [data.x, data.y]);

  return (
    <div
      ref={popupRef}
      {...props}
      className={cn("bg-[#fbf7ee] border shadow-lg p-2 fixed max-h-[80vh] overflow-hidden flex flex-col rounded-xl", className)}
      style={{ top: position.top, left: position.left, ...(style || {}) }}
    >
      <div className="overflow-y-auto flex-1 pr-2">
        {data.image && (
          <img src={data.image} alt="selected" className="max-w-xs mb-1" />
        )}
        {data.text && (
          <p className="text-sm mb-1 max-w-xs whitespace-pre-wrap">
            Copy to clipboard: "{data.text}"
          </p>
        )}
      </div>
      <div className="pt-2 border-t mt-2">
        <Input
          value={reference}
          onChange={(e) => onReferenceChange(e.target.value)}
          placeholder="Reference"
          className="mb-2 h-7"
        />
        <Input
          value={source || ''}
          onChange={(e) => onSourceChange?.(e.target.value)}
          placeholder="Source"
          className="mb-2 h-7"
          readOnly={!onSourceChange}
        />
        <div className="flex gap-2 justify-end">
          <Button size="sm" onClick={onSave} className="h-7">
            Save
          </Button>
          <Button size="sm" variant="outline" onClick={onCancel} className="h-7">
            Cancel
          </Button>
        </div>
      </div>
    </div>
  );
}
