// Map of country names to ISO 3166-1 alpha-2 country codes
const countryCodeMap: Record<string, string> = {
  "United States": "US",
  "USA": "US",
  "United Kingdom": "GB",
  "UK": "GB",
  "Canada": "CA",
  "Germany": "DE",
  "France": "FR",
  "Spain": "ES",
  "Italy": "IT",
  "Netherlands": "NL",
  "Belgium": "BE",
  "Switzerland": "CH",
  "Sweden": "SE",
  "Denmark": "DK",
  "Norway": "NO",
  "Finland": "FI",
  "Ireland": "IE",
  "Australia": "AU",
  "New Zealand": "NZ",
  "Japan": "JP",
  "China": "CN",
  "India": "IN",
  "Brazil": "BR",
  "Mexico": "MX",
  "South Africa": "ZA",
  "Singapore": "SG",
  "Israel": "IL",
  "Poland": "PL",
  "Croatia": "HR",
  "Hungary": "HU",
  "Portugal": "PT",
  "Austria": "AT",
  "Bulgaria": "BG",
  "Czechia": "CZ",
  "Czech Republic": "CZ",
  "Malta": "MT",
  "Romania": "RO",
  "Ukraine": "UA",
  "Greece": "GR",
  "Slovakia": "SK",
  "Slovenia": "SI",
  "Luxembourg": "LU",
  "Estonia": "EE",
  "Latvia": "LV",
  "Lithuania": "LT",
  "Liechtenstein": "LI",
  "Cyprus": "CY",
  "Iceland": "IS",
  "Azerbaijan": "AZ",
  "Georgia": "GE",
  "Moldova, Republic of": "MD",
  "Moldova": "MD",
  "Mongolia": "MN",
  "Pakistan": "PK",
  "Türkiye": "TR",
  "Turkey": "TR",
  "Viet Nam": "VN",
  "Vietnam": "VN",
  // Add more countries as needed
};

/**
 * Converts a country name to its ISO 3166-1 alpha-2 country code
 * @param countryName The name of the country
 * @returns The country code or null if not found
 */
export function getCountryCode(countryName: string): string | null {
  if (!countryName) return null;
  
  // Try direct match
  if (countryCodeMap[countryName]) {
    return countryCodeMap[countryName];
  }
  
  // Try case-insensitive match
  const normalizedName = countryName.trim().toLowerCase();
  const entry = Object.entries(countryCodeMap).find(
    ([key]) => key.toLowerCase() === normalizedName
  );
  
  return entry ? entry[1] : null;
}