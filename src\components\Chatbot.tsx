import React, { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { MessageSquare, X, Send, Copy, Check, SquarePen, GripHorizontal } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useTheme } from "@/contexts/ThemeContext";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";

// Add this loading dots component
const LoadingDots = () => (
  <div className="flex items-center space-x-1 px-2 py-1">
    <div className="w-1.5 h-1.5 bg-amber-500 rounded-full animate-bounce" style={{ animationDelay: "0ms" }}></div>
    <div className="w-1.5 h-1.5 bg-amber-500 rounded-full animate-bounce" style={{ animationDelay: "150ms" }}></div>
    <div className="w-1.5 h-1.5 bg-amber-500 rounded-full animate-bounce" style={{ animationDelay: "300ms" }}></div>
  </div>
);

type Message = {
  role: "user" | "assistant";
  content: string;
};

type CopiedMessage = { [key: number]: boolean };

// Add project form type
type ProjectFormData = {
  projectType: string;
  asset: string;
  therapeuticArea: string;
  objectives: string;
  geographicalScope: string;
  deliverables: string;
  intendedUse: string;
  timeline: string;
  budgetRange: string;
  timeframe: string;
  generate: string;
};

export function Chatbot() {
  // Existing state
  const { theme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [copiedMessages, setCopiedMessages] = useState<CopiedMessage>({});
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const MAX_CHARS = 300;
  const [isTemplateFormOpen, setIsTemplateFormOpen] = useState(false);
  
  // Add state for chat mode (form is default)
  const [useFormMode, setUseFormMode] = useState(true);
  
  // Add state to track if we're showing the form or chat
  const [showingForm, setShowingForm] = useState(true);
  
  const containerRef = useRef<HTMLDivElement>(null);
  const formRef = useRef<HTMLDivElement>(null);
  
  const [projectFormData, setProjectFormData] = useState<ProjectFormData>({
    projectType: "",
    asset: "",
    therapeuticArea: "",
    objectives: "",
    geographicalScope: "",
    deliverables: "",
    intendedUse: "",
    timeline: "",
    budgetRange: "",
    timeframe: "",
    generate: ""
  });

  // Handle form input changes
  const handleProjectFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProjectFormData(prev => ({ ...prev, [name]: value }));
  };

  // Generate message from form data and send it directly
  const generateMessageFromForm = async () => {
    // Check if any form fields are filled
    const hasContent = Object.values(projectFormData).some(value => value.trim() !== "");
    
    if (!hasContent) {
      // If no fields are filled, don't send anything
      return;
    }
    
    // Format each field, using "Not applicable" for empty fields
    const formatField = (value: string) => value.trim() ? value : "Not applicable";
    
    // Determine whether to use "a" or "an" based on the first letter of the generate field
    const generateValue = formatField(projectFormData.generate);
    const startsWithVowel = /^[aeiou]/i.test(generateValue);
    const article = startsWithVowel ? "an" : "a";
    
    const message = `Project Type: ${formatField(projectFormData.projectType)}
Asset: ${formatField(projectFormData.asset)}
Therapeutic Area: ${formatField(projectFormData.therapeuticArea)}
Objectives: ${formatField(projectFormData.objectives)}
Geographical Scope: ${formatField(projectFormData.geographicalScope)}
Expected Deliverables: ${formatField(projectFormData.deliverables)}
Intended Use: ${formatField(projectFormData.intendedUse)}
Timeline: ${formatField(projectFormData.timeline)}
Budget Range: ${formatField(projectFormData.budgetRange)}
Response Timeframe: ${formatField(projectFormData.timeframe)}

Above are details relevant to my request. Disregard any fields that are labeled as 'Not applicable.'
Generate ${article} ${generateValue}.`;

    // Set input but don't close the form
    setInput(message);
    
    // Switch to chat view after submitting form
    setShowingForm(false);
    
    // Automatically send the message
    await handleSendMessage(message);
    
    // Reset form fields after sending
    setProjectFormData({
      projectType: "",
      asset: "",
      therapeuticArea: "",
      objectives: "",
      geographicalScope: "",
      deliverables: "",
      intendedUse: "",
      timeline: "",
      budgetRange: "",
      timeframe: "",
      generate: ""
    });
  };

  // Existing useEffect hooks and functions
  useEffect(() => {
    const handleTemplateFormOpen = (e: CustomEvent) => {
      setIsTemplateFormOpen(e.detail.open);
    };
    
    window.addEventListener('template-form-state', handleTemplateFormOpen as EventListener);
    return () => {
      window.removeEventListener('template-form-state', handleTemplateFormOpen as EventListener);
    };
  }, []);

  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  // Reset to form view when switching to form mode
  useEffect(() => {
    if (useFormMode) {
      setShowingForm(true);
    }
  }, [useFormMode]);

  // Existing functions
  const handleCopyMessage = async (content: string, messageIndex: number) => {
    try {
      await navigator.clipboard.writeText(content);
      setCopiedMessages(prev => ({ ...prev, [messageIndex]: true }));
      
      // Reset the copied state after 2 seconds
      setTimeout(() => {
        setCopiedMessages(prev => {
          const newState = { ...prev };
          delete newState[messageIndex];
          return newState;
        });
      }, 2000);
    } catch (error) {
      console.error("Failed to copy text:", error);
    }
  };

  // Modified to accept an optional message parameter
  const handleSendMessage = async (messageText?: string) => {
    const messageToSend = messageText || input;
    if (!messageToSend.trim()) return;
    
    // Add user message
    const userMessage: Message = { role: "user", content: messageToSend };
    setMessages([...messages, userMessage]);
    setInput("");
    setIsLoading(true);
    
    try {
      // Call our API route instead of OpenAI directly
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: [...messages, userMessage].map(msg => ({
            role: msg.role,
            content: msg.content
          })),
        }),
      });
      
      if (!response.ok) {
        throw new Error('API request failed');
      }
      
      const data = await response.json();
      
      // Add assistant response
      const assistantMessage: Message = { 
        role: "assistant", 
        content: data.content || "Sorry, I couldn't process that."
      };
      
      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error("Error calling API:", error);
      setMessages(prev => [...prev, { 
        role: "assistant", 
        content: "Sorry, there was an error processing your request."
      }]);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleChat = () => {
    console.log("Toggle chat clicked, current state:", isOpen);
    setIsOpen(prevState => !prevState);
    // Clear copied states when opening/closing chat
    setCopiedMessages({});
  };

  // Function to go back to form
  const handleBackToForm = () => {
    setShowingForm(true);
  };

  return (
    <>
      <Button
        onClick={toggleChat}
        className="fixed bottom-4 right-4 z-50 rounded-full bg-amber-400 text-black hover:bg-amber-500 shadow-md"
        size="icon"
      >
        <MessageSquare className="h-5 w-5" />
      </Button>
      
      {isOpen && (
        <div 
          className={cn(
            "fixed bottom-16 right-5 w-72 sm:w-80 md:w-[360px] max-w-[calc(100vw-2rem)] bg-card text-card-foreground rounded-lg shadow-lg flex flex-col overflow-hidden border border-border",
            // Allow the container to expand up to nearly full screen height
            "max-h-[calc(100vh-5rem)]",
            // Set a minimum height for Free Chat mode, but allow it to grow
            !useFormMode ? "min-h-[430px]" : "",
            isTemplateFormOpen ? "z-40 opacity-85 pointer-events-none" : "z-[9999]"
          )}
          ref={containerRef}
        >
          <div className="flex items-center justify-between p-3 bg-amber-400">
            <h3 className="font-medium text-black">Chat Assistant</h3>
            <div className="flex items-center gap-2">
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={() => {
                  setMessages([]);
                  setShowingForm(true);
                }}
                className="h-8 w-8 p-0 hover:bg-amber-500/20"
                title="Reset chat"
              >
                <SquarePen className="h-4 w-4 text-black" />
              </Button>
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={toggleChat}
                className="h-8 w-8 p-0 hover:bg-amber-500/20"
              >
                <X className="h-4 w-4 text-black" />
              </Button>
            </div>
          </div>
          
          {/* Mode toggle */}
          <div className="flex items-center justify-between px-3 py-2 border-b border-border">
            <div className="flex items-center space-x-2">
              <Label htmlFor="chat-mode" className="text-xs">Free Chat</Label>
              <Switch 
                id="chat-mode" 
                checked={useFormMode}
                onCheckedChange={setUseFormMode}
              />
              <Label htmlFor="chat-mode" className="text-xs">Form Mode</Label>
            </div>
          </div>
          
          {/* Form and chat container */}
          <div className="flex flex-col flex-1 overflow-hidden">
            {/* Show project form only in form mode and when showingForm is true */}
            {useFormMode && showingForm ? (
              <div 
                className="p-3 overflow-y-auto flex-1"
                ref={formRef}
              >
                {/* <h4 className="font-medium mb-3 text-sm">Project Information</h4> */}
                <div className="space-y-3">
                  <div>
                    <Label htmlFor="projectType" className="text-xs">Project Type</Label>
                    <Input 
                      id="projectType"
                      name="projectType"
                      value={projectFormData.projectType}
                      onChange={handleProjectFormChange}
                      placeholder="e.g., CEA, BIM, SLR"
                      className="h-8 text-sm"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="asset" className="text-xs">Asset</Label>
                    <Input 
                      id="asset"
                      name="asset"
                      value={projectFormData.asset}
                      onChange={handleProjectFormChange}
                      placeholder="e.g., Drug X, Medical Device Y"
                      className="h-8 text-sm"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="therapeuticArea" className="text-xs">Therapeutic Area</Label>
                    <Input 
                      id="therapeuticArea"
                      name="therapeuticArea"
                      value={projectFormData.therapeuticArea}
                      onChange={handleProjectFormChange}
                      placeholder="e.g., Oncology, Cardiovascular"
                      className="h-8 text-sm"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="objectives" className="text-xs">Objectives</Label>
                    <Textarea 
                      id="objectives"
                      name="objectives"
                      value={projectFormData.objectives}
                      onChange={handleProjectFormChange}
                      placeholder="e.g., Demonstrate cost-effectiveness"
                      className="min-h-[60px] text-sm"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="geographicalScope" className="text-xs">Geographical Scope</Label>
                    <Input 
                      id="geographicalScope"
                      name="geographicalScope"
                      value={projectFormData.geographicalScope}
                      onChange={handleProjectFormChange}
                      placeholder="e.g., US, EU5, Global"
                      className="h-8 text-sm"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="deliverables" className="text-xs">Expected Deliverables</Label>
                    <Textarea 
                      id="deliverables"
                      name="deliverables"
                      value={projectFormData.deliverables}
                      onChange={handleProjectFormChange}
                      placeholder="e.g., Report, Model, Manuscript"
                      className="min-h-[60px] text-sm"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="intendedUse" className="text-xs">Intended Use</Label>
                    <Input 
                      id="intendedUse"
                      name="intendedUse"
                      value={projectFormData.intendedUse}
                      onChange={handleProjectFormChange}
                      placeholder="e.g., HTA submission, Publication"
                      className="h-8 text-sm"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="timeline" className="text-xs">Timeline</Label>
                    <Input 
                      id="timeline"
                      name="timeline"
                      value={projectFormData.timeline}
                      onChange={handleProjectFormChange}
                      placeholder="e.g., 3 months, 6 months"
                      className="h-8 text-sm"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="budgetRange" className="text-xs">Budget Range</Label>
                    <Input 
                      id="budgetRange"
                      name="budgetRange"
                      value={projectFormData.budgetRange}
                      onChange={handleProjectFormChange}
                      placeholder="e.g., $50-100k, TBD"
                      className="h-8 text-sm"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="timeframe" className="text-xs">Response Timeframe</Label>
                    <Input 
                      id="timeframe"
                      name="timeframe"
                      value={projectFormData.timeframe}
                      onChange={handleProjectFormChange}
                      placeholder="e.g., 2 weeks, ASAP"
                      className="h-8 text-sm"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="generate" className="text-xs">Generate</Label>
                    <Input 
                      id="generate"
                      name="generate"
                      value={projectFormData.generate}
                      onChange={handleProjectFormChange}
                      placeholder="e.g., RFP, email"
                      className="h-8 text-sm"
                    />
                  </div>
                  
                  <Button 
                    onClick={generateMessageFromForm}
                    className="w-full bg-amber-400 text-black hover:bg-amber-500 mt-2"
                  >
                    Generate Message
                  </Button>
                </div>
              </div>
            ) : (
              <>
                <ScrollArea className="flex-1 overflow-y-auto">
                  <div className="p-3">
                    {messages.length === 0 ? (
                      useFormMode ? (
                        // Don't show any placeholder text in form mode
                        <div></div>
                      ) : (
                        // Show placeholder text only in free chat mode
                        <div className="text-center text-muted-foreground py-8 mt-8">
                          <p>What can I help you draft?</p>
                        </div>
                      )
                    ) : (
                      <div className="space-y-4">
                        {messages.map((message, index) => (
                          <div 
                            key={index} 
                            className={`flex flex-col ${message.role === "user" ? "items-end" : "items-start"}`}
                          >
                            <div 
                              className={cn(
                                "max-w-[85%] rounded-lg px-3 py-2",
                                message.role === "user" 
                                  ? "bg-[#1399FF] text-white" 
                                  : "bg-muted text-foreground dark:bg-gray-800"
                              )}
                            >
                              <div className="break-words whitespace-pre-wrap">
                                {message.content}
                              </div>
                            </div>
                            {message.role === "assistant" && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0 mt-1 hover:bg-amber-500/20"
                                onClick={() => handleCopyMessage(message.content, index)}
                              >
                                {copiedMessages[index] ? (
                                  <Check className="h-3 w-3 text-amber-500" />
                                ) : (
                                  <Copy className="h-3 w-3 text-muted-foreground hover:text-amber-500" />
                                )}
                              </Button>
                            )}
                          </div>
                        ))}
                        
                        {/* Add loading animation when waiting for response */}
                        {isLoading && (
                          <div className="flex flex-col items-start">
                            <div className="bg-muted text-foreground dark:bg-gray-800 rounded-lg">
                              <LoadingDots />
                            </div>
                          </div>
                        )}
                        
                        <div ref={messagesEndRef} />
                      </div>
                    )}
                  </div>
                </ScrollArea>
                
                {/* Back to form button (only in form mode) */}
                {useFormMode && (
                  <div className="p-3 border-t border-border">
                    <Button
                      onClick={handleBackToForm}
                      className="w-full bg-amber-400 text-black hover:bg-amber-500"
                    >
                      Back to Form
                    </Button>
                  </div>
                )}
              </>
            )}
            
            {/* Free chat input - only show in free chat mode */}
            {!useFormMode && (
              <div className="p-3 border-t border-border flex flex-col">
                <Textarea
                  value={input}
                  onChange={(e) => {
                    // Limit input to MAX_CHARS characters
                    if (e.target.value.length <= MAX_CHARS) {
                      setInput(e.target.value);
                    }
                  }}
                  placeholder="Type your message..."
                  className="resize-none min-h-8 h-8 flex-1 mr-2 py-1 mb-1 bg-background"
                  onKeyDown={(e) => {
                    if (e.key === "Enter" && !e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage();
                    }
                  }}
                  maxLength={MAX_CHARS}
                />
                <div className="flex justify-between items-center">
                  <span className="text-xs text-muted-foreground">
                    {input.length}/{MAX_CHARS}
                  </span>
                  <Button 
                    onClick={() => handleSendMessage()} 
                    disabled={isLoading || !input.trim()}
                    className={cn(
                      "bg-transparent hover:bg-amber-600/20",
                      theme === 'dark' 
                        ? input.trim() ? "text-amber-500" : "text-white" 
                        : input.trim() ? "text-amber-500" : "opacity-50 text-white-500"
                    )}
                    size="icon"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
}
