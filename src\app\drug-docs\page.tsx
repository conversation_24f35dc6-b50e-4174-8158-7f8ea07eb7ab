"use client";

import React, { useState, use<PERSON>emo, useEffect, use<PERSON><PERSON>back } from "react";
import Link from "next/link";
import {
  ArrowLeft,
  Search,
  Bookmark,
  BookmarkCheck,
  Save,
  Plus,
  Star,
  X,
  Download,
  Eye,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ThemeToggle } from "@/components/ThemeToggle";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as ReTooltip,
  ResponsiveContainer,
  Legend,
} from "recharts";
import { <PERSON>lt<PERSON>, Toolt<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TooltipTrigger } from "@/components/ui/tooltip";

interface ApplicationDoc {
  id?: string;
  date?: string;
  title?: string;
  type?: string;
  url?: string;
}

interface Submission {
  submission_number?: string;
  submission_type?: string;
  application_docs?: ApplicationDoc[];
}

interface DrugResult {
  openfda?: {
    brand_name?: string[];
    generic_name?: string[];
    application_number?: string[];
    manufacturer_name?: string[];
    route?: string[];
    pharm_class_moa?: string[];
    [key: string]: any;
  };
  sponsor_name?: string;
  submissions?: Submission[];
}

interface SearchItem {
  id: number;
  query: string;
  field: string;
  results: DrugResult[];
  sort: string;
  isSearching: boolean;
  hasRun: boolean;
}

// Simplified independent viewer similar to the HTA reports page
const DrugPdfViewer = React.memo(
  ({
    url,
    title,
    pageInput,
    onPageChange,
    onSave,
    removable,
  }: {
    url: string;
    title: string;
    pageInput: number;
    onPageChange: (val: number) => void;
    onSave: () => void;
    removable?: React.ReactNode;
  }) => {
    const [viewerKey, setViewerKey] = useState(0);

    useEffect(() => {
      setViewerKey((k) => k + 1);
    }, [url, pageInput]);

    return (
      <div className="flex flex-col h-full">
        <div className="relative border rounded-md overflow-hidden flex-1">
          {removable}
          {url ? (
            <iframe
              key={viewerKey}
              src={`/pdf-frame?url=${encodeURIComponent(url)}&page=${pageInput}`}
              className="w-full h-full"
              style={{ display: "block" }}
            />
          ) : (
            <div className="h-full flex items-center justify-center text-gray-500">
              <p>No PDF selected</p>
            </div>
          )}
        </div>
        <div className="mt-2 flex items-center gap-2">
          <Input
            type="number"
            min={1}
            value={pageInput}
            onChange={(e) => onPageChange(parseInt(e.target.value) || 1)}
            className="w-20"
          />
          <Button size="sm" onClick={onSave} disabled={!url}>
            Save Page
          </Button>
        </div>
      </div>
    );
  }
);
DrugPdfViewer.displayName = "DrugPdfViewer";

export default function DrugDocsPage() {
  const [searches, setSearches] = useState<SearchItem[]>([
    { id: Date.now(), query: "", field: "brand", results: [], sort: "date_desc", isSearching: false, hasRun: false },
  ]);
  const [comparisonDocs, setComparisonDocs] = useState<
    { url: string; title: string }[]
  >([]);
  const [pdfModal, setPdfModal] = useState<{ url: string; title: string } | null>(null);
  const [favoriteSearches, setFavoriteSearches] = useState<{ query: string; field: string }[]>([]);
  const [favoriteLinks, setFavoriteLinks] = useState<{ url: string; title: string; date?: string; name?: string }[]>([]);
  const [selectedDocs, setSelectedDocs] = useState<Record<string, boolean>>({});
  const [expandedResults, setExpandedResults] = useState<Record<string, boolean>>({});
  const [savedPages, setSavedPages] = useState<{
    id: string;
    url: string;
    title: string;
    page: number;
  }[]>([]);
  const [pageInputs, setPageInputs] = useState<Record<string, number>>({});

  const docsToShow = useMemo(() => {
    const docs = [...comparisonDocs];
    if (pdfModal) docs.push(pdfModal);
    return docs.slice(0, 3);
  }, [comparisonDocs, pdfModal]);

  const gridCols = useMemo(() => {
    if (docsToShow.length === 1) return "grid-cols-1";
    if (docsToShow.length === 2) return "grid-cols-2";
    return "grid-cols-3";
  }, [docsToShow.length]);

  const addComparisonDoc = (url: string, title: string) => {
    setComparisonDocs((prev) => {
      if (prev.length >= 2) return prev;
      if (prev.some((d) => d.url === url)) return prev;
      return [...prev, { url, title }];
    });
  };

  const removeComparisonDoc = (url: string) => {
    setComparisonDocs((prev) => prev.filter((d) => d.url !== url));
  };

  useEffect(() => {
    const fs = localStorage.getItem("favoriteDrugSearches");
    const fl = localStorage.getItem("favoriteDrugLinks");
    if (fs) setFavoriteSearches(JSON.parse(fs));
    if (fl) setFavoriteLinks(JSON.parse(fl));
  }, []);

  useEffect(() => {
    localStorage.setItem("favoriteDrugSearches", JSON.stringify(favoriteSearches));
  }, [favoriteSearches]);

  useEffect(() => {
    localStorage.setItem("favoriteDrugLinks", JSON.stringify(favoriteLinks));
  }, [favoriteLinks]);

  const runSearch = async (
    index: number,
    query?: string,
    field?: string
  ) => {
    const item = searches[index];
    const searchQuery = query ?? item?.query;
    const searchField = field ?? item?.field;
    if (!searchQuery || !searchQuery.trim()) return;

    // Update only the specific search to show it's searching
    setSearches((prev) =>
      prev.map((s, i) =>
        i === index ? { ...s, isSearching: true } : s
      )
    );

    try {
      const res = await fetch(
        `/api/drugs-docs?query=${encodeURIComponent(
          searchQuery
        )}&field=${searchField}&extra=all`
      );
      const data = await res.json();
      setSearches((prev) =>
        prev.map((s, i) =>
          i === index
            ? { ...s, results: data.results || [], isSearching: false, hasRun: true }
            : s
        )
      );
    } catch (err) {
      console.error("search error", err);
      setSearches((prev) =>
        prev.map((s, i) =>
          i === index ? { ...s, results: [], isSearching: false, hasRun: true } : s
        )
      );
    }
  };

  const removeSearch = (id: number) => {
    setSearches((prev) => prev.filter((s) => s.id !== id));
  };

  const toggleFavoriteLink = (doc: ApplicationDoc, drugName: string) => {
    if (!doc.url) return;
    const exists = favoriteLinks.some((l) => l.url === doc.url);
    setFavoriteLinks((prev) =>
      exists
        ? prev.filter((l) => l.url !== doc.url)
        : [
            ...prev,
            {
              url: doc.url!,
              title: doc.title || doc.type || "Document",
              date: formatDate(doc.date),
              name: drugName,
            },
          ]
    );
  };

  const toggleDocSelected = (url: string, checked: boolean) => {
    setSelectedDocs((prev) => ({ ...prev, [url]: checked }));
  };

  const toggleSelectAll = (searchIndex: number, checked: boolean) => {
    const urls: string[] = [];
    searches[searchIndex].results.forEach((r) => {
      r.submissions?.forEach((s) => {
        s.application_docs?.forEach((d) => {
          if (d.url) urls.push(d.url);
        });
      });
    });
    setSelectedDocs((prev) => {
      const updated = { ...prev };
      urls.forEach((u) => {
        updated[u] = checked;
      });
      return updated;
    });
  };

  const startDetailSearch = (value: string, field: string) => {
    setSearches(prev => {
      const newItem: SearchItem = {
        id: Date.now(),
        query: value,
        field,
        results: [],
        sort: "date_desc",
        isSearching: false,
        hasRun: false,
      };
      const newIndex = prev.length;
      setTimeout(() => runSearch(newIndex, value, field), 0);
      return [...prev, newItem];
    });
  };

  function formatDate(d?: string) {
    if (!d) return undefined;
    return /^\d{8}$/.test(d) ? `${d.slice(0,4)}-${d.slice(4,6)}-${d.slice(6)}` : d;
  }

  function formatLabel(label: string) {
    return label
      .replace(/_/g, " ")
      .replace(/\b\w/g, (c) => c.toUpperCase());
  }

  const renderSearchValues = (values: string[], field: string) => {
    return values.map((v, i) => (
      <React.Fragment key={i}>
        {i > 0 && ", "}
        <button
          type="button"
          onClick={() => startDetailSearch(v, field)}
          className="underline text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
        >
          {v}
        </button>
      </React.Fragment>
    ));
  };

  const downloadSelected = async (searchIndex: number) => {
    const urls: string[] = [];
    searches[searchIndex].results.forEach((r) => {
      r.submissions?.forEach((s) => {
        s.application_docs?.forEach((d) => {
          if (d.url && selectedDocs[d.url]) urls.push(d.url);
        });
      });
    });
    for (const url of urls) {
      try {
        const res = await fetch(`/api/drugs-docs?download=1&url=${encodeURIComponent(url)}`);
        if (!res.ok) continue;
        const blob = await res.blob();
        const a = document.createElement('a');
        a.href = URL.createObjectURL(blob);
        a.download = url.split('/').pop() || 'document';
        document.body.appendChild(a);
        a.click();
        URL.revokeObjectURL(a.href);
        document.body.removeChild(a);
      } catch (err) {
        console.error('Download failed', err);
      }
    }
  };

  const saveCurrentPage = (doc: { url: string; title: string }) => {
    const page = pageInputs[doc.url] || 1;
    setSavedPages((prev) => [
      ...prev,
      {
        id: `${Date.now()}-${Math.random().toString(36).slice(2, 9)}`,
        url: doc.url,
        title: doc.title,
        page,
      },
    ]);
  };

  const downloadSavedPages = async () => {
    if (savedPages.length === 0) return;
    try {
      const res = await fetch("/api/combine-pdf-pages", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          selections: savedPages.map((p) => ({
            url: p.url,
            page: p.page,
          })),
        }),
      });
      if (!res.ok) {
        console.error("Combine request failed", res.status);
        return;
      }
      const blob = await res.blob();
      const a = document.createElement("a");
      a.href = URL.createObjectURL(blob);
      a.download = "saved_pages.pdf";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(a.href);
    } catch (err) {
      console.error("Failed to download combined PDF", err);
    }
  };

  const chartData = useMemo(() => {
    const yearCounts: Record<string, Record<number, number>> = {};

    searches.forEach((search, sIdx) => {
      search.results.forEach((r) => {
        r.submissions?.forEach((s) => {
          s.application_docs?.forEach((d) => {
            if (d.date) {
              const year = d.date.substring(0, 4);
              if (!yearCounts[year]) yearCounts[year] = {};
              yearCounts[year][sIdx] = (yearCounts[year][sIdx] || 0) + 1;
            }
          });
        });
      });
    });

    return Object.entries(yearCounts)
      .map(([year, counts]) => ({ year, ...counts }))
      .sort((a, b) => parseInt(a.year) - parseInt(b.year));
  }, [searches]);

  const searchColors = [
    "#f59e0b",
    "#ef4444",
    "#3b82f6",
    "#10b981",
    "#8b5cf6",
    "#a855f7",
    "#f43f5e",
    "#0ea5e9",
  ];

  // Add a custom tooltip component for the chart
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (!active || !payload || !payload.length) return null;

    return (
      <div className="bg-white dark:bg-gray-800 p-2 border rounded shadow-lg">
        <p className="font-bold">{label}</p>
        {payload.map((p: any, idx: number) => {
          const sIdx = parseInt(p.dataKey, 10);
          const search = searches[sIdx];
          if (!search) return null;
          const docs: ApplicationDoc[] = [];
          search.results.forEach((r) => {
            r.submissions?.forEach((s) => {
              s.application_docs?.forEach((d) => {
                if (d.date?.startsWith(label)) docs.push(d);
              });
            });
          });
          return (
            <div key={sIdx} className="mt-2">
              <p className="font-medium">
                {search.query || `Search ${sIdx + 1}`} ({docs.length})
              </p>
              <ul className="text-xs">
                {docs.slice(0, 5).map((doc, i) => (
                  <li key={i}>
                    {doc.url ? (
                      <a
                        href={doc.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300 underline"
                      >
                        {doc.title || doc.type || "Document"}
                      </a>
                    ) : (
                      doc.title || doc.type || "Document"
                    )}
                  </li>
                ))}
                {docs.length > 5 && <li>...and {docs.length - 5} more</li>}
              </ul>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <>
    <div className="container mx-auto py-8 px-4">
      <div className="flex justify-between items-center mb-6">
        <Link href="/">
          <Button variant="ghost" className="pl-0 hover:bg-transparent">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Evicenter
          </Button>
        </Link>
        <ThemeToggle />
      </div>

      <h1 className="text-3xl font-bold mb-6">FDA Drug Review Document Finder</h1>
      <div className="prose max-w-none mb-8 dark:text-gray-200">
        <p>
          Search the FDA Drugs API for application review documents such as
          approval letters, labels, and medical reviews.
        </p>
      </div>

      <div className="space-y-4 mb-8">
        {searches.map((s, idx) => (
          <form
            key={s.id}
            onSubmit={(e) => {
              e.preventDefault();
              runSearch(idx);
            }}
            className="flex flex-wrap gap-2 items-center"
          >
            <Input
              type="search"
              placeholder="Enter drug name..."
              value={s.query}
              onChange={(e) =>
                setSearches((prev) =>
                  prev.map((p, i) => (i === idx ? { ...p, query: e.target.value } : p))
                )
              }
              className="flex-1 min-w-[200px]"
            />
            <Select
              value={s.field}
              onValueChange={(val) =>
                setSearches((prev) => prev.map((p, i) => (i === idx ? { ...p, field: val } : p)))
              }
            >
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Field" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="brand">Brand</SelectItem>
                <SelectItem value="generic">Generic</SelectItem>
                <SelectItem value="application">Application</SelectItem>
                <SelectItem value="manufacturer">Manufacturer</SelectItem>
              </SelectContent>
            </Select>
            <Button
              type="submit"
              className="bg-amber-400 text-black hover:bg-amber-500"
              disabled={s.isSearching}
            >
              {s.isSearching ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black" />
              ) : (
                <>
                  <Search className="h-4 w-4 mr-2" /> Search
                </>
              )}
            </Button>
            <Button
              type="button"
              variant="ghost"
              onClick={() => {
                const exists = favoriteSearches.some((f) => f.query === s.query && f.field === s.field);
                setFavoriteSearches((prev) =>
                  exists
                    ? prev.filter((f) => !(f.query === s.query && f.field === s.field))
                    : [...prev, { query: s.query, field: s.field }]
                );
              }}
            >
              {favoriteSearches.some((f) => f.query === s.query && f.field === s.field) ? (
                <Bookmark className="h-4 w-4 text-yellow-500" />
              ) : (
                <Save className="h-4 w-4" />
              )}
            </Button>
            <Button type="button" variant="ghost" onClick={() => removeSearch(s.id)}>
              <X className="h-4 w-4" />
            </Button>
          </form>
        ))}
        <Button
          variant="outline"
          onClick={() =>
            setSearches((prev) => [
              ...prev,
              { id: Date.now(), query: "", field: "brand", results: [], sort: "date_desc", isSearching: false, hasRun: false },
            ])
          }
        >
          <Plus className="h-4 w-4 mr-2" /> Add Search
        </Button>
      </div>

      {chartData.length > 0 && (
        <div className="mb-8 h-64">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={chartData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="year" />
              <YAxis allowDecimals={false} />
              <ReTooltip content={<CustomTooltip />} wrapperStyle={{ pointerEvents: 'auto' }} />
              <Legend />
              {searches.map((s, idx) =>
                s.hasRun ? (
                  <Bar
                    key={s.id}
                    dataKey={String(idx)}
                    fill={searchColors[idx % searchColors.length]}
                    name={s.query || `Search ${idx + 1}`}
                  />
                ) : null
              )}
            </BarChart>
          </ResponsiveContainer>
        </div>
      )}


      {favoriteSearches.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-bold mb-2">Saved Searches</h2>
          <div className="flex flex-wrap gap-2">
            {favoriteSearches.map((fs, idx) => (
              <div key={idx} className="flex items-center gap-1 border rounded px-2 py-0">
                <Button
                  variant="link"
                  className="p-0 text-sm"
                  onClick={() => {
                    // Create a new search with the saved query
                    const emptyIndex = searches.findIndex((s) => !s.query.trim());
                    if (emptyIndex !== -1) {
                      setSearches((prev) =>
                        prev.map((p, i) =>
                          i === emptyIndex ? { ...p, query: fs.query, field: fs.field } : p
                        )
                      );
                      runSearch(emptyIndex, fs.query, fs.field);
                    } else {
                      setSearches((prev) => [
                        ...prev,
                        {
                          id: Date.now(),
                          query: fs.query,
                          field: fs.field,
                          results: [],
                          sort: 'date_desc',
                          isSearching: false,
                          hasRun: false,
                        },
                      ]);
                      runSearch(searches.length, fs.query, fs.field);
                    }
                  }}
                >
                  {fs.query} ({fs.field})
                </Button>
                <button
                  onClick={() => {
                    setFavoriteSearches((prev) => prev.filter((_, i) => i !== idx));
                  }}
                  className="text-muted-foreground hover:text-red-500"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {favoriteLinks.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-bold mb-2">Saved Documents</h2>
          <div className="flex flex-wrap gap-2">
            {favoriteLinks.map((fl, idx) => (
              <div key={idx} className="flex items-center gap-1 border rounded px-2 py-0.5">
                <a
                  href={fl.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                >
                  {fl.title}
                </a>
                {fl.date && <span className="text-sm text-muted-foreground ml-1">{fl.date}</span>}
                {fl.name && <span className="text-sm text-muted-foreground ml-1">{fl.name}</span>}
                <button
                  onClick={() => setFavoriteLinks((prev) => prev.filter((_, i) => i !== idx))}
                  className="text-muted-foreground hover:text-red-500"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Update the results display to show side by side and take full width */}
      <div className="flex flex-wrap gap-4 mt-8">
        {searches.map((search, sidx) => (
          <div key={search.id} className="space-y-4 flex-grow min-w-[300px] max-w-[500px] mb-6">
            {search.results.length > 0 && (
              <>
                {/* First row with truncated title */}
                <div className="w-full">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <h3 className="font-bold text-lg truncate" title={search.query}>
                          {search.query}
                        </h3>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{search.query}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                
                {/* Second row with controls - adjusted dropdown size */}
                <div className="flex items-center gap-2 w-full">
                  <Select
                    value={search.sort}
                    onValueChange={(val) =>
                      setSearches((prev) => prev.map((p, i) => (i === sidx ? { ...p, sort: val } : p)))
                    }
                  >
                    <SelectTrigger className="w-[80px] px-2 h-9">
                      <SelectValue placeholder="Sort" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="date_desc">Date ↓</SelectItem>
                      <SelectItem value="date_asc">Date ↑</SelectItem>
                      {/* <SelectItem value="type">Type</SelectItem> */}
                    </SelectContent>
                  </Select>
                  <Checkbox
                    checked={search.results.every(r =>
                      r.submissions?.every(s =>
                        s.application_docs?.every(d => d.url && selectedDocs[d.url]) ?? true
                      ) ?? true
                    )}
                    onCheckedChange={(checked) => toggleSelectAll(sidx, !!checked)}
                  />
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => downloadSelected(sidx)}
                    className="flex items-center gap-1 whitespace-nowrap px-2 h-9"
                  >
                    <Download className="h-4 w-4" />
                    <span className="sm:hidden">Download</span>
                    <span className="hidden sm:inline">Download Selected</span>
                  </Button>
                </div>
              </>
            )}
            {search.results.map((item, idx) => {
              const latestDocs: Record<string, { url?: string; date?: string }> = {};
              item.submissions?.forEach((sub) => {
                sub.application_docs?.forEach((d) => {
                  const t = d.type || "";
                  const existing = latestDocs[t];
                  if (!existing || ((d.date || "") > (existing.date || ""))) {
                    latestDocs[t] = { url: d.url, date: d.date };
                  }
                });
              });
              const cardTitle = item.openfda?.brand_name?.[0] || item.openfda?.generic_name?.[0] || "Unknown";
              return (
              <Card key={`${sidx}-${idx}`} className="relative">
                <Button
                  size="icon"
                  variant="ghost"
                  className="absolute top-2 right-2 z-10 h-6 w-6 p-0"
                  onClick={() => {
                    setSearches(prev =>
                      prev.map((s, i) =>
                        i === sidx ? { ...s, results: s.results.filter((_, ri) => ri !== idx) } : s
                      )
                    );
                  }}
                >
                  <X className="h-4 w-4" />
                </Button>
                <CardHeader>
                  <CardTitle className="text-lg w-full truncate" title={cardTitle}>
                  {cardTitle}
                </CardTitle>
              </CardHeader>
              <CardContent>
                  {item.openfda?.application_number && (
                    <p className="text-sm mb-2">Application: {item.openfda.application_number.join(", ")}</p>
                  )}
                  {item.sponsor_name && (
                    <p className="text-sm mb-2">
                      Sponsor: {" "}
                      <button
                        type="button"
                        onClick={() => startDetailSearch(item.sponsor_name!, "sponsor_name")}
                        className="underline text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                      >
                        {item.sponsor_name}
                      </button>
                    </p>
                  )}
                  {item.openfda?.route && (
                    <p className="text-sm mb-2">
                      Route: {renderSearchValues(item.openfda.route, "route")}
                    </p>
                  )}
                  {item.openfda?.brand_name && (
                    <p className="text-sm mb-2">
                      Brand: {renderSearchValues(item.openfda.brand_name, "brand")}
                    </p>
                  )}
                  {item.openfda?.generic_name && (
                    <p className="text-sm mb-2">
                      Generic: {renderSearchValues(item.openfda.generic_name, "generic")}
                    </p>
                  )}
                  {item.openfda?.pharm_class_moa && (
                    <p className="text-sm mb-2">
                      MOA: {renderSearchValues(item.openfda.pharm_class_moa, "pharm_class_moa")}
                    </p>
                  )}
                  {expandedResults[`${sidx}-${idx}`] && item.openfda && (
                    <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mb-2">
                      <ul className="space-y-1">
                        {Object.entries(item.openfda)
                          .filter(([k]) =>
                            ![
                              "application_number",
                              "route",
                              "brand_name",
                              "generic_name",
                              "pharm_class_moa",
                            ].includes(k)
                          )
                          .map(([k, v]) => (
                            <li key={k}>
                              <span className="font-semibold">{formatLabel(k)}:</span>{" "}
                              {Array.isArray(v)
                                ? renderSearchValues(v as string[], k)
                                : (
                                    <button
                                      type="button"
                                      onClick={() => startDetailSearch(String(v), k)}
                                      className="underline text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                                    >
                                      {String(v)}
                                    </button>
                                  )}
                            </li>
                          ))}
                      </ul>
                    </div>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="mb-2"
                    onClick={() => setExpandedResults(prev => ({ ...prev, [`${sidx}-${idx}`]: !prev[`${sidx}-${idx}`] }))}
                  >
                    {expandedResults[`${sidx}-${idx}`] ? 'Hide Details' : 'Show Details'}
                  </Button>
                  {item.submissions?.map((sub, subIdx) => {
                    const docs = [...(sub.application_docs || [])];
                    if (search.sort === "date_desc") {
                      docs.sort((a, b) => (b.date || "").localeCompare(a.date || ""));
                    } else if (search.sort === "date_asc") {
                      docs.sort((a, b) => (a.date || "").localeCompare(b.date || ""));
                    } else if (search.sort === "type") {
                      docs.sort((a, b) => (a.type || "").localeCompare(b.type || ""));
                    }
                    return (
                      <div key={subIdx} className="mb-4">
                        <p className="font-medium mb-2">
                          Submission {sub.submission_number} {sub.submission_type ? `- ${sub.submission_type}` : ""}
                        </p>
                        {docs.length ? (
                          <ul className="list-disc list-inside space-y-1">
                            {docs.map((doc, didx) => (
                              <li key={didx} className="flex items-center gap-2">
                                {doc.url && (
                                  <Checkbox
                                    checked={!!selectedDocs[doc.url]}
                                    onCheckedChange={(checked) =>
                                      toggleDocSelected(doc.url!, !!checked)
                                    }
                                  />
                                )}
                                <span>
                                  {doc.date ? `${formatDate(doc.date)}: ` : ""}
                                  {doc.url ? (
                                    <a
                                      href={doc.url}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300 underline"
                                    >
                                      {doc.title || doc.type || doc.id || "Document"}
                                    </a>
                                  ) : (
                                    doc.title || doc.type || doc.id || "Document"
                                  )}
                                </span>
                                {doc.type && latestDocs[doc.type]?.url === doc.url && (
                                  <Star className="h-4 w-4 text-yellow-500" />
                                )}
                                {doc.url && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 px-1"
                                    onClick={() => toggleFavoriteLink(doc, item.openfda?.brand_name?.[0] || item.openfda?.generic_name?.[0] || "Unknown")}
                                  >
                                    {favoriteLinks.some((l) => l.url === doc.url) ? (
                                      <BookmarkCheck className="h-4 w-4 text-amber-500" />
                                    ) : (
                                      <Bookmark className="h-4 w-4" />
                                    )}
                                  </Button>
                                )}
                                {doc.url && /\.pdf(\?|$)/i.test(doc.url) && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 px-1"
                                    onClick={() => setPdfModal({ url: doc.url!, title: doc.title || doc.type || doc.id || "Document" })}
                                  >
                                    <Eye className="h-4 w-4" />
                                  </Button>
                                )}
                              </li>
                            ))}
                          </ul>
                        ) : (
                          <p className="text-sm text-muted-foreground">No documents available.</p>
                        )}
                      </div>
                    );
                  })}
                </CardContent>
              </Card>
              );
            })}
          </div>
        ))}
      </div>
    </div>
    <Dialog open={!!pdfModal} onOpenChange={(open) => !open && setPdfModal(null)}>
      <DialogContent className="max-w-screen-2xl max-h-[95vh] w-full">
        <DialogHeader>
          <DialogTitle>PDF Comparison</DialogTitle>
        </DialogHeader>
        <div className={`h-[80vh] grid ${gridCols} gap-4`}>
          {docsToShow.map((doc, idx) => (
            <DrugPdfViewer
              key={idx}
              url={doc.url}
              title={doc.title}
              pageInput={pageInputs[doc.url] ?? 1}
              onPageChange={(val) =>
                setPageInputs((p) => ({
                  ...p,
                  [doc.url]: val,
                }))
              }
              onSave={() => saveCurrentPage(doc)}
              removable={
                comparisonDocs.some((d) => d.url === doc.url) ? (
                  <Button
                    size="icon"
                    variant="ghost"
                    className="absolute top-2 right-2 z-10 h-6 w-6 p-0 bg-white/70"
                    onClick={() => removeComparisonDoc(doc.url)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                ) : undefined
              }
            />
          ))}
        </div>
        <div className="mt-4 flex justify-end gap-2">
          {savedPages.length > 0 && (
            <Button size="sm" onClick={downloadSavedPages} className="bg-amber-400 text-black hover:bg-amber-500">
              Download Saved Pages
            </Button>
          )}
          {pdfModal && comparisonDocs.length < 2 && (
            <Button
              size="sm"
              onClick={() => {
                if (pdfModal) {
                  addComparisonDoc(pdfModal.url, pdfModal.title);
                  setPdfModal(null);
                }
              }}
              className="bg-amber-400 text-black hover:bg-amber-500"
            >
              Add Another Document
            </Button>
          )}
          <Button size="sm" variant="outline" onClick={() => setPdfModal(null)}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
    </>
  );
}

