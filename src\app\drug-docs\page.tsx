"use client";

import React, { useState, useMemo, useEffect, useCallback, useRef } from "react";
import {
  ArrowDown,
  ArrowUp,
  Search,
  Bookmark,
  BookmarkCheck,
  Save,
  Plus,
  Star,
  X,
  Download,
  Eye,
  FileText,
  Sparkles,
  Loader2,
  ChevronDown,
  Trash2,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import SelectionPopup from "@/components/SelectionPopup";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { SubpageHeader } from "@/components/SubpageHeader";
import { useLocalStorage } from "@/hooks/use-local-storage";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as ReTooltip,
  ResponsiveContainer,
  Legend,
} from "recharts";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import Image from "next/image";
import { useTheme } from "@/contexts/ThemeContext";
import PubMedLogo from "@/icons/pubmed_logo.svg?url";
import PubMedLogoDark from "@/icons/pubmed_logo_darkmode.svg?url";
// Use raster icons from the public folder for CT.gov, DailyMed, and MedlinePlus
const CtGovLogo = "/icons/ctg_icon.png";
const DailyMedLogo = "/icons/dailymed_icon.png";
const MedlinePlusLogo = "/icons/medline_icon.png";
const FdaIcon = "/icons/fda_icon.svg";
import { linkClinicalTrials } from "@/lib/linkClinicalTrials";
import { ClipboardProvider, useClipboard } from "@/contexts/ClipboardContext";
import { ClipboardPanel } from "@/components/Clipboard";
import PagePdfViewer from "@/components/PagePdfViewer";
import { cn } from "@/lib/utils";

interface ApplicationDoc {
  id?: string;
  date?: string;
  title?: string;
  type?: string;
  url?: string;
}

interface Submission {
  submission_number?: string;
  submission_type?: string;
  application_docs?: ApplicationDoc[];
}

interface DrugResult {
  openfda?: {
    brand_name?: string[];
    generic_name?: string[];
    application_number?: string[];
    manufacturer_name?: string[];
    route?: string[];
    pharm_class_moa?: string[];
    [key: string]: any;
  };
  sponsor_name?: string;
  submissions?: Submission[];
}

interface SearchItem {
  id: number;
  query: string;
  field: string;
  results: DrugResult[];
  sort: string;
  isSearching: boolean;
  hasRun: boolean;
  startYear?: string;
  endYear?: string;
}


function DrugDocsContent({ bringPdfToFront, pdfOnTop }: { bringPdfToFront: () => void; pdfOnTop: boolean }) {
  const { theme } = useTheme();
  const { addNote } = useClipboard();
  const [searches, setSearches] = useState<SearchItem[]>([
    { id: Date.now(), query: "", field: "brand", results: [], sort: "date_desc", isSearching: false, hasRun: false, startYear: "", endYear: "" },
  ]);
  const [comparisonDocs, setComparisonDocs] = useState<
    { url: string; title: string }[]
  >([]);
  const [pdfModal, setPdfModal] = useState<{ url: string; title: string } | null>(null);

  useEffect(() => {
    if (pdfModal) bringPdfToFront();
  }, [pdfModal, bringPdfToFront]);
  const [favoriteSearches, setFavoriteSearches] = useLocalStorage<{ query: string; field: string }[]>(
    "favoriteDrugSearches",
    [],
  );
  const [favoriteLinks, setFavoriteLinks] = useLocalStorage<{
    url: string;
    title: string;
    date?: string;
    name?: string;
  }[]>("favoriteDrugLinks", []);
  const [storageUsage, setStorageUsage] = useState(0);
  const [showStorageUsage, setShowStorageUsage] = useState(false);
  const [storageCleared, setStorageCleared] = useState(false);
  const calculateUsage = useCallback(() => {
    if (typeof window === "undefined") return 0;
    let total = 0;
    for (let i = 0; i < window.localStorage.length; i++) {
      const key = window.localStorage.key(i);
      if (!key) continue;
      const value = window.localStorage.getItem(key) || "";
      total += new Blob([key]).size + new Blob([value]).size;
    }
    return total;
  }, []);
  useEffect(() => {
    setStorageUsage(calculateUsage());
  }, [favoriteSearches, favoriteLinks, calculateUsage]);

  const clearLocalStorage = () => {
    if (typeof window === "undefined") return;
    window.localStorage.clear();
    setFavoriteSearches([]);
    setFavoriteLinks([]);
    setStorageUsage(0);
    setStorageCleared(true);
    setTimeout(() => setStorageCleared(false), 2000);
  };
  const [selectedDocs, setSelectedDocs] = useState<Record<string, boolean>>({});
  const [expandedResults, setExpandedResults] = useState<Record<string, boolean>>({});
  const [savedPages, setSavedPages] = useState<{
    id: string;
    url: string;
    title: string;
    page: number;
  }[]>([]);
  const [pageInputs, setPageInputs] = useState<Record<string, number>>({});
  const [drugSetIds, setDrugSetIds] = useState<Record<string, string>>({});
  const [labelModal, setLabelModal] = useState<{ drug: string; label: any | null } | null>(null);
  const [comparisonLabels, setComparisonLabels] = useState<{ drug: string; label: any }[]>([]);
  const [selectedFields, setSelectedFields] = useState<string[]>([]);
  const [labelsOpen, setLabelsOpen] = useState(false);
  const [labelComparisonSummary, setLabelComparisonSummary] = useState<string>("");
  const [loadingLabelSummary, setLoadingLabelSummary] = useState(false);
  const labelBottomRef = useRef<HTMLDivElement | null>(null);
  const labelTopRef = useRef<HTMLDivElement | null>(null);
  const [selectionPopup, setSelectionPopup] = useState<{ text: string; source?: string; x: number; y: number; image?: string } | null>(null);
  const [selectionRef, setSelectionRef] = useState<string>("");

  const scrollToLabelBottom = useCallback(() => {
    labelBottomRef.current?.scrollIntoView({ behavior: "smooth" });
  }, []);

  const scrollToLabelTop = useCallback(() => {
    labelTopRef.current?.scrollIntoView({ behavior: "smooth" });
  }, []);

  const saveSelection = () => {
    if (!selectionPopup) return;
    addNote(
      selectionPopup.text,
      selectionPopup.source,
      undefined,
      selectionRef,
      undefined,
      selectionPopup.image,
    );
    window.getSelection()?.removeAllRanges();
    setSelectionPopup(null);
    setSelectionRef("");
  };

  const closeSelectionPopup = () => {
    setSelectionPopup(null);
    setSelectionRef("");
  };

  const docsToShow = useMemo(() => {
    const docs = [...comparisonDocs];
    if (pdfModal) docs.push(pdfModal);
    return docs.slice(0, 3);
  }, [comparisonDocs, pdfModal]);

  const gridCols = useMemo(() => {
    if (docsToShow.length === 1) return "grid-cols-1";
    if (docsToShow.length === 2) return "grid-cols-2";
    return "grid-cols-3";
  }, [docsToShow.length]);

  const resultsGridCols = useMemo(() => {
    if (searches.length === 1) return "grid-cols-1";
    if (searches.length === 2) return "grid-cols-2";
    return "grid-cols-3";
  }, [searches.length]);

  const labelsToShow = useMemo(() => {
    const arr = [...comparisonLabels];
    if (labelModal) {
      if (labelModal.label) arr.push(labelModal as { drug: string; label: any });
    }
    return arr;
  }, [comparisonLabels, labelModal]);

  const getSectionArray = useCallback((label: any) => {
    if (!label) return null;
    const secArr: any = label.sections || label.document?.sections;
    if (!secArr) return null;
    return Array.isArray(secArr) ? secArr : Object.values(secArr);
  }, []);

  const allLabelFields = useMemo(() => {
    const set = new Set<string>();
    labelsToShow.forEach((l) => {
      if (!l.label) return;
      const arr = getSectionArray(l.label);
      if (arr) {
        arr.forEach((sec: any) => {
          if (sec?.title) set.add(sec.title);
        });
      } else {
        Object.keys(l.label).forEach((k) => set.add(k));
      }
    });
    return Array.from(set);
  }, [labelsToShow, getSectionArray]);

  useEffect(() => {
    if (allLabelFields.length && selectedFields.length === 0) {
      setSelectedFields(allLabelFields);
    }
  }, [allLabelFields]);

  useEffect(() => {
    const handler = (e: MouseEvent) => {
      const sel = window.getSelection();
      const text = sel?.toString().trim();
      const anchor = (e.target as HTMLElement).closest('a') as HTMLAnchorElement | null;
      const img = (e.target as HTMLElement).closest('img') as HTMLImageElement | null;
      if (text || anchor || img) {
        e.preventDefault();
        let src =
          anchor?.href ||
          (e.target as HTMLElement).closest('[data-query]')?.getAttribute('data-query') ||
          '';
        if (!src && img) src = img.src;
        if (!src || src === 'highlight') {
          const q = searches.map((s) => s.query).filter(Boolean).join(' ');
          const base = window.location.origin + window.location.pathname;
          src = base + (q ? `?q=${encodeURIComponent(q)}` : '');
        }
        setSelectionRef('');
        setSelectionPopup({
          text: text || anchor?.textContent || '',
          source: src || undefined,
          x: e.clientX,
          y: e.clientY,
          image: img ? img.src : undefined,
        });
      }
    };
    document.addEventListener('contextmenu', handler);
    return () => document.removeEventListener('contextmenu', handler);
  }, [addNote]);

  const addComparisonDoc = (url: string, title: string) => {
    setComparisonDocs((prev) => {
      if (prev.length >= 2) return prev;
      if (prev.some((d) => d.url === url)) return prev;
      return [...prev, { url, title }];
    });
  };

  const removeComparisonDoc = (url: string) => {
    setComparisonDocs((prev) => prev.filter((d) => d.url !== url));
  };


  const runSearch = async (
    index: number,
    query?: string,
    field?: string
  ) => {
    const item = searches[index];
    const searchQuery = query ?? item?.query;
    const searchField = field ?? item?.field;
    const startYear = item?.startYear ? parseInt(item.startYear, 10) : undefined;
    const endYear = item?.endYear ? parseInt(item.endYear, 10) : undefined;
    if (!searchQuery || !searchQuery.trim()) return;

    // Update only the specific search to show it's searching
    setSearches((prev) =>
      prev.map((s, i) =>
        i === index ? { ...s, isSearching: true } : s
      )
    );

    try {
      const res = await fetch(
        `/api/drugs-docs?query=${encodeURIComponent(
          searchQuery
        )}&field=${searchField}&extra=all`
      );
      const data = await res.json();
      const filtered = ((data.results || []) as DrugResult[]).map((r) => {
        const submissions = r.submissions?.map((sub) => ({
          ...sub,
          application_docs: sub.application_docs?.filter((d) => {
            if (!d.date) return true;
            const year = parseInt(d.date.substring(0, 4), 10);
            if (startYear && year < startYear) return false;
            if (endYear && year > endYear) return false;
            return true;
          }) || [],
        })).filter((s) => s.application_docs && s.application_docs.length > 0) || [];
        return { ...r, submissions };
      }).filter((r) => r.submissions && r.submissions.length > 0);
      setSearches((prev) =>
        prev.map((s, i) =>
          i === index
            ? { ...s, results: filtered, isSearching: false, hasRun: true }
            : s
        )
      );
      filtered.forEach(async (r) => {
        const name = r.openfda?.generic_name?.[0] || r.openfda?.brand_name?.[0];
        if (name && !drugSetIds[name]) {
          const lbl = await fetchLatestLabel(name);
          const id = lbl?.set_id || lbl?.spl_set_id;
          if (id) {
            setDrugSetIds((prev) => ({ ...prev, [name]: id }));
          }
        }
      });
    } catch (err) {
      console.error("search error", err);
      setSearches((prev) =>
        prev.map((s, i) =>
          i === index ? { ...s, results: [], isSearching: false, hasRun: true } : s
        )
      );
    }
  };

  const removeSearch = (id: number) => {
    setSearches((prev) => prev.filter((s) => s.id !== id));
  };

  const toggleFavoriteLink = (doc: ApplicationDoc, drugName: string) => {
    if (!doc.url) return;
    const exists = favoriteLinks.some((l) => l.url === doc.url);
    const updated = exists
      ? favoriteLinks.filter((l) => l.url !== doc.url)
      : [
          ...favoriteLinks,
          {
            url: doc.url!,
            title: doc.title || doc.type || "Document",
            date: formatDate(doc.date),
            name: drugName,
          },
        ];
    setFavoriteLinks(updated);
  };

  const toggleDocSelected = (url: string, checked: boolean) => {
    setSelectedDocs((prev) => ({ ...prev, [url]: checked }));
  };

  const toggleSelectAll = (searchIndex: number, checked: boolean) => {
    const urls: string[] = [];
    searches[searchIndex].results.forEach((r) => {
      r.submissions?.forEach((s) => {
        s.application_docs?.forEach((d) => {
          if (d.url) urls.push(d.url);
        });
      });
    });
    setSelectedDocs((prev) => {
      const updated = { ...prev };
      urls.forEach((u) => {
        updated[u] = checked;
      });
      return updated;
    });
  };

  const selectLatestDocs = (searchIndex: number) => {
    const allUrls: string[] = [];
    const latestUrls: string[] = [];
    searches[searchIndex].results.forEach((r) => {
      const latest: Record<string, { url?: string; date?: string }> = {};
      r.submissions?.forEach((s) => {
        s.application_docs?.forEach((d) => {
          if (d.url) {
            allUrls.push(d.url);
            const t = d.type || "";
            const existing = latest[t];
            if (!existing || ((d.date || "") > (existing.date || ""))) {
              latest[t] = { url: d.url, date: d.date };
            }
          }
        });
      });
      Object.values(latest).forEach((ld) => {
        if (ld.url) latestUrls.push(ld.url);
      });
    });
    setSelectedDocs((prev) => {
      const updated = { ...prev };
      allUrls.forEach((u) => {
        updated[u] = false;
      });
      latestUrls.forEach((u) => {
        updated[u] = true;
      });
      return updated;
    });
  };

  const startDetailSearch = (value: string, field: string) => {
    setSearches(prev => {
      const newItem: SearchItem = {
        id: Date.now(),
        query: value,
        field,
        results: [],
        sort: "date_desc",
        isSearching: false,
        hasRun: false,
        startYear: "",
        endYear: "",
      };
      const newIndex = prev.length;
      setTimeout(() => runSearch(newIndex, value, field), 0);
      return [...prev, newItem];
    });
  };

  const saveCurrentSearch = (idx: number) => {
    const s = searches[idx];
    if (!s.query.trim()) return;
    setFavoriteSearches([...favoriteSearches, { query: s.query, field: s.field }]);
  };

  function formatDate(d?: string) {
    if (!d) return undefined;
    return /^\d{8}$/.test(d) ? `${d.slice(0,4)}-${d.slice(4,6)}-${d.slice(6)}` : d;
  }

  function formatLabel(label: string) {
    // Check if the label already has a section number format (like "3 DOSAGE FORMS AND STRENGTHS")
    const hasSectionNumberFormat = /^\d+\s+[A-Z\s]+$/.test(label);
    
    if (hasSectionNumberFormat) {
      // Already formatted with section number, return as is
      return label;
    }
    
    // Check if this is a header with ## prefix
    if (label.startsWith('##')) {
      // Return without additional formatting
      return label;
    }
    
    // Basic formatting for simple field names
    return label
      .replace(/_/g, " ")
      .replace(/\b\w/g, (c) => c.toUpperCase());
  }

  const fetchChat = async (prompt: string) => {
    const res = await fetch("/api/chat", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ messages: [{ role: "user", content: prompt }] }),
    });
    if (!res.ok) throw new Error("ChatGPT request failed");
    const data = await res.json();
    return data.content as string;
  };

  const parseBullets = (text: string) =>
    text
      .split(/\n+/)
      .map((l) => l.replace(/^[-•*\d.\s]+/, "").trim())
      .filter(Boolean);

  const styleHtmlTables = (html: string) =>
    html
      .replace(
        /<table/gi,
        '<table style="border-collapse:collapse;width:100%;"',
      )
      .replace(
        /<th/gi,
        '<th style="border:1px solid #ccc;padding:4px;text-align:left;"',
      )
      .replace(
        /<td/gi,
        '<td style="border:1px solid #ccc;padding:4px;text-align:center;"',
      );

  const renderSearchValues = (values: string[], field: string) => {
    return values.map((v, i) => (
      <React.Fragment key={i}>
        {i > 0 && ", "}
        <button
          type="button"
          onClick={() => startDetailSearch(v, field)}
          className="underline text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
        >
          {v}
        </button>
      </React.Fragment>
    ));
  };

  const downloadSelected = async (searchIndex: number) => {
    const urls: string[] = [];
    searches[searchIndex].results.forEach((r) => {
      r.submissions?.forEach((s) => {
        s.application_docs?.forEach((d) => {
          if (d.url && selectedDocs[d.url]) urls.push(d.url);
        });
      });
    });
    for (const url of urls) {
      try {
        const res = await fetch(`/api/drugs-docs?download=1&url=${encodeURIComponent(url)}`);
        if (!res.ok) continue;
        const blob = await res.blob();
        const a = document.createElement('a');
        a.href = URL.createObjectURL(blob);
        a.download = url.split('/').pop() || 'document';
        document.body.appendChild(a);
        a.click();
        URL.revokeObjectURL(a.href);
        document.body.removeChild(a);
      } catch (err) {
        console.error('Download failed', err);
      }
    }
  };

  const saveCurrentPage = (doc: { url: string; title: string }) => {
    const page = pageInputs[doc.url] || 1;
    setSavedPages((prev) => [
      ...prev,
      {
        id: `${Date.now()}-${Math.random().toString(36).slice(2, 9)}`,
        url: doc.url,
        title: doc.title,
        page,
      },
    ]);
  };

  const downloadSavedPages = async () => {
    if (savedPages.length === 0) return;
    try {
      const res = await fetch("/api/combine-pdf-pages", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          selections: savedPages.map((p) => ({
            url: p.url,
            page: p.page,
          })),
        }),
      });
      if (!res.ok) {
        console.error("Combine request failed", res.status);
        return;
      }
      const blob = await res.blob();
      const a = document.createElement("a");
      a.href = URL.createObjectURL(blob);
      a.download = "saved_pages.pdf";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(a.href);
    } catch (err) {
      console.error("Failed to download combined PDF", err);
    }
  };

  const fetchLatestLabel = async (drug: string) => {
    try {
      const res = await fetch(`/api/drug-labels?drug=${encodeURIComponent(drug)}`);
      if (!res.ok) return null;
      const data = await res.json();
      return data.label;
    } catch (e) {
      console.error("label fetch failed", e);
      return null;
    }
  };

  const addComparisonLabel = (labelObj: { drug: string; label: any }) => {
    setComparisonLabels((prev) => {
      if (prev.some((l) => l.drug === labelObj.drug)) return prev;
      return [...prev, labelObj];
    });
  };

  const removeComparisonLabel = (drug: string) => {
    setComparisonLabels((prev) => {
      const updated = prev.filter((l) => l.drug !== drug);
      if (updated.length === 0 && !labelModal) {
        setLabelsOpen(false);
      }
      return updated;
    });
  };

  const exportLabelCSV = (labels: { drug: string; label: any }[], fields: string[]) => {
    let csv = "Field," + labels.map((l) => l.drug).join(',') + "\n";
    fields.forEach((f) => {
      const row = labels
        .map((l) => {
          const arr = getSectionArray(l.label);
          let val: any;
          if (arr) {
            const sec = arr.find((s: any) => s?.title === f);
            val = sec ? sec.text || sec.content : '';
          } else {
            val = l.label[f];
          }
          if (Array.isArray(val)) val = val.join(' ; ');
          else if (typeof val === 'object') val = JSON.stringify(val);
          const text = String(val ?? '').replace(/\n/g, ' ').replace(/"/g, '""');
          return `"${text}"`;
        })
        .join(',');
      csv += `"${f}",${row}\n`;
    });
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'labels.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  useEffect(() => {
    searches.forEach((search) => {
      search.results.forEach((result) => {
        const name =
          result.openfda?.generic_name?.[0] || result.openfda?.brand_name?.[0];
        if (name && !drugSetIds[name]) {
          fetchLatestLabel(name).then((lbl) => {
            const id = lbl?.set_id || lbl?.spl_set_id;
            if (id) {
              setDrugSetIds((prev) => ({ ...prev, [name]: id }));
            }
          });
        }
      });
    });
  }, [searches]);

  const summarizeLabelComparison = async () => {
    if (labelsToShow.length < 2 || selectedFields.length === 0) return;
    setLoadingLabelSummary(true);
    const text = labelsToShow
      .slice(0, 3)
      .map((l) => {
        const lines = selectedFields
          .map((f) => {
            const arr = getSectionArray(l.label);
            let val: any;
            if (arr) {
              const sec = arr.find((s: any) => s?.title === f);
              val = sec ? sec.text || sec.content : '';
            } else {
              val = l.label ? l.label[f] : '';
            }
            if (Array.isArray(val)) val = val.join(' ; ');
            else if (typeof val === 'object') val = JSON.stringify(val);
            return `${formatLabel(f)}: ${String(val ?? '').replace(/\n/g, ' ')}`;
          })
          .join('\n');
        return `Drug: ${l.drug}\n${lines}`;
      })
      .join('\n\n');
    const prompt = `Compare these drug labels focusing on the selected fields and summarize the key similarities and differences in up to five \u2022 bullet points without numbers.\n\n${text}`;
    try {
      const resp = await fetchChat(prompt);
      setLabelComparisonSummary(resp);
    } catch (e) {
      console.error('label comparison summary failed', e);
    } finally {
      setLoadingLabelSummary(false);
    }
  };

  const chartData = useMemo(() => {
    const yearCounts: Record<string, Record<number, number>> = {};

    searches.forEach((search, sIdx) => {
      search.results.forEach((r) => {
        r.submissions?.forEach((s) => {
          s.application_docs?.forEach((d) => {
            if (d.date) {
              const year = d.date.substring(0, 4);
              if (!yearCounts[year]) yearCounts[year] = {};
              yearCounts[year][sIdx] = (yearCounts[year][sIdx] || 0) + 1;
            }
          });
        });
      });
    });

    return Object.entries(yearCounts)
      .map(([year, counts]) => ({ year, ...counts }))
      .sort((a, b) => parseInt(a.year) - parseInt(b.year));
  }, [searches]);

  const searchColors = [
    "#f59e0b",
    "#ef4444",
    "#3b82f6",
    "#10b981",
    "#8b5cf6",
    "#a855f7",
    "#f43f5e",
    "#0ea5e9",
  ];

  // Add a custom tooltip component for the chart
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (!active || !payload || !payload.length) return null;

    return (
      <div className="bg-white dark:bg-gray-800 p-2 border rounded shadow-lg">
        <p className="font-bold">{label}</p>
        {payload.map((p: any, idx: number) => {
          const sIdx = parseInt(p.dataKey, 10);
          const search = searches[sIdx];
          if (!search) return null;
          const docs: ApplicationDoc[] = [];
          search.results.forEach((r) => {
            r.submissions?.forEach((s) => {
              s.application_docs?.forEach((d) => {
                if (d.date?.startsWith(label)) docs.push(d);
              });
            });
          });
          return (
            <div key={sIdx} className="mt-2">
              <p className="font-medium">
                {search.query || `Search ${sIdx + 1}`} ({docs.length})
              </p>
              <ul className="text-xs">
                {docs.slice(0, 5).map((doc, i) => (
                  <li key={i}>
                    {doc.url ? (
                      <a
                        href={doc.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300 underline"
                      >
                        {doc.title || doc.type || "Document"}
                      </a>
                    ) : (
                      doc.title || doc.type || "Document"
                    )}
                  </li>
                ))}
                {docs.length > 5 && <li>...and {docs.length - 5} more</li>}
              </ul>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <>
    {selectionPopup && (
      <SelectionPopup
        className="fixed"
        data={selectionPopup}
        reference={selectionRef}
        onReferenceChange={setSelectionRef}
        onSave={saveSelection}
        onCancel={closeSelectionPopup}
      />
    )}
    <div className="container mx-auto py-8 px-4">
      <SubpageHeader current="drug-docs" />

      <Button
        variant="link"
        size="sm"
        className="mb-2 px-0"
        onClick={() => setShowStorageUsage((s) => !s)}
      >
        {showStorageUsage ? "Hide storage usage" : "Show storage usage"}
      </Button>
      {showStorageUsage && (
        <p className="text-sm text-muted-foreground mb-4 flex items-center gap-2">
          <span>
            Local storage used: {(storageUsage / 1024).toFixed(1)} KB
          </span>
          <button
            onClick={clearLocalStorage}
            aria-label="Clear saved data"
            className="text-muted-foreground hover:text-red-500"
          >
            <Trash2 className="h-4 w-4" />
          </button>
          {storageCleared && (
            <span className="text-green-600">Cleared!</span>
          )}
        </p>
      )}

      <h1 className="text-3xl font-bold mb-6">FDA Drug Document Finder</h1>
      <div className="prose max-w-none mb-8 dark:text-gray-200">
        <p>
          Search Drugs@FDA for application review documents such as
          approval letters, labels, and medical reviews.
        </p>
      </div>

      <div className="space-y-4 mb-8">
        {searches.map((s, idx) => (
          <form
            key={s.id}
            onSubmit={(e) => {
              e.preventDefault();
              runSearch(idx);
            }}
            className="flex flex-wrap gap-2 items-center"
          >
            <Input
              type="search"
              placeholder="Enter drug name..."
              value={s.query}
              onChange={(e) =>
                setSearches((prev) =>
                  prev.map((p, i) => (i === idx ? { ...p, query: e.target.value } : p))
                )
              }
              className="flex-1 min-w-[200px]"
            />
            <Select
              value={s.field}
              onValueChange={(val) =>
                setSearches((prev) => prev.map((p, i) => (i === idx ? { ...p, field: val } : p)))
              }
            >
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Field" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="brand">Brand</SelectItem>
                <SelectItem value="generic">Generic</SelectItem>
                <SelectItem value="application">Application</SelectItem>
                <SelectItem value="manufacturer">Manufacturer</SelectItem>
              </SelectContent>
            </Select>
            <Input
              type="number"
              placeholder="Start Year"
              min={1900}
              max={2100}
              value={s.startYear ?? ""}
              onChange={(e) =>
                setSearches((prev) =>
                  prev.map((p, i) => (i === idx ? { ...p, startYear: e.target.value } : p))
                )
              }
              className="w-28"
            />
            <Input
              type="number"
              placeholder="End Year"
              min={1900}
              max={2100}
              value={s.endYear ?? ""}
              onChange={(e) =>
                setSearches((prev) =>
                  prev.map((p, i) => (i === idx ? { ...p, endYear: e.target.value } : p))
                )
              }
              className="w-28"
            />
            <Button
              type="submit"
              className="bg-amber-400 text-black hover:bg-amber-500"
              disabled={s.isSearching}
            >
              {s.isSearching ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Search className="h-4 w-4 mr-2" />
              )}
              Search
            </Button>
            <Button
              type="button"
              onClick={() => saveCurrentSearch(idx)}
              variant="outline"
              className="flex items-center gap-1"
            >
              <Save className="h-4 w-4" /> Save
            </Button>
            <Button type="button" variant="ghost" onClick={() => removeSearch(s.id)}>
              <X className="h-4 w-4" />
            </Button>
          </form>
        ))}
        <Button
          variant="outline"
          onClick={() =>
            setSearches((prev) => [
              ...prev,
              { id: Date.now(), query: "", field: "brand", results: [], sort: "date_desc", isSearching: false, hasRun: false, startYear: "", endYear: "" },
            ])
          }
        >
          <Plus className="h-4 w-4 mr-2" /> Add Search
        </Button>
      </div>

      {chartData.length > 0 && (
        <div className="mb-8 h-64">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={chartData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="year" />
              <YAxis allowDecimals={false} />
              <ReTooltip content={<CustomTooltip />} wrapperStyle={{ pointerEvents: 'auto' }} />
              <Legend />
              {searches.map((s, idx) =>
                s.hasRun ? (
                  <Bar
                    key={s.id}
                    dataKey={String(idx)}
                    fill={searchColors[idx % searchColors.length]}
                    name={s.query || `Search ${idx + 1}`}
                  />
                ) : null
              )}
            </BarChart>
          </ResponsiveContainer>
        </div>
      )}


      {favoriteSearches.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-bold mb-2">Saved Searches</h2>
          <div className="flex flex-wrap gap-2">
            {favoriteSearches.map((fs, idx) => (
              <div key={idx} className="flex items-center gap-1 border rounded px-2 py-0">
                <Button
                  variant="link"
                  className="p-0 text-sm"
                  onClick={() => {
                    // Create a new search with the saved query
                    const emptyIndex = searches.findIndex((s) => !s.query.trim());
                    if (emptyIndex !== -1) {
                      setSearches((prev) =>
                        prev.map((p, i) =>
                          i === emptyIndex ? { ...p, query: fs.query, field: fs.field } : p
                        )
                      );
                      runSearch(emptyIndex, fs.query, fs.field);
                    } else {
                      setSearches((prev) => [
                        ...prev,
                        {
                          id: Date.now(),
                          query: fs.query,
                          field: fs.field,
                          results: [],
                          sort: 'date_desc',
                          isSearching: false,
                          hasRun: false,
                        },
                      ]);
                      runSearch(searches.length, fs.query, fs.field);
                    }
                  }}
                >
                  {fs.query} ({fs.field})
                </Button>
                <button
                  onClick={() => {
                    setFavoriteSearches(favoriteSearches.filter((_, i) => i !== idx));
                  }}
                  className="text-muted-foreground hover:text-red-500"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {favoriteLinks.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-bold mb-2">Saved Documents</h2>
          <div className="flex flex-wrap gap-2">
            {favoriteLinks.map((fl, idx) => (
              <div key={idx} className="flex items-center gap-1 border rounded px-2 py-0.5">
                <a
                  href={fl.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                >
                  {fl.title}
                </a>
                {fl.date && <span className="text-sm text-muted-foreground ml-1">{fl.date}</span>}
                {fl.name && <span className="text-sm text-muted-foreground ml-1">{fl.name}</span>}
                <button
                  onClick={() => setFavoriteLinks(favoriteLinks.filter((_, i) => i !== idx))}
                  className="text-muted-foreground hover:text-red-500"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Update the results display to show side by side and take full width */}
      <div className={`grid ${resultsGridCols} gap-4 mt-8`}>
        {searches.map((search, sidx) => (
          <div
            key={search.id}
            className={`space-y-4 w-full mb-6 ${searches.length === 1 ? 'max-w-3xl mx-auto' : ''}`}
          >
            {search.results.length > 0 && (
              <>
                {/* First row with truncated title */}
                <div className="w-full">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <h3 className="font-bold text-lg truncate" title={search.query}>
                          {search.query}
                        </h3>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{search.query}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>

                {/* Row with controls and external search links */}
                <div className="flex flex-wrap items-center justify-between gap-2 w-full">
                  <div className="flex flex-wrap items-center gap-2">
                  <Select
                    value={search.sort}
                    onValueChange={(val) =>
                      setSearches((prev) => prev.map((p, i) => (i === sidx ? { ...p, sort: val } : p)))
                    }
                  >
                    <SelectTrigger className="w-[80px] px-2 h-9">
                      <SelectValue placeholder="Sort" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="date_desc">Date ↓</SelectItem>
                      <SelectItem value="date_asc">Date ↑</SelectItem>
                      {/* <SelectItem value="type">Type</SelectItem> */}
                    </SelectContent>
                  </Select>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        size="sm"
                        variant="outline"
                        className="flex items-center gap-1 whitespace-nowrap px-2 h-9"
                      >
                        Select <ChevronDown className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start">
                      <DropdownMenuItem onSelect={() => toggleSelectAll(sidx, true)}>
                        Select All
                      </DropdownMenuItem>
                      <DropdownMenuItem onSelect={() => selectLatestDocs(sidx)}>
                        Select Latest
                      </DropdownMenuItem>
                      <DropdownMenuItem onSelect={() => toggleSelectAll(sidx, false)}>
                        Clear Selection
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => downloadSelected(sidx)}
                    className="flex items-center gap-1 whitespace-nowrap px-2 h-9"
                  >
                    <Download className="h-4 w-4" />
                    <span className="hidden min-[500px]:inline sm:hidden">Download</span>
                    <span className="hidden sm:inline">Download Selected</span>
                  </Button>
                  </div>
                  <div className="flex items-center gap-2">
                  <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <a
                            href={`https://vsearch.nlm.nih.gov/vivisimo/cgi-bin/query-meta?v%3Aproject=medlineplus&v%3Asources=medlineplus-bundle&query=${encodeURIComponent(search.query)}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex"
                            // style={{ marginRight: '-6px'}}
                          >
                            <Image
                              src={MedlinePlusLogo}
                              alt="MedlinePlus"
                              width={30}
                              height={30}
                            />
                          </a>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Search MedlinePlus</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>

                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <a
                            href={`https://pubmed.ncbi.nlm.nih.gov/?term=${encodeURIComponent(search.query)}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex"
                            style = {{ marginRight: '4px'}}
                          >
                            <Image
                              src={theme === "dark" ? PubMedLogoDark : PubMedLogo}
                              alt="PubMed"
                              width={20}
                              height={20}
                            />
                          </a>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Search PubMed</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>

                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <a
                            href={`https://clinicaltrials.gov/search?term=${encodeURIComponent(search.query)}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex"
                            style = {{ marginRight: '4px'}}
                          >
                            <Image
                              src={CtGovLogo}
                              alt="ClinicalTrials.gov"
                              width={20}
                              height={20}
                            />
                          </a>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Search ClinicalTrials.gov</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>

                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <a
                            href={`https://dailymed.nlm.nih.gov/dailymed/search.cfm?query=${encodeURIComponent(search.query)}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex"
                          >
                            <Image
                              src={DailyMedLogo}
                              alt="DailyMed"
                              width={30}
                              height={30}
                            />
                          </a>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Search DailyMed</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </div>
              </>
            )}
            {search.results.map((item, idx) => {
              const latestDocs: Record<string, { url?: string; date?: string }> = {};
              item.submissions?.forEach((sub) => {
                sub.application_docs?.forEach((d) => {
                  const t = d.type || "";
                  const existing = latestDocs[t];
                  if (!existing || ((d.date || "") > (existing.date || ""))) {
                    latestDocs[t] = { url: d.url, date: d.date };
                  }
                });
              });
              const cardTitle = item.openfda?.brand_name?.[0] || item.openfda?.generic_name?.[0] || "Unknown";
              return (
              <div data-query={search.query} key={`${sidx}-${idx}`}>
              <Card className="relative">
                <div className="absolute top-2 right-2 flex items-center gap-2 z-10">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 px-2"
                    onClick={async () => {
                      const name =
                        item.openfda?.generic_name?.[0] || item.openfda?.brand_name?.[0];
                      if (!name) return;
                      setLabelModal({ drug: name, label: null });
                      setLabelsOpen(true);
                      const lbl = await fetchLatestLabel(name);
                      setLabelModal({ drug: name, label: lbl });
                    }}
                  >
                    <FileText className="h-4 w-4 mr-0" /> Label
                  </Button>
                  <Button
                    size="icon"
                    variant="ghost"
                    className="h-6 w-6 p-0"
                    onClick={() => {
                      setSearches(prev =>
                        prev.map((s, i) =>
                          i === sidx ? { ...s, results: s.results.filter((_, ri) => ri !== idx) } : s
                        )
                      );
                    }}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                <CardHeader>
                  <div className="flex items-center gap-1">
                    <CardTitle className="text-lg truncate mr-1" title={cardTitle}>
                      {cardTitle}
                    </CardTitle>
                    {(() => {
                      const name =
                        item.openfda?.generic_name?.[0] || item.openfda?.brand_name?.[0];
                      const sid = name ? drugSetIds[name] : undefined;
                      return sid ? (
                        <a
                          href={`https://dailymed.nlm.nih.gov/dailymed/fda/fdaDrugXsl.cfm?setid=${sid}&type=display`}
                          target="_blank"
                          rel="noopener noreferrer"
                        >                          
                          <img src={FdaIcon} alt="FDA" width={20} height={20} />
                        </a>
                      ) : null;
                    })()}
                  </div>
                </CardHeader>
              <CardContent>
                  {item.openfda?.application_number && (
                    <p className="text-sm mb-2">Application: {item.openfda.application_number.join(", ")}</p>
                  )}
                  {item.sponsor_name && (
                    <p className="text-sm mb-2">
                      Sponsor: {" "}
                      <button
                        type="button"
                        onClick={() => startDetailSearch(item.sponsor_name!, "sponsor_name")}
                        className="underline text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                      >
                        {item.sponsor_name}
                      </button>
                    </p>
                  )}
                  {item.openfda?.route && (
                    <p className="text-sm mb-2">
                      Route: {renderSearchValues(item.openfda.route, "route")}
                    </p>
                  )}
                  {item.openfda?.brand_name && (
                    <p className="text-sm mb-2">
                      Brand: {renderSearchValues(item.openfda.brand_name, "brand")}
                    </p>
                  )}
                  {item.openfda?.generic_name && (
                    <p className="text-sm mb-2">
                      Generic: {renderSearchValues(item.openfda.generic_name, "generic")}
                    </p>
                  )}
                  {item.openfda?.pharm_class_moa && (
                    <p className="text-sm mb-2">
                      MOA: {renderSearchValues(item.openfda.pharm_class_moa, "pharm_class_moa")}
                    </p>
                  )}
                  {expandedResults[`${sidx}-${idx}`] && item.openfda && (
                    <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mb-2">
                      <ul className="space-y-1">
                        {Object.entries(item.openfda)
                          .filter(([k]) =>
                            ![
                              "application_number",
                              "route",
                              "brand_name",
                              "generic_name",
                              "pharm_class_moa",
                            ].includes(k)
                          )
                          .map(([k, v]) => (
                            <li key={k}>
                              <span className="font-semibold">{formatLabel(k)}:</span>{" "}
                              {Array.isArray(v)
                                ? renderSearchValues(v as string[], k)
                                : (
                                    <button
                                      type="button"
                                      onClick={() => startDetailSearch(String(v), k)}
                                      className="underline text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                                    >
                                      {String(v)}
                                    </button>
                                  )}
                            </li>
                          ))}
                      </ul>
                    </div>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="mb-2"
                    onClick={() => setExpandedResults(prev => ({ ...prev, [`${sidx}-${idx}`]: !prev[`${sidx}-${idx}`] }))}
                  >
                    {expandedResults[`${sidx}-${idx}`] ? 'Hide Details' : 'Show Details'}
                  </Button>
                  {item.submissions?.map((sub, subIdx) => {
                    const docs = [...(sub.application_docs || [])];
                    if (search.sort === "date_desc") {
                      docs.sort((a, b) => (b.date || "").localeCompare(a.date || ""));
                    } else if (search.sort === "date_asc") {
                      docs.sort((a, b) => (a.date || "").localeCompare(b.date || ""));
                    } else if (search.sort === "type") {
                      docs.sort((a, b) => (a.type || "").localeCompare(b.type || ""));
                    }
                    return (
                      <div key={subIdx} className="mb-4">
                        <p className="font-medium mb-2">
                          Submission {sub.submission_number} {sub.submission_type ? `- ${sub.submission_type}` : ""}
                        </p>
                        {docs.length ? (
                          <ul className="list-disc list-inside space-y-1">
                            {docs.map((doc, didx) => (
                              <li key={didx} className="flex items-center gap-2">
                                {doc.url && (
                                  <Checkbox
                                    checked={!!selectedDocs[doc.url]}
                                    onCheckedChange={(checked) =>
                                      toggleDocSelected(doc.url!, !!checked)
                                    }
                                  />
                                )}
                                <span>
                                  {doc.date ? `${formatDate(doc.date)}: ` : ""}
                                  {doc.url ? (
                                    <a
                                      href={doc.url}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300 underline"
                                    >
                                      {doc.title || doc.type || doc.id || "Document"}
                                    </a>
                                  ) : (
                                    doc.title || doc.type || doc.id || "Document"
                                  )}
                                </span>
                                {doc.type && latestDocs[doc.type]?.url === doc.url && (
                                  <Star className="h-4 w-4 -mr-1 text-yellow-500" />
                                )}
                                {doc.url && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 px-1"
                                    onClick={() => toggleFavoriteLink(doc, item.openfda?.brand_name?.[0] || item.openfda?.generic_name?.[0] || "Unknown")}
                                  >
                                    {favoriteLinks.some((l) => l.url === doc.url) ? (
                                      <BookmarkCheck className="h-4 w-4 text-amber-500" />
                                    ) : (
                                      <Bookmark className="h-4 w-4" />
                                    )}
                                  </Button>
                                )}
                                {doc.url && /\.pdf(\?|$)/i.test(doc.url) && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 px-1 -ml-2"
                                    onClick={() => setPdfModal({ url: doc.url!, title: doc.title || doc.type || doc.id || "Document" })}
                                  >
                                    <Eye className="h-4 w-4" />
                                  </Button>
                                )}
                              </li>
                            ))}
                          </ul>
                        ) : (
                          <p className="text-sm text-muted-foreground">No documents available.</p>
                        )}
                      </div>
                    );
                  })}
                </CardContent>
              </Card>
              </div>);
            })}
          </div>
        ))}
      </div>
    </div>
    <Dialog open={!!pdfModal} onOpenChange={(open) => !open && setPdfModal(null)}>
      <DialogContent
        className={cn(
          "max-w-screen-2xl max-h-[95vh] w-full",
          pdfOnTop ? "z-[100001]" : "z-40"
        )}
        overlayClassName={pdfOnTop ? "z-[100000]" : "z-30"}
      >
        <DialogHeader>
          <DialogTitle>PDF Comparison</DialogTitle>
        </DialogHeader>
        <div className={`h-[80vh] grid ${gridCols} gap-4`}>
          {docsToShow.map((doc, idx) => (
            <PagePdfViewer
              key={idx}
              url={doc.url}
              title={doc.title}
              pageInput={pageInputs[doc.url] ?? 1}
              onPageChange={(val) =>
                setPageInputs((p) => ({
                  ...p,
                  [doc.url]: val,
                }))
              }
              onSave={() => saveCurrentPage(doc)}
              onClose={() => {
                const isComparison = comparisonDocs.some((d) => d.url === doc.url);
                if (isComparison) {
                  removeComparisonDoc(doc.url);
                  if (docsToShow.length === 1) setPdfModal(null);
                } else {
                  setPdfModal(null);
                }
              }}
            />
          ))}
        </div>
        <div className="mt-4 flex items-start justify-between gap-4 flex-wrap">
          {savedPages.length > 0 && (
            <div className="flex items-center gap-2 overflow-x-auto flex-1">
              {savedPages.map((p) => (
                <span
                  key={p.id}
                  className="px-2 py-1 text-sm border rounded whitespace-nowrap"
                >
                  {p.title} (p{p.page})
                </span>
              ))}
              <Button size="sm" variant="ghost" onClick={() => setSavedPages([])}>
                Reset
              </Button>
              <Button
                size="sm"
                onClick={downloadSavedPages}
                className="bg-amber-400 text-black hover:bg-amber-500 whitespace-nowrap"
              >
                Download Saved Pages
              </Button>
            </div>
          )}
          <div className="flex items-center gap-2 ml-auto justify-end">
            {pdfModal && comparisonDocs.length < 2 && (
              <Button
                size="sm"
                onClick={() => {
                  if (pdfModal) {
                    addComparisonDoc(pdfModal.url, pdfModal.title);
                    setPdfModal(null);
                  }
                }}
                className="bg-amber-400 text-black hover:bg-amber-500"
              >
                Add Another Document
              </Button>
            )}
            <Button size="sm" variant="outline" onClick={() => setPdfModal(null)}>
              Close
            </Button>
          </div>
        </div>
    </DialogContent>
  </Dialog>
  <Dialog open={labelsOpen} onOpenChange={(open) => {
    setLabelsOpen(open);
    if (!open) {
      setLabelModal(null);
      setComparisonLabels([]);
    }
  }}>
  <DialogContent className="max-w-screen-xl max-h-[90vh] overflow-auto">
      <div ref={labelTopRef} className="absolute top-0 left-0" />
      {/* Add custom styling to increase space below the close button */}
      <style jsx global>{`
        .label-comparison-dialog [data-radix-dialog-close] {
          margin-top: 0.5rem;
          margin-right: 0.5rem;
        }
      `}</style>
      <DialogHeader className="pt-8 sm:pt-6"> {/* Add padding to the top of the header */}
        <div className="flex items-start justify-between gap-4">
          <DialogTitle>Label Comparison</DialogTitle>
          <div className="flex flex-wrap items-center gap-2 ml-auto">
            {labelModal && (
              <Button
                size="sm"
                onClick={() => {
                  if (labelModal && labelModal.label) {
                    addComparisonLabel(labelModal as { drug: string; label: any });
                    setLabelModal(null);
                    setLabelsOpen(false);
                  }
                }}
                className="bg-amber-400 text-black hover:bg-amber-500"
              >
                Add Another Label
              </Button>
            )}
            <Button
              size="sm"
              variant="outline"
              onClick={summarizeLabelComparison}
              disabled={loadingLabelSummary}
              className="gap-1"
            >
              {loadingLabelSummary ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" /> Summarizing...
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4" /> Summarize
                </>
              )}
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => exportLabelCSV(labelsToShow, selectedFields)}
            >
              Export CSV
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={scrollToLabelBottom}
              className="gap-1"
            >
              <ArrowDown className="h-4 w-4" /> Jump to Bottom
            </Button>
          </div>
        </div>
      </DialogHeader>
      <div className="mb-4 flex items-center gap-2">
        <Button
          size="sm"
          variant="outline"
          onClick={() =>
            setSelectedFields(
              selectedFields.length === allLabelFields.length
                ? []
                : allLabelFields
            )
          }
        >
          {selectedFields.length === allLabelFields.length
            ? "Deselect All"
            : "Select All"}
        </Button>
      </div>
      {labelModal && labelModal.label === null ? (
        <p>Loading...</p>
      ) : (
        <>
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 mb-4">
            {allLabelFields.map((f) => (
              <label key={f} className="flex items-center gap-1 text-sm">
                <Checkbox
                  checked={selectedFields.includes(f)}
                  onCheckedChange={(checked) =>
                    setSelectedFields((prev) =>
                      checked ? [...prev, f] : prev.filter((x) => x !== f)
                    )
                  }
                />
                {formatLabel(f)}
              </label>
            ))}
          </div>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-32 align-top">Field</TableHead>
                {labelsToShow.map((l, idx) => (
                  <TableHead key={idx} className="whitespace-nowrap align-top">
                    {l.drug}
                    {labelsToShow.length > 1 && (
                      <Button
                        size="icon"
                        variant="ghost"
                        className="ml-1 h-4 w-4"
                        onClick={() => {
                          if (labelModal && labelModal.drug === l.drug) {
                            setLabelModal(null);
                            setLabelsOpen(comparisonLabels.length > 0);
                          } else {
                            removeComparisonLabel(l.drug);
                            if (comparisonLabels.filter((c) => c.drug !== l.drug).length === 0 && !labelModal) {
                              setLabelsOpen(false);
                            }
                          }
                        }}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    )}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {selectedFields.map((field) => (
                <TableRow key={field}>
                  <TableCell className="font-medium w-32 align-top">{formatLabel(field)}</TableCell>
                  {labelsToShow.map((l, idx) => (
                    <TableCell key={idx} className="prose max-w-md align-top">
                      {(() => {
                        const arr = getSectionArray(l.label);
                        let val: any;
                        if (arr) {
                          const sec = arr.find((s: any) => s?.title === field);
                          val = sec ? sec.text || sec.content : '';
                        } else {
                          val = l.label ? l.label[field] : '';
                        }
                        if (Array.isArray(val)) val = val.join('<br/>');
                        if (typeof val === 'object') return JSON.stringify(val);
                        if (typeof val === 'string') {
                          const normalizedField = field
                            .toLowerCase()
                            .replace(/[\s_-]/g, '');
                          if (
                            normalizedField === 'setid' ||
                            normalizedField === 'splsetid'
                          ) {
                            return (
                              <a
                                href={`https://dailymed.nlm.nih.gov/dailymed/fda/fdaDrugXsl.cfm?setid=${val}&type=display`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="underline text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                              >
                                {val}
                              </a>
                            );
                          }
                        }
                        if (typeof val === 'string' && /<[^>]+>/.test(val)) {
                          return (
                            <div
                              dangerouslySetInnerHTML={{
                                __html: styleHtmlTables(linkClinicalTrials(val) as string),
                              }}
                            />
                          );
                        }
                        if (typeof val === 'string') {
                          return <>{linkClinicalTrials(val)}</>;
                        }
                        return String(val ?? '');
                      })()}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
          {labelComparisonSummary && (
            <div className="mt-4 p-2 rounded-md bg-blue-50 dark:bg-blue-900/40 relative">
              <button
                onClick={() => setLabelComparisonSummary("")}
                className="absolute top-1 right-1 text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-800 rounded-full p-1"
                aria-label="Close comparison summary"
              >
                <X className="h-3 w-3" />
              </button>
              <p className="font-semibold text-blue-700 dark:text-blue-300 flex items-center gap-1 mb-1">
                <Sparkles className="h-4 w-4" /> Comparison Summary
              </p>
              <ul className="list-[circle] pl-4 text-sm text-blue-700 dark:text-blue-300">
                {parseBullets(labelComparisonSummary).map((b, i) => (
                  <li key={i}>{linkClinicalTrials(b)}</li>
                ))}
              </ul>
            </div>
          )}
          <div className="mt-4 flex items-center gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={scrollToLabelTop}
              className="gap-1"
            >
              <ArrowUp className="h-4 w-4" /> Jump to Top
            </Button>
          </div>
          <div ref={labelBottomRef} />
        </>
      )}
    </DialogContent>
  </Dialog>
  </>
  );
}

export default function DrugDocsPage() {
  const [top, setTop] = useState<'clipboard' | 'pdf' | null>(null)

  return (
    <ClipboardProvider>
      <DrugDocsContent bringPdfToFront={() => setTop('pdf')} pdfOnTop={top === 'pdf'} />
      <ClipboardPanel
        zIndex={top === 'clipboard' ? 100001 : 99999}
        onFocus={() => setTop('clipboard')}
      />
    </ClipboardProvider>
  )
}

