"use client";

import Image from "next/image";
import { useTheme } from "@/contexts/ThemeContext";
import EvicenterLogo from "@/icons/Evicenter.svg?url";
import EvicenterWhiteLogo from "@/icons/evicenter-white.svg?url";

export default function Loading() {
  const { theme } = useTheme();
  
  return (
    <div className="flex min-h-screen relative">
      <div className="flex-1 overflow-hidden">
        <main className="flex flex-col h-screen">
          <div className="sticky top-0 z-20 bg-background border-b border-gray-100 dark:border-gray-800 px-4 pb-2">
            <div className="flex justify-between items-center mb-0">
              <div className="flex-shrink-0 py-2">
                <Image
                  src={theme === 'dark' ? EvicenterWhiteLogo : EvicenterLogo}
                  alt="Evicenter Logo"
                  width={210}
                  height={200}
                  className="h-auto"
                  priority
                />
              </div>
            </div>
          </div>
          
          <div className="flex-1 flex items-center justify-center">
            <div className="animate-pulse text-lg text-muted-foreground">
              Loading...
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
