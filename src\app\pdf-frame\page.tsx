"use client";

import { useSearchParams } from 'next/navigation';
import PdfViewerConfig from '@/components/PdfViewerConfig';
import PdfViewer from '@/components/PdfViewer';
import React, { Suspense, useState, useEffect, useMemo } from 'react';
import { ClipboardProvider, useClipboard } from '@/contexts/ClipboardContext';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import SelectionPopup from '@/components/SelectionPopup';

// Extract the component that uses useSearchParams
function PdfFrameContent() {
  const params = useSearchParams();
  const url = params.get('url') || '';
  const page = parseInt(params.get('page') || '1', 10);
  const title = params.get('title') || '';
  const articleUrl = params.get('articleUrl') || '';
  const { addNote } = useClipboard();

  const [popup, setPopup] = useState<
    { text: string; x: number; y: number } | null
  >(null);
  const [reference, setReference] = useState(title);

  const saveSelection = () => {
    if (!popup) return;
    // Use articleUrl if available, otherwise fall back to PDF url
    const sourceUrl = articleUrl || url;
    addNote(popup.text, sourceUrl, undefined, reference || title);
    window.getSelection()?.removeAllRanges();
    setPopup(null);
  };

  const closePopup = () => setPopup(null);

  useEffect(() => {
    const handler = (e: MouseEvent) => {
      const sel = window.getSelection();
      const text = sel?.toString().trim();
      if (text) {
        e.preventDefault();
        e.stopPropagation();
        setReference(title);
        setPopup({ text, x: e.clientX, y: e.clientY });
      }
    };
    document.addEventListener('contextmenu', handler);
    return () => document.removeEventListener('contextmenu', handler);
  }, [title, addNote]);

  if (!url) {
    return <div>No PDF url specified</div>;
  }

  // Proxy external URLs through our API so the viewer can load them.
  // Allow blob: URLs from local uploads to pass through directly.
  const proxied =
    url.startsWith('blob:') || url.startsWith('/api/')
      ? url
      : `/api/drugs-docs?download=1&inline=1&url=${encodeURIComponent(url)}`;
  console.log('Loading PDF from', url);

  const viewer = useMemo(
    () => <PdfViewer url={proxied} initialPage={page} />,
    [proxied, page],
  );

  return (
    <PdfViewerConfig>
      <div className="w-screen h-screen relative">
        {popup && (
          <SelectionPopup
            className="absolute z-[100002]"
            data={popup}
            reference={reference}
            source={articleUrl || url}
            onReferenceChange={setReference}
            onSave={saveSelection}
            onCancel={closePopup}
            onClick={(e) => e.stopPropagation()}
          />
        )}
        {viewer}
      </div>
    </PdfViewerConfig>
  );
}

// Main component that wraps with Suspense
export default function PdfFramePage() {
  return (
    <ClipboardProvider storageKey="pubmedNotes">
      <Suspense fallback={<div>Loading...</div>}>
        <PdfFrameContent />
      </Suspense>
    </ClipboardProvider>
  );
}