"use client";

import { useSearchParams } from 'next/navigation';
import PdfViewerConfig from '@/components/PdfViewerConfig';
import PdfViewer from '@/components/PdfViewer';
import React, { Suspense } from 'react';

// Extract the component that uses useSearchParams
function PdfFrameContent() {
  const params = useSearchParams();
  const url = params.get('url') || '';
  const page = parseInt(params.get('page') || '1', 10);

  if (!url) {
    return <div>No PDF url specified</div>;
  }

  const proxied = `/api/drugs-docs?download=1&inline=1&url=${encodeURIComponent(url)}`;

  return (
    <PdfViewerConfig>
      <div className="w-screen h-screen">
        <PdfViewer url={proxied} initialPage={page} />
      </div>
    </PdfViewerConfig>
  );
}

// Main component that wraps with Suspense
export default function PdfFramePage() {
  return (
    <Suspense fallback={<div className="w-screen h-screen flex items-center justify-center">Loading PDF...</div>}>
      <PdfFrameContent />
    </Suspense>
  );
}