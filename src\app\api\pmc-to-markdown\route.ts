import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';

const execAsync = promisify(exec);

export async function GET(req: NextRequest) {
  const pmid = req.nextUrl.searchParams.get('pmid');
  if (!pmid) {
    return NextResponse.json({ error: 'Missing pmid parameter' }, { status: 400 });
  }
  try {
    const scriptPath = path.join(process.cwd(), 'scripts', 'pmc_to_markdown.py');
    const { stdout, stderr } = await execAsync(`python "${scriptPath}" "${pmid}"`, {
      maxBuffer: 50 * 1024 * 1024,
    });
    if (stderr) {
      console.error('pmc-to-markdown stderr:', stderr.trim());
    }
    if (!stdout) {
      return NextResponse.json({ text: null }, { status: 200 });
    }
    console.log(`pmc-to-markdown converted PMID ${pmid} to markdown (${stdout.length} chars)`);
    return NextResponse.json({ text: stdout }, { status: 200 });
  } catch (err) {
    console.error('pmc-to-markdown error', err);
    return NextResponse.json({ error: 'Failed to convert PMC article' }, { status: 500 });
  }
}
