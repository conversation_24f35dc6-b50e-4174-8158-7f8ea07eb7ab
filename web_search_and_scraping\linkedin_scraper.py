from playwright.sync_api import sync_playwright

def connect_to_existing_chrome():
    with sync_playwright() as p:
        # Connect to running Chrome instance
        browser = p.chromium.connect_over_cdp("http://localhost:9222")
        
        # Get the existing page or create new one
        contexts = browser.contexts
        if contexts:
            page = contexts[0].pages[0]
        else:
            context = browser.new_context()
            page = context.new_page()
        
        # Now you can control your existing Chrome
        page.goto("https://www.linkedin.com")
        
        # Your scraping code here
        print("Connected to existing Chrome!")
        print(f"Current URL: {page.url}")
        
        # Don't close the browser (since it's your existing one)
        input("Press Enter to disconnect...")

if __name__ == "__main__":
    connect_to_existing_chrome()