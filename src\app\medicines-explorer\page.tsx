"use client";

import React, { useState, useMemo } from "react";
import Link from "next/link";
import { <PERSON><PERSON><PERSON><PERSON>, Filter, ExternalLink } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { ThemeToggle } from "@/components/ThemeToggle";
import { ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip as ReTooltip } from "recharts";
import medicinesData from "../../../medicines_output_medicines_en_fixed.json";

interface Medicine {
  [key: string]: any;
  Category: string;
  "Name of medicine": string;
  "Medicine status": string;
  "International non-proprietary name (INN) / common name"?: string;
  "Active substance"?: string;
  "Therapeutic area (MeSH)"?: string;
  "Therapeutic indication"?: string;
  "Marketing authorisation date"?: string;
  "Medicine URL"?: string;
}

export default function MedicinesExplorerPage() {
  const medicines = medicinesData as Medicine[];

  const statuses = useMemo(
    () => Array.from(new Set(medicines.map((m) => m["Medicine status"]))),
    [medicines],
  );
  const categories = useMemo(
    () => Array.from(new Set(medicines.map((m) => m.Category))),
    [medicines],
  );

  const [search, setSearch] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [categoryFilter, setCategoryFilter] = useState("all");

  const filteredMedicines = useMemo(() => {
    return medicines.filter((m) => {
      const searchText = `${m["Name of medicine"]} ${m["International non-proprietary name (INN) / common name"] ?? ""} ${m["Active substance"] ?? ""}`.toLowerCase();
      const matchesSearch = searchText.includes(search.toLowerCase());
      const matchesStatus = statusFilter === "all" || m["Medicine status"] === statusFilter;
      const matchesCategory = categoryFilter === "all" || m.Category === categoryFilter;
      return matchesSearch && matchesStatus && matchesCategory;
    });
  }, [medicines, search, statusFilter, categoryFilter]);

  const statusChartData = useMemo(() => {
    const counts: Record<string, number> = {};
    filteredMedicines.forEach((m) => {
      counts[m["Medicine status"]] = (counts[m["Medicine status"]] || 0) + 1;
    });
    return Object.entries(counts).map(([name, value]) => ({ name, value }));
  }, [filteredMedicines]);

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex justify-between items-center mb-6">
        <Link href="/">
          <Button variant="ghost" className="pl-0 hover:bg-transparent">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Evicenter
          </Button>
        </Link>
        <ThemeToggle />
      </div>

      <h1 className="text-3xl font-bold mb-6">Medicines Explorer</h1>

      <div className="prose max-w-none mb-8 dark:text-gray-200">
        <p>
          Explore authorised medicines and related information. Use the search
          and filters to quickly find medicines and uncover insights.
        </p>
      </div>

      <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg mb-6">
        <h3 className="text-lg font-medium mb-4 flex items-center dark:text-white">
          <Filter className="mr-2 h-4 w-4" /> Filter Medicines
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Input
            placeholder="Search name or INN"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="md:col-span-1"
          />
          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger>
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map((cat) => (
                <SelectItem key={cat} value={cat}>
                  {cat}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger>
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              {statuses.map((st) => (
                <SelectItem key={st} value={st}>
                  {st}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="w-full h-64 mb-6">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={statusChartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" className="text-xs" />
            <YAxis allowDecimals={false} />
            <ReTooltip />
            <Bar dataKey="value" fill="#fb923c" />
          </BarChart>
        </ResponsiveContainer>
      </div>

      {filteredMedicines.length === 0 ? (
        <div className="text-center py-12 text-muted-foreground">
          No medicines match your search.
        </div>
      ) : (
        <Accordion type="multiple" className="w-full">
          {filteredMedicines.slice(0, 100).map((med, idx) => (
            <AccordionItem key={idx} value={`${med["Name of medicine"]}-${idx}`}>
              <AccordionTrigger className="text-left">
                <div className="flex items-center gap-2">
                  <span className="font-medium">
                    {med["Name of medicine"]}
                  </span>
                  <span className="text-sm text-muted-foreground">
                    {med["Medicine status"]}
                  </span>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <Card className="mb-4">
                  <CardHeader>
                    <CardTitle>{med["Name of medicine"]}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {med["International non-proprietary name (INN) / common name"] && (
                      <p>
                        <strong>INN:</strong> {med["International non-proprietary name (INN) / common name"]}
                      </p>
                    )}
                    {med["Active substance"] && (
                      <p>
                        <strong>Active substance:</strong> {med["Active substance"]}
                      </p>
                    )}
                    {med["Therapeutic area (MeSH)"] && (
                      <p>
                        <strong>Therapeutic area:</strong> {med["Therapeutic area (MeSH)"]}
                      </p>
                    )}
                    {med["Therapeutic indication"] && (
                      <p>
                        <strong>Indication:</strong> {med["Therapeutic indication"]}
                      </p>
                    )}
                    {med["Marketing authorisation date"] && (
                      <p>
                        <strong>MA date:</strong> {med["Marketing authorisation date"]}
                      </p>
                    )}
                    {med["Medicine URL"] && (
                      <p>
                        <a
                          href={med["Medicine URL"]}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-amber-600 underline inline-flex items-center"
                        >
                          More information <ExternalLink className="ml-1 h-4 w-4" />
                        </a>
                      </p>
                    )}
                  </CardContent>
                </Card>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      )}
    </div>
  );
}