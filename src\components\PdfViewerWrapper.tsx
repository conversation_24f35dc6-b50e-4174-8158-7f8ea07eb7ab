"use client";

import React, { useEffect, useState, forwardRef } from 'react';
import { RPProvider, RPDefaultLayout, RPPages } from '@pdf-viewer/react';
import { useTheme } from "@/contexts/ThemeContext";

interface PdfViewerWrapperProps {
  url: string | null;
  highlightBbox?: number[];
  pageNumber?: number;
  onLoaded?: (pdf: any) => void;
}

// Use forwardRef to properly handle refs in React 19
const PdfViewerWrapper = forwardRef<HTMLDivElement, PdfViewerWrapperProps>(({ 
  url,
  highlightBbox,
  pageNumber,
  onLoaded
}, ref) => {
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';
  const [viewer, setViewer] = useState<any>(null);

  // Add cleanup for PDF viewer
  useEffect(() => {
    return () => {
      if (viewer) {
        try {
          // Run any built-in cleanup methods without terminating the shared worker
          viewer.cleanup && viewer.cleanup();
          viewer.pdfDocument && viewer.pdfDocument.destroy();
          // Avoid terminating pdfWorker here to prevent "Transport destroyed" errors
        } catch (error) {
          console.log('PDF viewer cleanup error:', error);
        }
      }
    };
  }, [viewer]);

  // Don't render the PDF viewer if URL is null
  if (!url) {
    return (
      <div ref={ref} className="h-full flex items-center justify-center text-gray-500">
        <p>No PDF selected</p>
      </div>
    );
  }

  return (
    <div ref={ref}>
      <RPProvider 
        src={url}
        onLoaded={(pdfViewer) => {
          setViewer(pdfViewer);
          onLoaded?.(pdfViewer);
        }}
        darkMode={isDarkMode}
        initialPage={pageNumber && pageNumber > 0 ? pageNumber : 1}
        characterMap={{
          url: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@2.0.288/cmaps/',
          isCompressed: true
        }}
      >
        <RPDefaultLayout>
          <RPPages />
        </RPDefaultLayout>
      </RPProvider>
    </div>
  );
});

// Add display name for better debugging
PdfViewerWrapper.displayName = 'PdfViewerWrapper';

export default PdfViewerWrapper;
