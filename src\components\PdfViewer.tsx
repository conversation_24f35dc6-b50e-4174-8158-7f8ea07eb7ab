"use client";

import { R<PERSON><PERSON><PERSON>, RPDefaultLayout, RPPages, type PDFDocumentProxy } from '@pdf-viewer/react';
import { type FC, useEffect } from 'react';
import { useTheme } from "@/contexts/ThemeContext";

interface PdfViewerProps {
  url: string;
  onLoaded?: (pdf: PDFDocumentProxy) => void;
  initialPage?: number;
}

const PdfViewer: FC<PdfViewerProps> = ({ url, onLoaded, initialPage }) => {
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';
  
  const handleLoadError = (error: any) => {
    console.error("PDF load error:", error);
  };

  const handleDarkModeChange = (darkMode: boolean) => {
    // This is handled by the theme context, but we can add additional logic here if needed
  };

  // Add error boundary or try-catch for PDF operations
  const safeOnLoaded = (pdf: PDFDocumentProxy) => {
    try {
      if (onLoaded) onLoaded(pdf);
    } catch (error) {
      console.error("PDF onLoaded error:", error);
    }
  };

  // Ensure initialPage is a number and is not zero-indexed
  // PDF.js uses 1-based indexing, but RPProvider expects 0-based indexing
  const pageIndex = initialPage && initialPage > 0 ? initialPage - 1 : 0;

  return (
    <div className="w-full h-full">
      <RPProvider 
        src={url}
        onLoaded={safeOnLoaded}
        onLoadError={handleLoadError}
        onDarkModeChange={handleDarkModeChange}
        darkMode={isDarkMode}
        initialPage={pageIndex}
        characterMap={{
          url: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@2.0.288/cmaps/',
          isCompressed: true
        }}
      >
        <RPDefaultLayout style={{ height: '95%', width: '100%' }}>
          <RPPages />
        </RPDefaultLayout>
      </RPProvider>
    </div>
  );
};

export default PdfViewer;
