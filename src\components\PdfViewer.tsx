"use client";

import { R<PERSON><PERSON><PERSON>, RPDefaultLayout, RPPages, type PDFDocumentProxy } from '@pdf-viewer/react';
import { type FC, useState } from 'react';
import { useTheme } from "@/contexts/ThemeContext";
import { Loader2 } from 'lucide-react';

interface PdfViewerProps {
  url: string;
  onLoaded?: (pdf: PDFDocumentProxy) => void;
  initialPage?: number;
}

const PdfViewer: FC<PdfViewerProps> = ({ url, onLoaded, initialPage }) => {
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';
  const [isLoading, setIsLoading] = useState(true);
  
  const handleLoadError = (error: any) => {
    console.error("PDF load error:", error);
    setIsLoading(false);
  };

  const handleDarkModeChange = (darkMode: boolean) => {
    // This is handled by the theme context, but we can add additional logic here if needed
  };

  // Add error boundary or try-catch for PDF operations
  const safeOnLoaded = (pdf: PDFDocumentProxy) => {
    try {
      setIsLoading(false);
      if (onLoaded) onLoaded(pdf);
    } catch (error) {
      console.error("PDF onLoaded error:", error);
    }
  };

  // Ensure initialPage is at least 1. RPProvider expects a 1-based page number
  // which matches PDF.js, so no conversion is necessary here.
  const startPage = initialPage && initialPage > 0 ? initialPage : 1;

  return (
    <div className="w-full h-full relative">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white dark:bg-gray-900 z-50">
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
            <p className="text-gray-500">Loading PDF...</p>
          </div>
        </div>
      )}
      
      <RPProvider 
        src={url}
        onLoaded={safeOnLoaded}
        onLoadError={handleLoadError}
        onDarkModeChange={handleDarkModeChange}
        darkMode={isDarkMode}
        initialPage={startPage}
        characterMap={{
          url: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@2.0.288/cmaps/',
          isCompressed: true
        }}
      >
        <RPDefaultLayout style={{ height: '95%', width: '100%' }}>
          <RPPages />
        </RPDefaultLayout>
      </RPProvider>
    </div>
  );
};

export default PdfViewer;
