"use client";

import React, { useState } from "react";
import { SubpageHeader } from "@/components/SubpageHeader";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { LangGraph } from "@/lib/langgraph";

interface Step {
  id: number;
  name: string;
  prompt: string;
}

export default function AgentWorkflowsPage() {
  const [steps, setSteps] = useState<Step[]>([
    { id: 1, name: "Agent 1", prompt: "" },
  ]);
  const [initialInput, setInitialInput] = useState("");
  const [running, setRunning] = useState(false);
  const [outputs, setOutputs] = useState<Record<string, string>>({});

  const addStep = () => {
    setSteps((prev) => [
      ...prev,
      { id: Date.now(), name: `Agent ${prev.length + 1}`, prompt: "" },
    ]);
  };

  const updateStep = (id: number, field: keyof Step, value: string) => {
    setSteps((prev) =>
      prev.map((s) => (s.id === id ? { ...s, [field]: value } : s))
    );
  };

  const removeStep = (id: number) => {
    setSteps((prev) => prev.filter((s) => s.id !== id));
  };

  const runWorkflow = async () => {
    setRunning(true);
    const graph = new LangGraph();
    let prevNode: string | undefined;

    steps.forEach((step, idx) => {
      const nodeName = `step${idx}`;
      graph.addNode(nodeName, async (input: string) => {
        const res = await fetch("/api/chat", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            messages: [
              { role: "system", content: step.prompt },
              { role: "user", content: input },
            ],
          }),
        });
        const data = await res.json();
        return data.content as string;
      });
      if (idx === 0) {
        graph.setEntry(nodeName);
      }
      if (prevNode) {
        graph.addEdge(prevNode, nodeName);
      }
      prevNode = nodeName;
    });

    try {
      const result = await graph.run(initialInput);
      setOutputs(result);
    } catch (err) {
      console.error(err);
    } finally {
      setRunning(false);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4 space-y-6">
      <SubpageHeader current="agent-workflows" />
      <h1 className="text-3xl font-bold">Multi-Agent Workflows</h1>

      <div className="space-y-4">
        {steps.map((step) => (
          <div key={step.id} className="border p-4 rounded-md space-y-2">
            <div className="flex gap-2">
              <Input
                value={step.name}
                onChange={(e) => updateStep(step.id, "name", e.target.value)}
                placeholder="Agent Name"
              />
              <Button
                type="button"
                variant="outline"
                onClick={() => removeStep(step.id)}
              >
                Remove
              </Button>
            </div>
            <Textarea
              value={step.prompt}
              onChange={(e) => updateStep(step.id, "prompt", e.target.value)}
              placeholder="System Prompt"
            />
          </div>
        ))}
        <Button type="button" variant="secondary" onClick={addStep}>
          Add Step
        </Button>
      </div>

      <div className="space-y-2">
        <Textarea
          value={initialInput}
          onChange={(e) => setInitialInput(e.target.value)}
          placeholder="Initial input"
        />
        <Button onClick={runWorkflow} disabled={running}>
          {running ? "Running..." : "Run Workflow"}
        </Button>
      </div>

      {Object.keys(outputs).length > 0 && (
        <div className="space-y-4">
          {steps.map((_, idx) => {
            const name = `step${idx}`;
            return (
              <div key={name} className="border p-4 rounded-md">
                <h3 className="font-medium mb-2">{steps[idx].name}</h3>
                <pre className="whitespace-pre-wrap">{outputs[name]}</pre>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
