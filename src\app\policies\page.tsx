"use client";

import React from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from 'next/link';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function PoliciesPage() {
  return (
    <div className="container max-w-3xl mx-auto py-8 px-4">
      <Link href="/">
        <Button variant="ghost" className="mb-6 pl-0 hover:bg-transparent">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Vendor Hub
        </Button>
      </Link>
      
      <h1 className="text-3xl font-bold mb-6">Policies</h1>
      
      <Tabs defaultValue="terms" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="terms">Terms of Service</TabsTrigger>
          <TabsTrigger value="privacy">Privacy Policy</TabsTrigger>
          <TabsTrigger value="data">Data Usage</TabsTrigger>
        </TabsList>
        
        <TabsContent value="terms" className="mt-6">
          <div className="prose max-w-none">
            <h2 className="text-xl font-semibold mb-4">Terms of Service</h2>
            <p className="mb-4">
              Welcome to Vendor Hub. By accessing or using our platform, you agree to be bound by these Terms of Service.
            </p>
            
            <h3 className="text-lg font-medium mt-6 mb-2">1. Acceptance of Terms</h3>
            <p className="mb-4">
              By accessing or using Vendor Hub, you agree to be bound by these Terms of Service and all applicable laws and regulations. If you do not agree with any of these terms, you are prohibited from using or accessing this site.
            </p>
            
            <h3 className="text-lg font-medium mt-6 mb-2">2. Use License</h3>
            <p className="mb-4">
              Permission is granted to temporarily access the materials on Vendor Hub for personal, non-commercial transitory viewing only. This is the grant of a license, not a transfer of title, and under this license you may not:
            </p>
            <ul className="list-disc pl-6 mb-4">
              <li>Modify or copy the materials</li>
              <li>Use the materials for any commercial purpose</li>
              <li>Attempt to decompile or reverse engineer any software contained on Vendor Hub</li>
              <li>Remove any copyright or other proprietary notations from the materials</li>
              <li>Transfer the materials to another person or "mirror" the materials on any other server</li>
            </ul>
            
            <h3 className="text-lg font-medium mt-6 mb-2">3. Disclaimer</h3>
            <p className="mb-4">
              The materials on Vendor Hub are provided on an 'as is' basis. Vendor Hub makes no warranties, expressed or implied, and hereby disclaims and negates all other warranties including, without limitation, implied warranties or conditions of merchantability, fitness for a particular purpose, or non-infringement of intellectual property or other violation of rights.
            </p>
            
            <h3 className="text-lg font-medium mt-6 mb-2">4. Limitations</h3>
            <p className="mb-4">
              In no event shall Vendor Hub or its suppliers be liable for any damages (including, without limitation, damages for loss of data or profit, or due to business interruption) arising out of the use or inability to use the materials on Vendor Hub, even if Vendor Hub or a Vendor Hub authorized representative has been notified orally or in writing of the possibility of such damage.
            </p>
            
            <div className="bg-amber-50 p-4 rounded-md border border-amber-200 mt-6">
              <p className="text-sm">
                Last updated: June 1, 2025
              </p>
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="privacy" className="mt-6">
          <div className="prose max-w-none">
            <h2 className="text-xl font-semibold mb-4">Privacy Policy</h2>
            <p className="mb-4">
              At Vendor Hub, we take your privacy seriously. This Privacy Policy describes how we collect, use, and share information about you when you use our website and services.
            </p>
            
            <h3 className="text-lg font-medium mt-6 mb-2">1. Information We Collect</h3>
            <p className="mb-4">
              We collect information you provide directly to us, such as when you create an account, update your profile, use interactive features, participate in surveys, or communicate with us.
            </p>
            <p className="mb-4">
              This information may include:
            </p>
            <ul className="list-disc pl-6 mb-4">
              <li>Name and contact information</li>
              <li>Company information</li>
              <li>Login credentials</li>
              <li>Profile information</li>
              <li>Communications and feedback</li>
              <li>Payment information</li>
            </ul>
            
            <h3 className="text-lg font-medium mt-6 mb-2">2. How We Use Your Information</h3>
            <p className="mb-4">
              We use the information we collect to:
            </p>
            <ul className="list-disc pl-6 mb-4">
              <li>Provide, maintain, and improve our services</li>
              <li>Process transactions and send related information</li>
              <li>Send technical notices, updates, security alerts, and support messages</li>
              <li>Respond to your comments, questions, and requests</li>
              <li>Communicate with you about products, services, offers, and events</li>
              <li>Monitor and analyze trends, usage, and activities</li>
            </ul>
            
            <h3 className="text-lg font-medium mt-6 mb-2">3. Sharing of Information</h3>
            <p className="mb-4">
              We may share information about you as follows:
            </p>
            <ul className="list-disc pl-6 mb-4">
              <li>With vendors, consultants, and other service providers who need access to such information to carry out work on our behalf</li>
              <li>In response to a request for information if we believe disclosure is in accordance with any applicable law, regulation, or legal process</li>
              <li>If we believe your actions are inconsistent with our user agreements or policies, or to protect the rights, property, and safety of Vendor Hub or others</li>
              <li>In connection with, or during negotiations of, any merger, sale of company assets, financing, or acquisition of all or a portion of our business by another company</li>
              <li>With your consent or at your direction</li>
            </ul>
            
            <div className="bg-amber-50 p-4 rounded-md border border-amber-200 mt-6">
              <p className="text-sm">
                Last updated: May 15, 2025
              </p>
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="data" className="mt-6">
          <div className="prose max-w-none">
            <h2 className="text-xl font-semibold mb-4">Data Usage Policy</h2>
            <p className="mb-4">
              This Data Usage Policy explains how Vendor Hub collects, processes, and stores data from users of our platform.
            </p>
            
            <h3 className="text-lg font-medium mt-6 mb-2">1. Data Collection</h3>
            <p className="mb-4">
              We collect various types of data to provide and improve our services:
            </p>
            <ul className="list-disc pl-6 mb-4">
              <li><strong>Account Data:</strong> Information provided during account creation and profile setup</li>
              <li><strong>Usage Data:</strong> Information about how you interact with our platform</li>
              <li><strong>Communication Data:</strong> Content of messages sent through our platform</li>
              <li><strong>Technical Data:</strong> Device information, IP addresses, and browser details</li>
            </ul>
            
            <h3 className="text-lg font-medium mt-6 mb-2">2. Data Processing</h3>
            <p className="mb-4">
              We process data for the following purposes:
            </p>
            <ul className="list-disc pl-6 mb-4">
              <li>Providing and maintaining our services</li>
              <li>Improving and personalizing user experience</li>
              <li>Analyzing usage patterns to enhance platform functionality</li>
              <li>Facilitating communication between users</li>
              <li>Ensuring platform security and preventing fraud</li>
            </ul>
            
            <h3 className="text-lg font-medium mt-6 mb-2">3. Data Retention</h3>
            <p className="mb-4">
              We retain different types of data for varying periods:
            </p>
            <ul className="list-disc pl-6 mb-4">
              <li><strong>Account Data:</strong> Retained as long as your account is active</li>
              <li><strong>Communication Data:</strong> Retained for up to 24 months</li>
              <li><strong>Usage Data:</strong> Retained for up to 36 months</li>
              <li><strong>Technical Data:</strong> Retained for up to 12 months</li>
            </ul>
            <p className="mb-4">
              You may request deletion of your data at any time by contacting our support team.
            </p>
            
            <h3 className="text-lg font-medium mt-6 mb-2">4. Data Security</h3>
            <p className="mb-4">
              We implement appropriate technical and organizational measures to protect your data against unauthorized access, alteration, disclosure, or destruction. These measures include encryption, access controls, and regular security assessments.
            </p>
            
            <div className="bg-amber-50 p-4 rounded-md border border-amber-200 mt-6">
              <p className="text-sm">
                Last updated: April 30, 2025
              </p>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
