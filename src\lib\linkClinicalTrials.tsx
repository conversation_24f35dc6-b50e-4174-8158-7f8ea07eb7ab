import React from "react";

/**
 * Convert any clinical trial identifiers in the text to links. If the text
 * already contains HTML, a string with the hyperlinks injected is returned so
 * that callers can use `dangerouslySetInnerHTML` to preserve the markup.
 */
export function linkClinicalTrials(text: string): React.ReactNode | string {
  // If the text has HTML tags, perform a simple string replacement. This keeps
  // the existing markup intact while inserting the links.
  if (/<[^>]+>/.test(text)) {
    return text.replace(/(NCT\d{8})/gi, (m) =>
      `<a href="https://clinicaltrials.gov/study/${m.toUpperCase()}" target="_blank" rel="noopener noreferrer" class="underline text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300">${m.toUpperCase()}</a>`,
    );
  }

  // Plain text can be safely converted into React nodes.
  return text.split(/(NCT\d{8})/gi).map((part, i) =>
    /^NCT\d{8}$/i.test(part) ? (
      <a
        key={i}
        href={`https://clinicaltrials.gov/study/${part.toUpperCase()}`}
        target="_blank"
        rel="noopener noreferrer"
        className="underline text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
      >
        {part.toUpperCase()}
      </a>
    ) : (
      part
    ),
  );
}


