import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';

// Define interface for vendor objects
interface VendorDocument {
  name: string;
  _id?: any;
}

export async function GET(request: NextRequest) {
  try {
    // Get search parameters
    const searchParams = request.nextUrl.searchParams;
    const title = searchParams.get('title') || '';
    const abstract = searchParams.get('abstract') || '';
    const therapeuticArea = searchParams.get('therapeuticArea') || '';
    const region = searchParams.get('region') || '';
    const products = searchParams.get('products') || '';
    const studyType = searchParams.get('studyType') || '';

    // Connect to MongoDB
    const { db } = await connectToDatabase();
    
    // Build query with $and to support multiple fields (instead of $or)
    const queryConditions = [];
    const andConditions = [];

    if (title) {
      const titleConditions = [
        { Title: { $regex: title, $options: 'i' } },
        { title: { $regex: title, $options: 'i' } }
      ];
      andConditions.push({ $or: titleConditions });
    }

    if (abstract) {
      const abstractConditions = [
        { Abstract: { $regex: abstract, $options: 'i' } },
        { abstract: { $regex: abstract, $options: 'i' } }
      ];
      andConditions.push({ $or: abstractConditions });
    }

    if (therapeuticArea) {
      const taConditions = [
        { 'therapeutic_area': { $regex: therapeuticArea, $options: 'i' } },
        { 'therapeuticArea': { $regex: therapeuticArea, $options: 'i' } },
        { 'therapeutic-area': { $regex: therapeuticArea, $options: 'i' } }
      ];
      andConditions.push({ $or: taConditions });
    }

    if (region) {
      const regionConditions = [
        { 'region': { $regex: region, $options: 'i' } },
        { 'regions': { $regex: region, $options: 'i' } }
      ];
      andConditions.push({ $or: regionConditions });
    }

    if (products) {
      const productConditions = [
        { 'products': { $regex: products, $options: 'i' } },
        { 'product': { $regex: products, $options: 'i' } }
      ];
      andConditions.push({ $or: productConditions });
    }

    if (studyType) {
      const studyTypeConditions = [
        { 'study_type': { $regex: studyType, $options: 'i' } },
        { 'studyType': { $regex: studyType, $options: 'i' } },
        { 'study-type': { $regex: studyType, $options: 'i' } }
      ];
      andConditions.push({ $or: studyTypeConditions });
    }

    // Create the final query using $and to combine all conditions
    const query = andConditions.length > 0 ? { $and: andConditions } : {};
    
    console.log('Publication search query:', JSON.stringify(query));
    
    // Execute query
    const publications = await db.collection('vendor_articles').find(query).limit(100).toArray();

    console.log(`Found ${publications.length} matching publications`);

    // Extract vendor information from publications
    const vendorNames: string[] = [];
    const vendorArticleMap: Record<string, any[]> = {}; // Track articles per vendor

    for (const pub of publications) {
      // Try different vendor-related fields
      const possibleVendorFields = ['vendor', 'Vendor', 'OriginalAffiliation', 'affiliation', 'Affiliation', 'author_affiliation'];
      
      let vendorFound = false;
      for (const field of possibleVendorFields) {
        if (pub[field] && typeof pub[field] === 'string' && pub[field].trim() !== '') {
          const vendorName = pub[field];
          vendorNames.push(vendorName);
          
          // Add to vendor-article mapping with more details
          if (!vendorArticleMap[vendorName]) {
            vendorArticleMap[vendorName] = [];
          }
          vendorArticleMap[vendorName].push({
            title: pub.Title || pub.title,
            journal: pub.Journal || pub.journal,
            pubDate: pub.PubDate || pub.pubdate || pub.publication_date,
            authors: pub.Authors || pub.authors,
            abstract: pub.Abstract || pub.abstract,
            PMID: pub.PMID || pub.pmid,
            DOI: pub.DOI || pub.doi,
            therapeuticArea: pub.therapeutic_area || pub.therapeuticArea || pub['therapeutic-area'],
            // Include search terms for highlighting
            searchTerms: andConditions.length > 0 
              ? andConditions.flatMap(condition => 
                  condition.$or 
                    ? condition.$or.map((orCond: any) => Object.keys(orCond)[0]) 
                    : []
                )
              : []
          });
          
          vendorFound = true;
          break;
        }
      }
      
      // If no vendor field found, try to extract from title or authors
      if (!vendorFound && pub.Title) {
        // Check if title contains company names (common format: "... by [Company]")
        const titleMatch = pub.Title.match(/by\s+([A-Z][A-Za-z\s]+(?:Inc|LLC|Ltd|GmbH|BV|SA|AG|Co|Group|Consulting|Research|Health|Sciences))/);
        if (titleMatch && titleMatch[1]) {
          const vendorName = titleMatch[1].trim();
          vendorNames.push(vendorName);
          
          // Add to vendor-article mapping
          if (!vendorArticleMap[vendorName]) {
            vendorArticleMap[vendorName] = [];
          }
          vendorArticleMap[vendorName].push({
            title: pub.Title || pub.title,
            therapeuticArea: pub.therapeutic_area || pub.therapeuticArea || pub['therapeutic-area'],
            // Fix the searchTerms extraction to work with $and queries
            searchTerms: andConditions.length > 0 
              ? andConditions.flatMap(condition => 
                  condition.$or 
                    ? condition.$or.map((orCond: any) => Object.keys(orCond)[0]) 
                    : []
                )
              : []
          });
        }
      }
      
      // Try to extract from authors affiliations if available
      if (!vendorFound && pub.authors && Array.isArray(pub.authors)) {
        for (const author of pub.authors) {
          if (author.affiliation && typeof author.affiliation === 'string') {
            const vendorName = author.affiliation;
            vendorNames.push(vendorName);
            
            // Add to vendor-article mapping
            if (!vendorArticleMap[vendorName]) {
              vendorArticleMap[vendorName] = [];
            }
            vendorArticleMap[vendorName].push({
              title: pub.Title || pub.title,
              therapeuticArea: pub.therapeutic_area || pub.therapeuticArea || pub['therapeutic-area'],
              // Fix the searchTerms extraction to work with $and queries
              searchTerms: andConditions.length > 0 
                ? andConditions.flatMap(condition => 
                    condition.$or 
                      ? condition.$or.map((orCond: any) => Object.keys(orCond)[0]) 
                      : []
                  )
                : []
            });
          }
        }
      }
    }

    // Remove duplicates and filter out empty values
    const uniqueVendorNames = [...new Set(vendorNames.filter(name => name && name.trim() !== ''))];

    console.log(`Found ${uniqueVendorNames.length} unique vendors:`, uniqueVendorNames);

    // Log detailed vendor-article information
    console.log('Vendor-article details:');
    uniqueVendorNames.forEach(vendor => {
      console.log(`\nVendor: ${vendor}`);
      console.log(`Articles: ${vendorArticleMap[vendor].length}`);
      vendorArticleMap[vendor].slice(0, 3).forEach((article, i) => {
        console.log(`  ${i+1}. Title: ${article.title?.substring(0, 100)}...`);
        console.log(`     Therapeutic Area: ${article.therapeuticArea || 'Not specified'}`);
      });
      if (vendorArticleMap[vendor].length > 3) {
        console.log(`  ... and ${vendorArticleMap[vendor].length - 3} more articles`);
      }
    });

    // If no vendor names found but we have publications, try to get vendor names from a separate collection
    if (uniqueVendorNames.length === 0 && publications.length > 0) {
      // Get all vendor names from the vendors collection
      const allVendors = await db.collection('vendors').find({}, { projection: { name: 1 } }).toArray();
      const allVendorNames = allVendors.map((v: VendorDocument) => v.name);
      
      console.log(`Checking ${allVendorNames.length} vendors for matches`);
      
      // For each publication, try to find a matching vendor
      for (const pub of publications) {
        const pubText = JSON.stringify(pub).toLowerCase();
        
        for (const vendorName of allVendorNames) {
          if (pubText.includes(vendorName.toLowerCase())) {
            uniqueVendorNames.push(vendorName);
          }
        }
      }
      
      console.log(`After text search, found ${uniqueVendorNames.length} unique vendors`);
    }
    
    return NextResponse.json({ 
      success: true, 
      vendorNames: uniqueVendorNames,
      count: uniqueVendorNames.length,
      publicationsCount: publications.length,
      vendorPublications: vendorArticleMap // Include the full publication details
    });
  } catch (error) {
    console.error('Error searching publications:', error);
    return NextResponse.json(
      { error: 'Failed to search publications' },
      { status: 500 }
    );
  }
}
