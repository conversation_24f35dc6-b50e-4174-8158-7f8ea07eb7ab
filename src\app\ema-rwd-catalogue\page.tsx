"use client";

import React, { useState, useEffect, useMemo } from 'react';
import Image from 'next/image';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectSeparator,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { ArrowLeft, Search, Filter, ChevronDown, ChevronUp, ExternalLink, Info, FileText, Globe, MessageSquare, Copy, Check, X } from 'lucide-react';
import Link from 'next/link';
import { useTheme } from '@/contexts/ThemeContext';
import { ThemeToggle } from '@/components/ThemeToggle';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { getCountryCode } from '@/utils/countryUtils';

interface RwdDataSource {
  Title: string;
  "Data source domain": string;
  "URL LINK": string;
  "First published": string;
  "Updated": string;
  "Data source ID": number;
  "Data source acronym": string;
  "Data holder": string | string[];
  "Data source type": string | string[];
  "Care setting": string | string[];
  "DARWIN EU data partner?": boolean;
  "Data source countries": string;
  "Data source languages": string;
  "Data source established": string;
  "Data source time span (First collection)": string;
  "Data source time span (Last collection)": string | null;
  "Population size": number | string;
  "Active population size": number | string;
  [key: string]: any; // For other fields
}

export default function EmaRwdCataloguePage() {
  const { theme } = useTheme();
  const [dataSources, setDataSources] = useState<RwdDataSource[]>([]);
  const [filteredSources, setFilteredSources] = useState<RwdDataSource[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchField, setSearchField] = useState('all');
  const [selectedSources, setSelectedSources] = useState<number[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeFilters, setActiveFilters] = useState<Record<string, string[]>>({});
  const [showComparison, setShowComparison] = useState(false);
  const [selectedSource, setSelectedSource] = useState<RwdDataSource | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [contactInfoSource, setContactInfoSource] = useState<RwdDataSource | null>(null);
  const [copiedMainEmail, setCopiedMainEmail] = useState(false);
  const [copiedAltEmail, setCopiedAltEmail] = useState(false);
  
  // Add state for expanded filter view
  const [showAllFilters, setShowAllFilters] = useState(false);

  // Define priority filters that should be shown initially
  const priorityFilters = [
    "Data source type",
    "Data source type, other",
    "Care setting",
    "DARWIN EU data partner?",
    "Data source countries",
    "Disease information",
    "Disease information collected",
    "Disease information collected, other",
    "Rare diseases",
    "Pregnancy and/or neonates",
    "Hospital admission and/or discharge",
    "ICU admission",
    "Cause of death",
    "Prescriptions of medicines",
    "Dispensing of medicines",
    "Advance therapy medicinal products (ATMP)",
    "Contraception",
    "Indication for use",
    "Medical devices",
    "Administration of vaccines",
    "Procedures",
    "Clinical measurements",
    "Healthcare provider",
    "Genetic data",
    "Biomarker data",
    "Patient-reported outcomes",
    "Patient-generated data",
    "Units of healthcare utilisation",
    "Unique identifier for persons",
    "Diagnostic codes",
    "Lifestyle factors",
    "Sociodemographic information",
    "Age group",
    "Biospecimen access",
    "Access to subject details",
    "Possibility of data validation",
    "Records are preserved indefinitely",
    "Quality of life measurements",
    "Medicinal product information",
    "Approval for publication",
    "CDM mapping"
  ];

  // Define common filter categories with proper typing
  const [filterCategories, setFilterCategories] = useState<Record<string, string[]>>({
    "Data source type": [],
    "Care setting": [],
    "Data source countries": [],
    "Age group": [],
    "Data holder": [],
    "Data source languages": [],
    "Data source established": [],
    "First published": [],
    "Updated": [],
    "Data source time span (First collection)": [],
    "Data source time span (Last collection)": [],
    "DARWIN EU data partner?": []
  });

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        
        // Update the path to the new JSON file
        const response = await fetch('/ema-rwd-catalogue-export-data-6.4.2025_combined.json');
        
        if (!response.ok) {
          throw new Error(`Failed to load data: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        setDataSources(data);
        setFilteredSources(data);
        
        // Get all filterable fields from the first data source
        const sampleSource = data[0];
        const filterableFields: string[] = [];
        
        // Identify fields that can be used for filtering
        Object.entries(sampleSource).forEach(([key, value]) => {
          // Skip ID, Title, URL, and other non-filterable fields
          const nonFilterableFields = [
            "Data source ID", "Title", "URL LINK", "Data source acronym", 
            "Population size", "Active population size"
          ];
          
          if (!nonFilterableFields.includes(key)) {
            filterableFields.push(key);
          }
        });
        
        // Initialize extracted filters with all filterable fields
        const extractedFilters: Record<string, Set<string>> = {};
        filterableFields.forEach(field => {
          extractedFilters[field] = new Set();
        });

        // Helper function to split strings while respecting parentheses
        const splitRespectingParentheses = (str: string): string[] => {
          // This regex splits by commas but ignores commas inside parentheses
          const regex = /,\s*(?![^()]*\))/;
          return str.split(regex).map(item => item.trim()).filter(Boolean);
        };

        // Collect all unique individual values for each field
        data.forEach((source: RwdDataSource) => {
          filterableFields.forEach(field => {
            const value = source[field];
            
            if (value === undefined || value === null) {
              return;
            }
            
            if (Array.isArray(value)) {
              value.filter(Boolean).forEach(v => {
                extractedFilters[field].add(String(v).trim());
              });
            } else if (typeof value === 'string') {
              splitRespectingParentheses(value).forEach(v => {
                extractedFilters[field].add(v.trim());
              });
            } else if (typeof value === 'boolean') {
              extractedFilters[field].add(value ? 'Yes' : 'No');
            } else if (field === "Medicinal product information" && 
                      (value === "Captured" || value === "Not Captured")) {
              // Treat "Captured" as "Yes" and "Not Captured" as "No"
              extractedFilters[field].add(value === "Captured" ? 'Yes' : 'No');
            } else {
              extractedFilters[field].add(String(value).trim());
            }
          });
        });
        
        // Convert Sets to sorted arrays
        const sortedFilters: Record<string, string[]> = {};
        Object.keys(extractedFilters).forEach(key => {
          sortedFilters[key] = Array.from(extractedFilters[key]).filter(Boolean).sort();
        });
        
        setFilterCategories(sortedFilters);
      } catch (error) {
        console.error('Error loading data:', error);
        // Show error message to user
        alert(`Failed to load data: ${error instanceof Error ? error.message : 'Unknown error'}`);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadData();
  }, []);

  // Apply search and filters
  useEffect(() => {
    if (!dataSources.length) return;
    
    let results = [...dataSources];
    
    // Helper function to split strings while respecting parentheses
    const splitRespectingParentheses = (str: string): string[] => {
      // This regex splits by commas but ignores commas inside parentheses
      const regex = /,\s*(?![^()]*\))/;
      return str.split(regex).map(item => item.trim()).filter(Boolean);
    };
    
    // Apply text search
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      results = results.filter(source => {
        if (searchField === 'all') {
          // Search in all text fields
          return Object.entries(source).some(([key, value]) => {
            if (typeof value === 'string') {
              return value.toLowerCase().includes(query);
            } else if (Array.isArray(value)) {
              return value.some(item => 
                typeof item === 'string' && item.toLowerCase().includes(query)
              );
            }
            return false;
          });
        } else {
          // Search in specific field
          const value = source[searchField];
          if (typeof value === 'string') {
            return value.toLowerCase().includes(query);
          } else if (Array.isArray(value)) {
            return value.some(item => 
              typeof item === 'string' && item.toLowerCase().includes(query)
            );
          }
          return false;
        }
      });
    }
    
    // Apply active filters
    Object.entries(activeFilters).forEach(([field, values]) => {
      if (values.length) {
        results = results.filter(source => {
          const sourceValue = source[field];
          
          // Create an array of individual values from the source
          let sourceValues: string[] = [];
          
          if (Array.isArray(sourceValue)) {
            sourceValues = sourceValue.map(v => typeof v === 'string' ? v.trim().toLowerCase() : String(v).toLowerCase());
          } else if (typeof sourceValue === 'string') {
            sourceValues = splitRespectingParentheses(sourceValue).map(v => v.trim().toLowerCase());
          } else if (sourceValue !== null && sourceValue !== undefined) {
            sourceValues = [String(sourceValue).toLowerCase()];
          }
          
          // Check if any of the selected filter values match any of the source values (case-insensitive)
          return values.some(value => 
            sourceValues.some(sourceVal => 
              sourceVal === value.toLowerCase()
            )
          );
        });
      }
    });
    
    setFilteredSources(results);
  }, [searchQuery, searchField, activeFilters, dataSources]);

  // Define the most commonly used search fields
  const commonSearchFields = [
    "all", // Keep "All Fields" as the first option
    "Title",
    "Data source acronym", 
    "Data holder",
    "Data source countries",
    "DATA SOURCE TYPE",
    "Care setting",
    "DISEASE INFORMATION COLLECTED",
    "PRESCRIPTIONS VOCABULARY",
    "DISPENSING VOCABULARY",
    "INDICATION VOCABULARY",
    "PROCEDURES VOCABULARY",
    "QUALITY OF LIFE MEASUREMENTS VOCABULARY",
    "LIFESTYLE FACTORS COLLECTED",
    "SOCIODEMOGRAPHIC INFORMATION COLLECTED"
  ];

  // Get all searchable fields
  const searchableFields = useMemo(() => {
    if (!dataSources.length) return [];
    
    const fields = new Set<string>();
    const sample = dataSources[0];
    
    Object.entries(sample).forEach(([key, value]) => {
      if (typeof value === 'string' || Array.isArray(value)) {
        fields.add(key);
      }
    });
    
    return Array.from(fields).sort();
  }, [dataSources]);

  // Separate common fields from all fields
  const getOrganizedSearchFields = useMemo(() => {
    const common = commonSearchFields.filter(field => 
      field === "all" || searchableFields.includes(field)
    );
    
    const other = searchableFields.filter(
      field => !commonSearchFields.includes(field)
    );
    
    return { common, other };
  }, [searchableFields]);

  // Toggle data source selection
  const toggleSourceSelection = (sourceId: number) => {
    setSelectedSources(prev => 
      prev.includes(sourceId) 
        ? prev.filter(id => id !== sourceId)
        : [...prev, sourceId]
    );
  };

  // Update filter
  const updateFilter = (category: string, value: string, isChecked: boolean) => {
    setActiveFilters(prev => {
      const current = prev[category] || [];
      const updated = isChecked
        ? [...current, value]
        : current.filter(v => v !== value);
      
      return {
        ...prev,
        [category]: updated
      };
    });
  };

  // Format array or string for display
  const formatValue = (value: any): string | React.ReactNode => {
    if (Array.isArray(value)) {
      return value.filter(Boolean).join(', ');
    } else if (typeof value === 'boolean') {
      return value ? 'Yes' : 'No';
    } else if (typeof value === 'number') {
      return value.toLocaleString();
    } else if (value === null || value === undefined) {
      return 'Not specified';
    }
    return String(value);
  };

  // Format age groups as a bulleted list
  const formatAgeGroups = (value: any): React.ReactNode => {
    if (!value) return 'Not specified';
    
    const groups = Array.isArray(value) 
      ? value.filter(Boolean) 
      : typeof value === 'string' 
        ? value.split(',').map(v => v.trim()).filter(Boolean)
        : [String(value)];
    
    if (groups.length === 0) return 'Not specified';
    
    return (
      <ul className="list-disc pl-5 space-y-1">
        {groups.map((group, index) => (
          <li key={index}>{group}</li>
        ))}
      </ul>
    );
  };

  // Format population numbers with comma separators
  const formatPopulation = (value: number | string): string => {
    const num = typeof value === 'number' ? value : parseFloat(value);
    if (isNaN(num)) {
      return String(value);
    }
    return num >= 1000 ? num.toLocaleString('en-US') : num.toString();
  };

  // Open detail modal for a specific source
  const openDetailModal = (source: RwdDataSource) => {
    setSelectedSource(source);
    setIsDetailModalOpen(true);
  };

  // Group fields for better organization in detail view
  const fieldGroups = {
    "Basic Information": [
      "Title", "Data source acronym", "Data holder", 
      "Data source domain", "URL LINK", "Main financial support"
    ],
    "Population": [
      "Population size", "Active population size", "Age group"
    ],
    "Geographic & Language": [
      "Data source countries", "Data source languages"
    ],
    "Time & Updates": [
      "Data source established", "First published", "Updated",
      "Data source time span (First collection)", "Data source time span (Last collection)"
    ],
    "Classification": [
      "Data source type", "Data source type, other", "Care setting", "DARWIN EU data partner?"
    ],
    "Publications & Resources": [
      "Data source publications"
    ]
  };

  // Helper function to capitalize first letter
  const capitalizeFirstLetter = (string: string): string => {
    if (!string) return string;
    return string.charAt(0).toUpperCase() + string.slice(1);
  };

  // Function to copy text to clipboard
  const copyToClipboard = (text: string, type: 'main' | 'alt') => {
    navigator.clipboard.writeText(text).then(() => {
      if (type === 'main') {
        setCopiedMainEmail(true);
        setTimeout(() => setCopiedMainEmail(false), 2000);
      } else {
        setCopiedAltEmail(true);
        setTimeout(() => setCopiedAltEmail(false), 2000);
      }
    });
  };

  // Add this function to find fields where search terms appear
  const findMatchingFields = (source: RwdDataSource, searchTerm: string): string[] => {
    if (!searchTerm) return [];
    
    const query = searchTerm.toLowerCase();
    const matchingFields: string[] = [];
    
    Object.entries(source).forEach(([key, value]) => {
      if (typeof value === 'string' && value.toLowerCase().includes(query)) {
        matchingFields.push(key);
      } else if (Array.isArray(value)) {
        if (value.some(item => typeof item === 'string' && item.toLowerCase().includes(query))) {
          matchingFields.push(key);
        }
      }
    });
    
    return matchingFields;
  };

  // Define priority order for boolean filters
  const getBooleanFilterPriority = (category: string): number => {
    // Define priority order (lower number = higher priority)
    const priorityOrder: Record<string, number> = {
      "Disease information": 1,
      "Quality of life measurements": 2,
      "Sociodemographic information": 3,
      "Lifestyle factors": 4,
      "Diagnostic codes": 5,
      "Medicinal product information": 6,  
      "Prescriptions of medicines": 7,
      "Clinical measurements": 8,
      "Procedures": 9,
      "Dispensing of medicines": 10,
      "Indication for use": 11,
      "Rare diseases": 12,
      "Pregnancy and/or neonates": 13,
      "Hospital admission and/or discharge": 14,
      "ICU admission": 15,
      "Cause of death": 16,
      "Advance therapy medicinal products (ATMP)": 17,
      "Contraception": 18,     
      "Medical devices": 19,
      "Administration of vaccines": 20,
      "Healthcare provider": 21,
      "Genetic data": 22,
      "Biomarker data": 23,
      "Patient-reported outcomes": 24,
      "Patient-generated data": 25,

      // Add other boolean filters with appropriate priority numbers
    };
    
    // Return priority if defined, otherwise a high number (low priority)
    return priorityOrder[category] || 999;
  };

  // Add this function to identify boolean filters
  const getBooleanFilters = () => {
    const booleanFilters: string[] = [];
    
    Object.entries(filterCategories).forEach(([category, values]) => {
      // Check if the filter only has Yes/No values
      if (
        (values.length === 2 && 
         values.includes('Yes') && 
         values.includes('No')) ||
        // Special case for Medicinal product information
        (category === "Medicinal product information" &&
         values.length === 2 &&
         values.includes('Captured') &&
         values.includes('Not Captured'))
      ) {
        booleanFilters.push(category);
      }
    });
    
    // Sort by priority
    return booleanFilters.sort((a, b) => 
      getBooleanFilterPriority(a) - getBooleanFilterPriority(b)
    );
  };

  // Add this function to check if a filter is selected
  const isFilterSelected = (category: string, value: string) => {
    // For Medicinal product information, map Yes/No to Captured/Not Captured
    if (category === "Medicinal product information") {
      if (value === "Yes") value = "Captured";
      if (value === "No") value = "Not Captured";
    }
    
    return (activeFilters[category] || []).includes(value);
  };

  // Add this function to toggle a boolean filter
  const toggleBooleanFilter = (category: string) => {
    const value = category === "Medicinal product information" ? "Captured" : "Yes";
    const negValue = category === "Medicinal product information" ? "Not Captured" : "No";
    
    // If "Yes"/"Captured" is already selected, remove it (uncheck)
    if (isFilterSelected(category, value)) {
      updateFilter(category, value, false);
    } 
    // Otherwise add it (check)
    else {
      updateFilter(category, value, true);
      // Also remove "No"/"Not Captured" if it was selected
      if (isFilterSelected(category, negValue)) {
        updateFilter(category, negValue, false);
      }
    }
  };

  // Add this function to check if a filter should be displayed as a checkbox with options
  const isCheckboxWithOptionsFilter = (category: string): boolean => {
    const checkboxWithOptionsFilters = [
      "Care setting", 
      "Data source countries", 
      "Data source languages", 
      "Age group", 
      "Data source type"
    ];
    
    return checkboxWithOptionsFilters.includes(category);
  };

  // Add state to track expanded filter options
  const [expandedFilters, setExpandedFilters] = useState<Record<string, boolean>>({});

  // Toggle filter expansion
  const toggleFilterExpansion = (category: string) => {
    setExpandedFilters(prev => ({
      ...prev,
      [category]: !prev[category]
    }));
  };

  // Add this function to check if a boolean filter has associated vocabulary
  const hasAssociatedVocabulary = (category: string): string | null => {
    // Map of boolean filters to their associated vocabulary fields
    const vocabularyMap: Record<string, string> = {
      "Disease information": "DISEASE INFORMATION COLLECTED",
      "Prescriptions of medicines": "PRESCRIPTIONS VOCABULARY",
      "Dispensing of medicines": "DISPENSING VOCABULARY",
      "Indication for use": "INDICATION VOCABULARY",
      "Procedures": "PROCEDURES VOCABULARY",
      "Quality of life measurements": "QUALITY OF LIFE MEASUREMENTS VOCABULARY",
      "Lifestyle factors": "LIFESTYLE FACTORS COLLECTED",
      "Sociodemographic information": "SOCIODEMOGRAPHIC INFORMATION COLLECTED",
      "Diagnostic codes": "DIAGNOSIS / MEDICAL EVENT VOCABULARY",
      "Medical devices": "MEDICAL DEVICES VOCABULARY",
      "Medicinal product information": "MEDICINAL PRODUCT VOCABULARY"
    };
    
    return vocabularyMap[category] || null;
  };

  // Add state to track expanded vocabulary sections
  const [expandedVocabularies, setExpandedVocabularies] = useState<Record<string, boolean>>({});

  // Toggle vocabulary expansion
  const toggleVocabularyExpansion = (category: string) => {
    setExpandedVocabularies(prev => ({
      ...prev,
      [category]: !prev[category]
    }));
  };

  return (
    <div className="container mx-auto py-6 px-4">
      <div className="flex justify-between items-center mb-6">
        <Link href="/">
          <Button variant="ghost" className="pl-0 hover:bg-transparent hover:text-amber-600 text-black dark:text-white dark:hover:text-white">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Evicenter
          </Button>
        </Link>
        
        {/* Theme Toggle Button */}
        <ThemeToggle />
      </div>
      
      <h1 className="text-3xl font-bold mb-4">EMA Real World Data Catalogue</h1>
      
      <div className="prose max-w-none mb-6">
        <p>
          Explore the European Medicines Agency's catalogue of real-world data sources. 
          This database contains detailed information about various healthcare data sources 
          across Europe that can be used for regulatory purposes and research.
        </p>
      </div>
      
      <Tabs defaultValue="search" className="mb-6">
        <TabsList>
          <TabsTrigger value="search">Search</TabsTrigger>
          <TabsTrigger value="filter">Filter</TabsTrigger>
          {selectedSources.length > 0 && (
            <TabsTrigger value="compare">
              Compare ({selectedSources.length})
            </TabsTrigger>
          )}
        </TabsList>
        
        <TabsContent value="search" className="mt-2">
          <div className="flex gap-2">
            <Select 
              value={searchField} 
              onValueChange={setSearchField}
            >
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Search in..." />
              </SelectTrigger>
              <SelectContent>
                {/* Common fields section */}
                <SelectGroup>
                  <SelectLabel>Common Fields</SelectLabel>
                  {getOrganizedSearchFields.common.map(field => (
                    <SelectItem key={field} value={field}>
                      {field === "all" ? "All Fields" : field}
                    </SelectItem>
                  ))}
                </SelectGroup>
                
                {/* Divider */}
                {getOrganizedSearchFields.other.length > 0 && (
                  <SelectSeparator />
                )}
                
                {/* Other fields section - collapsed by default */}
                {getOrganizedSearchFields.other.length > 0 && (
                  <SelectGroup>
                    <SelectLabel>Other Fields</SelectLabel>
                    {getOrganizedSearchFields.other.map(field => (
                      <SelectItem key={field} value={field}>
                        {field}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                )}
              </SelectContent>
            </Select>
            
            <Input
              type="search"
              placeholder="Search data sources..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1"
            />
          </div>
        </TabsContent>
        
        <TabsContent value="filter" className="mt-2">
          <div className="mb-4">
            <p className="text-sm text-muted-foreground">
              Select filters from any category below to narrow down the data sources.
            </p>
          </div>
          
          {/* Boolean filters section */}
          {getBooleanFilters().length > 0 && (
            <div className="mb-6 p-4 border rounded-md bg-amber-50/50 dark:bg-amber-950/20">
              <div className="flex justify-between items-center mb-3">
                <h3 className="font-medium">Yes/No Filters</h3>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="text-xs text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300 h-7 px-2"
                  onClick={() => {
                    // Clear all boolean filters
                    const booleanFilters = getBooleanFilters();
                    const newActiveFilters = { ...activeFilters };
                    
                    booleanFilters.forEach(category => {
                      const value = category === "Medicinal product information" ? "Captured" : "Yes";
                      const negValue = category === "Medicinal product information" ? "Not Captured" : "No";
                      
                      // Remove both Yes/No values
                      if (newActiveFilters[category]) {
                        newActiveFilters[category] = newActiveFilters[category].filter(
                          v => v !== value && v !== negValue
                        );
                        if (newActiveFilters[category].length === 0) {
                          delete newActiveFilters[category];
                        }
                      }
                      
                      // Also clear associated vocabulary filters
                      const vocabularyField = hasAssociatedVocabulary(category);
                      if (vocabularyField && newActiveFilters[vocabularyField]) {
                        delete newActiveFilters[vocabularyField];
                      }
                    });
                    
                    setActiveFilters(newActiveFilters);
                  }}
                >
                  <X className="h-3.5 w-3.5 mr-1" />
                  Clear All
                </Button>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {getBooleanFilters().map(category => {
                  const vocabularyField = hasAssociatedVocabulary(category);
                  const hasVocabulary = vocabularyField && filterCategories[vocabularyField]?.length > 0;
                  const isExpanded = expandedVocabularies[category];
                  const isSelected = isFilterSelected(category, category === "Medicinal product information" ? "Captured" : "Yes");
                  
                  return (
                    <div key={category} className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox 
                          id={`boolean-${category}`}
                          checked={isSelected}
                          onCheckedChange={() => toggleBooleanFilter(category)}
                        />
                        <label 
                          htmlFor={`boolean-${category}`}
                          className="text-sm cursor-pointer"
                        >
                          {category}
                        </label>
                        
                        {/* Indicator for filters with dropdown options */}
                        {hasVocabulary && (
                          <span className="text-xs text-amber-600 dark:text-amber-400 ml-1">
                            {isSelected ? (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="ml-auto h-6 w-6 p-0"
                                onClick={(e) => {
                                  e.preventDefault();
                                  toggleVocabularyExpansion(category);
                                }}
                              >
                                {isExpanded ? 
                                  <ChevronUp className="h-4 w-4" /> : 
                                  <ChevronDown className="h-4 w-4" />
                                }
                              </Button>
                            ) : (
                              <span className="flex items-center">
                                <ChevronDown className="h-3 w-3 ml-1 opacity-60" />
                              </span>
                            )}
                          </span>
                        )}
                      </div>
                      
                      {/* Vocabulary options */}
                      {hasVocabulary && isSelected && isExpanded && vocabularyField && (
                        <div className="ml-6 mt-2 p-2 border rounded-md bg-white/80 dark:bg-gray-800/50 text-xs">
                          <p className="font-medium mb-2 text-amber-700 dark:text-amber-400">
                            {vocabularyField === "DISEASE INFORMATION COLLECTED" 
                              ? "Disease Information Collected" 
                              : vocabularyField === "DIAGNOSIS / MEDICAL EVENT VOCABULARY"
                                ? "Diagnosis/Medical Event Vocabulary"
                                : vocabularyField.replace("VOCABULARY", "Vocabulary")}:
                          </p>
                          <div className="space-y-1 max-h-40 overflow-y-auto">
                            {filterCategories[vocabularyField]?.map(value => (
                              <div key={`${vocabularyField}-${value}`} className="flex items-center space-x-2">
                                <Checkbox 
                                  id={`${vocabularyField}-${value}`}
                                  checked={(activeFilters[vocabularyField] || []).includes(value)}
                                  onCheckedChange={(checked) => 
                                    updateFilter(vocabularyField, value, checked === true)
                                  }
                                />
                                <label 
                                  htmlFor={`${vocabularyField}-${value}`}
                                  className="text-xs cursor-pointer"
                                >
                                  {capitalizeFirstLetter(value)}
                                </label>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          )}
          
          {/* Checkbox with options filters */}
          <div className="mb-6 p-4 border rounded-md bg-blue-50/50 dark:bg-blue-950/20">
            <h3 className="font-medium mb-3">Other Common Filters</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {["Care setting", "Data source countries", "Data source languages", "Age group", "Data source type"]
                .filter(category => filterCategories[category]?.length > 0)
                .map(category => {
                  const isExpanded = expandedFilters[category];
                  const hasOptions = filterCategories[category]?.length > 0;
                  
                  return (
                    <div key={category} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <h4 className="text-sm font-medium">{category}</h4>
                          {hasOptions && (
                            <span className="text-xs text-blue-600 dark:text-blue-400 ml-1">
                              <ChevronDown className="h-3 w-3 ml-1 opacity-60" />
                            </span>
                          )}
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0"
                          onClick={() => toggleFilterExpansion(category)}
                        >
                          {isExpanded ? 
                            <ChevronUp className="h-4 w-4" /> : 
                            <ChevronDown className="h-4 w-4" />
                          }
                        </Button>
                      </div>
                      
                      {isExpanded && (
                        <div className="ml-2 p-2 border rounded-md bg-white/80 dark:bg-gray-800/50 text-xs">
                          <div className="space-y-1 max-h-40 overflow-y-auto">
                            {filterCategories[category].map(value => (
                              <div key={`${category}-${value}`} className="flex items-center space-x-2">
                                <Checkbox 
                                  id={`${category}-${value}`}
                                  checked={(activeFilters[category] || []).includes(value)}
                                  onCheckedChange={(checked) => 
                                    updateFilter(category, value, checked === true)
                                  }
                                />
                                <label 
                                  htmlFor={`${category}-${value}`}
                                  className="text-xs cursor-pointer"
                                >
                                  {capitalizeFirstLetter(value)}
                                </label>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
            </div>
          </div>
          
          {/* Active filters display */}
          {Object.keys(activeFilters).length > 0 && (
            <div className="mt-6 flex flex-wrap gap-2">
              {Object.entries(activeFilters).map(([category, values]) => 
                values.map(value => (
                  <Badge 
                    key={`${category}-${value}`} 
                    variant="outline"
                    className="flex items-center gap-1 bg-amber-50 dark:bg-amber-950/30 border-amber-200 dark:border-amber-800"
                  >
                    <span className="font-medium">{category}:</span>
                    <span>{capitalizeFirstLetter(value)}</span>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-4 w-4 p-0 ml-1 hover:bg-transparent" 
                      onClick={() => updateFilter(category, value, false)}
                    >
                      <span className="sr-only">Remove</span>
                      <span aria-hidden="true">×</span>
                    </Button>
                  </Badge>
                ))
              )}
              <Button 
                variant="ghost" 
                size="sm" 
                className="text-xs text-muted-foreground hover:text-amber-600"
                onClick={() => setActiveFilters({})}
              >
                Clear all filters
              </Button>
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="browse" className="mt-2">
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Browse by Category</h3>
            <Accordion type="single" collapsible className="w-full">
              {Object.entries(filterCategories).map(([category, values]) => (
                <AccordionItem key={category} value={category}>
                  <AccordionTrigger className="text-md font-medium">
                    {category}
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                      {values.map(value => (
                        <Button
                          key={value}
                          variant="outline"
                          size="sm"
                          className={`justify-start ${
                            (activeFilters[category] || []).includes(value) 
                              ? "bg-amber-50 dark:bg-amber-950/30 border-amber-300" 
                              : ""
                          }`}
                          onClick={() => {
                            updateFilter(
                              category, 
                              value, 
                              !(activeFilters[category] || []).includes(value)
                            );
                          }}
                        >
                          {value}
                        </Button>
                      ))}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        </TabsContent>
        
        {selectedSources.length > 0 && (
          <TabsContent value="compare" className="mt-2">
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-muted">
                    <th className="border p-2 text-left">Field</th>
                    {selectedSources.map(id => {
                      const source = dataSources.find(s => s["Data source ID"] === id);
                      return (
                        <th key={id} className="border p-2 text-left">
                          {source?.Title || `ID: ${id}`}
                        </th>
                      );
                    })}
                  </tr>
                </thead>
                <tbody>
                  {/* Group fields for better organization */}
                  {Object.entries(fieldGroups).map(([group, fields]) => (
                    <React.Fragment key={group}>
                      <tr className="bg-muted/50">
                        <td colSpan={selectedSources.length + 1} className="border p-2 font-bold">
                          {group}
                        </td>
                      </tr>
                      {fields.map(field => (
                        <tr key={field} className="border-b hover:bg-muted/30">
                          <td className="border p-2 font-medium">{field}</td>
                          {selectedSources.map(id => {
                            const source = dataSources.find(s => s["Data source ID"] === id);
                            return (
                              <td key={`${id}-${field}`} className="border p-2">
                                {source
                                  ? field === "Population size" || field === "Active population size"
                                    ? formatPopulation(source[field])
                                    : formatValue(source[field])
                                  : ''}
                              </td>
                            );
                          })}
                        </tr>
                      ))}
                    </React.Fragment>
                  ))}
                </tbody>
              </table>
            </div>
          </TabsContent>
        )}
      </Tabs>
      
      {/* Active filters display */}
      {Object.keys(activeFilters).length > 0 && (
        <div className="mb-4">
          <h3 className="text-sm font-medium mb-2">Active Filters:</h3>
          <div className="flex flex-wrap gap-2">
            {Object.entries(activeFilters).map(([category, values]) => 
              values.map(value => (
                <Badge 
                  key={`${category}-${value}`} 
                  variant="outline"
                  className="flex items-center gap-1 bg-amber-50 dark:bg-amber-950/30 border-amber-200 dark:border-amber-800"
                >
                  <span className="font-medium">{category}:</span>
                  <span>{value}</span>
                </Badge>
              ))
            )}
          </div>
        </div>
      )}
      
      {/* Results count */}
      <div className="mb-4">
        <p className="text-sm text-muted-foreground">
          Showing {filteredSources.length} of {dataSources.length} data sources
        </p>
      </div>
      
      {/* Data source cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {isLoading ? (
          // Loading skeleton
          Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-2">
                <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : filteredSources.length > 0 ? (
          filteredSources.map((source) => {
            // Find matching fields if there's a search query
            const matchingFields = searchQuery && searchField === 'all' 
              ? findMatchingFields(source, searchQuery)
              : [];
            
            return (
              <Card 
                key={source["Data source ID"]}
                className={`transition-colors duration-200 h-full relative overflow-hidden ${
                  selectedSources.includes(source["Data source ID"]) 
                    ? "shadow-md border-amber-300 dark:border-amber-700 " + 
                      (theme === 'dark' 
                        ? "dark:bg-amber-950/30" 
                        : "bg-amber-50/50")
                    : ""
                }`}
              >
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start gap-3">
                    <CardTitle className="text-lg">
                      {source.Title}
                    </CardTitle>
                    <Checkbox 
                      checked={selectedSources.includes(source["Data source ID"])}
                      onCheckedChange={() => toggleSourceSelection(source["Data source ID"])}
                      aria-label={`Select ${source.Title}`}
                    />
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    {source["Data source acronym"] && (
                      <span className="font-medium">{source["Data source acronym"]}</span>
                    )}
                    {source["DARWIN EU data partner?"] && (
                      <Badge variant="secondary" className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200">
                        DARWIN EU
                      </Badge>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {/* Data holder - with website link at the end, inline with text */}
                    <div>
                      <h3 className="text-sm font-medium text-amber-700 dark:text-amber-400">Data Holder:</h3>
                      <p className="text-sm">
                        {formatValue(source["Data holder"])}
                        {source["URL LINK"] && (
                          <a 
                            href={source["URL LINK"]} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="inline-flex ml-1.5 align-text-bottom text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                          >
                            <Globe className="h-4 w-4" />
                          </a>
                        )}
                      </p>
                    </div>
                    
                    {/* Data source countries - show text only for single country */}
                    <div>
                      <h3 className="text-sm font-medium text-amber-700 dark:text-amber-400">Countries:</h3>
                      <div className="flex items-center flex-wrap gap-1 mt-1">
                        {source["Data source countries"]
                          ? (() => {
                              const countries = source["Data source countries"].split(',').map(c => c.trim());
                              const flags = countries
                                .map((country, i) => {
                                  const code = getCountryCode(country);
                                  return code ? (
                                    <div
                                      key={country + i}
                                      className="flex-shrink-0 w-5 h-5 relative rounded-full overflow-hidden border border-gray-200 dark:border-gray-700"
                                      title={country}
                                    >
                                      <Image
                                        src={`/flags/${code.toLowerCase()}.svg`}
                                        alt={`${country} flag`}
                                        fill
                                        className="object-cover"
                                      />
                                    </div>
                                  ) : null;
                                })
                                .filter(Boolean);
                              
                              return (
                                <>
                                  {flags}
                                  {countries.length === 1 && (
                                    <span className="text-xs text-muted-foreground ml-1">
                                      {countries[0]}
                                    </span>
                                  )}
                                </>
                              );
                            })()
                          : (
                            <span className="text-xs text-muted-foreground">
                              Not specified
                            </span>
                          )}
                      </div>
                    </div>
                    
                    {/* Key metadata with formatted numbers */}
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <h3 className="text-xs font-medium text-amber-700 dark:text-amber-400">Population:</h3>
                        <p>{source["Population size"] ? formatPopulation(source["Population size"]) : "Not specified"}</p>
                      </div>
                      <div>
                        <h3 className="text-xs font-medium text-amber-700 dark:text-amber-400">Active Population:</h3>
                        <p>{source["Active population size"] ? formatPopulation(source["Active population size"]) : "Not specified"}</p>
                      </div>
                    </div>
                    
                    {/* Data source type with truncation and show more/less */}
                    <div>
                      <h3 className="text-sm font-medium text-amber-700 dark:text-amber-400">Data Source Type:</h3>
                      <div className="relative">
                        <p className="text-sm truncate" id={`type-container-${source["Data source ID"]}`}>
                          {formatValue(source["Data source type"])}
                        </p>
                        {typeof source["Data source type"] === 'string' && 
                         source["Data source type"].length > 60 && (
                          <Button
                            variant="link"
                            size="sm"
                            className="text-xs p-0 h-6 text-muted-foreground"
                            onClick={(e) => {
                              const container = document.getElementById(`type-container-${source["Data source ID"]}`);
                              if (container) {
                                if (container.classList.contains('truncate')) {
                                  container.classList.remove('truncate');
                                  container.classList.add('whitespace-normal');
                                  (e.target as HTMLElement).textContent = 'Show less';
                                } else {
                                  container.classList.add('truncate');
                                  container.classList.remove('whitespace-normal');
                                  (e.target as HTMLElement).textContent = 'Show more';
                                }
                              }
                            }}
                          >
                            Show more
                          </Button>
                        )}
                      </div>
                    </div>
                    
                    {/* Data source type, other with truncation and show more/less */}
                    {source["Data source type, other"] && (
                      <div>
                        <h3 className="text-sm font-medium text-amber-700 dark:text-amber-400">Other Data Source Types:</h3>
                        <div className="relative">
                          <p className="text-sm truncate" id={`other-type-container-${source["Data source ID"]}`}>
                            {formatValue(source["Data source type, other"])}
                          </p>
                          {typeof source["Data source type, other"] === 'string' && 
                           source["Data source type, other"].length > 60 && (
                            <Button
                              variant="link"
                              size="sm"
                              className="text-xs p-0 h-6 text-muted-foreground"
                              onClick={(e) => {
                                const container = document.getElementById(`other-type-container-${source["Data source ID"]}`);
                                if (container) {
                                  if (container.classList.contains('truncate')) {
                                    container.classList.remove('truncate');
                                    container.classList.add('whitespace-normal');
                                    (e.target as HTMLElement).textContent = 'Show less';
                                  } else {
                                    container.classList.add('truncate');
                                    container.classList.remove('whitespace-normal');
                                    (e.target as HTMLElement).textContent = 'Show more';
                                  }
                                }
                              }}
                            >
                              Show more
                            </Button>
                          )}
                        </div>
                      </div>
                    )}
                    
                    {/* Publications section - with show more functionality */}
                    {source["Data source publications"] && (
                      <div>
                        <h3 className="text-sm font-medium text-amber-700 dark:text-amber-400">Publications:</h3>
                        <div className="mt-1">
                          {typeof source["Data source publications"] === 'string' && source["Data source publications"].includes(',') ? (
                            <div className="relative">
                              <div className="flex flex-wrap gap-1 max-h-8 overflow-hidden" id={`pub-container-${source["Data source ID"]}`}>
                                {source["Data source publications"].split(',').map((url, index) => (
                                  <Button 
                                    key={index}
                                    variant="outline" 
                                    size="sm"
                                    className="text-xs"
                                    onClick={() => window.open(url.trim(), '_blank')}
                                  >
                                    <FileText className="mr-1 h-3 w-3" />
                                    Publication {index + 1}
                                  </Button>
                                ))}
                              </div>
                              {source["Data source publications"].split(',').length > 2 && (
                                <Button
                                  variant="link"
                                  size="sm"
                                  className="text-xs mt-1 p-0 h-6 text-muted-foreground"
                                  onClick={(e) => {
                                    const container = document.getElementById(`pub-container-${source["Data source ID"]}`);
                                    if (container) {
                                      const isExpanded = container.style.maxHeight !== 'none';
                                      container.style.maxHeight = isExpanded ? 'none' : '2rem';
                                      (e.target as HTMLElement).textContent = isExpanded ? 'Show less' : 'Show more';
                                    }
                                  }}
                                >
                                  Show more
                                </Button>
                              )}
                            </div>
                          ) : (
                            <Button 
                              variant="outline" 
                              size="sm"
                              className="text-xs"
                              onClick={() => window.open(source["Data source publications"], '_blank')}
                            >
                              <FileText className="mr-1 h-3 w-3" />
                              Publication 1
                            </Button>
                          )}
                        </div>
                      </div>
                    )}
                    
                    {/* View details button - moved to separate section */}
                    <div className="border-t pt-3 mt-3 space-y-2">
                      {/* Search match information - only show if searching all fields and there are matches */}
                      {searchQuery && searchField === 'all' && matchingFields.length > 0 && (
                        <div className="mb-3 text-xs">
                          <p className="font-medium text-amber-700 dark:text-amber-400 mb-1">
                            Found matches in:
                          </p>
                          <div className="flex flex-wrap gap-1">
                            {matchingFields.map(field => (
                              <Badge 
                                key={field} 
                                variant="outline" 
                                className="bg-amber-50 dark:bg-amber-950/30 text-amber-800 dark:text-amber-300 border-amber-200"
                              >
                                {field}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => openDetailModal(source)}
                        className="w-full"
                      >
                        <Info className="mr-2 h-4 w-4" />
                        Additional Details
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setContactInfoSource(source)}
                        className="w-full"
                      >
                        <MessageSquare className="mr-2 h-4 w-4" />
                        Contact Information
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })
        ) : (
          <div className="col-span-full text-center py-12">
            <p className="text-muted-foreground">No data sources match your search criteria.</p>
            <Button 
              variant="outline" 
              className="mt-4"
              onClick={() => {
                setSearchQuery('');
                setActiveFilters({});
              }}
            >
              Clear all filters
            </Button>
          </div>
        )}
      </div>
      
      {/* Selected sources floating panel */}
      {selectedSources.length > 0 && (
        <div className="fixed bottom-4 right-4 bg-background border border-border rounded-lg shadow-lg p-4 max-w-md">
          <div className="flex justify-between items-center mb-2">
            <h3 className="font-medium">Selected Data Sources ({selectedSources.length})</h3>
            <Button 
              variant="ghost" 
              size="sm" 
              className="h-8 w-8 p-0"
              onClick={() => setShowComparison(!showComparison)}
            >
              {showComparison ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
            </Button>
          </div>
          
          {showComparison && (
            <div className="space-y-2 mt-2">
              {selectedSources.map(id => {
                const source = dataSources.find(s => s["Data source ID"] === id);
                return source ? (
                  <div key={id} className="flex justify-between items-center">
                    <span className="text-sm truncate max-w-[300px]">{source.Title}</span>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-6 w-6 p-0"
                      onClick={() => toggleSourceSelection(id)}
                    >
                      <span className="sr-only">Remove</span>
                      <span aria-hidden="true">×</span>
                    </Button>
                  </div>
                ) : null;
              })}
              
              <div className="flex justify-between pt-2 border-t">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setSelectedSources([])}
                >
                  Clear All
                </Button>
                <Button 
                  variant="default" 
                  size="sm"
                  onClick={() => {
                    // Switch to compare tab
                    const tabsElement = document.querySelector('[role="tablist"]');
                    const compareTab = tabsElement?.querySelector('[value="compare"]') as HTMLButtonElement;
                    if (compareTab) {
                      compareTab.click();
                    }
                  }}
                >
                  Compare
                </Button>
              </div>
            </div>
          )}
        </div>
      )}
      
      {/* Detail Modal */}
      <Dialog open={isDetailModalOpen} onOpenChange={setIsDetailModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          {selectedSource && (
            <>
              <DialogHeader>
                <DialogTitle className="text-xl flex items-center gap-2">
                  {selectedSource.Title}
                  {selectedSource["DARWIN EU data partner?"] && (
                    <Badge variant="secondary" className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200">
                      DARWIN EU
                    </Badge>
                  )}
                </DialogTitle>
                <DialogDescription>
                  {selectedSource["Data source acronym"] && (
                    <span className="font-medium">{selectedSource["Data source acronym"]}</span>
                  )}
                  {selectedSource["Data source ID"] && (
                    <span className="ml-2">ID: {selectedSource["Data source ID"]}</span>
                  )}
                </DialogDescription>
              </DialogHeader>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                {Object.entries(fieldGroups).map(([group, fields]) => (
                  <div key={group} className="space-y-3">
                    <h3 className="font-bold text-lg border-b pb-1">{group}</h3>
                    <div className="space-y-2">
                      {fields.map(field => (
                        <div key={field} className="grid grid-cols-[1fr,1fr] gap-4">
                          <div className="font-medium text-amber-700 dark:text-amber-400">{field}:</div>
                          <div>
                            {field === "URL LINK" ? (
                              <a 
                                href={selectedSource[field]} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300 underline"
                              >
                                Visit Website <ExternalLink className="inline h-3 w-3" />
                              </a>
                            ) : field === "Data source publications" && selectedSource[field] && 
                               typeof selectedSource[field] === 'string' && 
                               selectedSource[field].includes(',') ? (
                              <div className="flex flex-wrap gap-2">
                                {selectedSource[field].split(',').map((url, index) => (
                                  <a 
                                    key={index}
                                    href={url.trim()} 
                                    target="_blank" 
                                    rel="noopener noreferrer"
                                    className="text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300 underline"
                                  >
                                    Publication {index + 1} <ExternalLink className="inline h-3 w-3" />
                                  </a>
                                ))}
                              </div>
                            ) : field === "Population size" || field === "Active population size" ? (
                              selectedSource[field] ? formatPopulation(selectedSource[field]) : "Not specified"
                            ) : field === "Age group" ? (
                              formatAgeGroups(selectedSource[field])
                            ) : (
                              formatValue(selectedSource[field])
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="flex justify-between mt-6">
                <Button 
                  variant="outline" 
                  onClick={() => setIsDetailModalOpen(false)}
                >
                  Close
                </Button>
                
                <Button
                  variant={selectedSources.includes(selectedSource["Data source ID"]) ? "destructive" : "default"}
                  onClick={() => toggleSourceSelection(selectedSource["Data source ID"])}
                >
                  {selectedSources.includes(selectedSource["Data source ID"]) ? "Remove from Comparison" : "Add to Comparison"}
                </Button>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>

      {/* Contact Information Modal */}
      <Dialog open={!!contactInfoSource} onOpenChange={(open) => !open && setContactInfoSource(null)}>
        <DialogContent className="max-w-md">
          {contactInfoSource && (
            <>
              <DialogHeader>
                <DialogTitle className="text-xl">Contact Information</DialogTitle>
                <DialogDescription>
                  {contactInfoSource["Data source acronym"] || contactInfoSource.Title}
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4 mt-4">
                {/* Main Contact */}
                <div className="space-y-2">
                  <h3 className="font-bold text-amber-700 dark:text-amber-400">Main Contact</h3>
                  <div className="space-y-1">
                    <div className="flex items-start">
                      <div className="font-medium w-20 flex-shrink-0">Name:</div>
                      <div className="text-left">
                        {contactInfoSource["Main contact: First name"] && contactInfoSource["Main contact: Last name"] 
                          ? `${contactInfoSource["Main contact: First name"]} ${contactInfoSource["Main contact: Last name"]}`
                          : "Not specified"}
                      </div>
                    </div>
                    <div className="flex items-center">
                      <div className="font-medium w-20 flex-shrink-0">Email:</div>
                      <div className="flex items-center gap-2 flex-1 min-w-0">
                        <span className="truncate">
                          {contactInfoSource["Main contact: Email"] || "Not specified"}
                        </span>
                        {contactInfoSource["Main contact: Email"] && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 flex-shrink-0 hover:bg-transparent"
                            onClick={() => copyToClipboard(contactInfoSource["Main contact: Email"], 'main')}
                          >
                            {copiedMainEmail ? (
                              <Check className="h-3 w-3 text-amber-500" />
                            ) : (
                              <Copy className="h-3 w-3 text-muted-foreground hover:text-amber-500" />
                            )}
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Alternate Contact */}
                <div className="space-y-2">
                  <h3 className="font-bold text-amber-700 dark:text-amber-400">Alternate Contact</h3>
                  <div className="space-y-1">
                    <div className="flex items-start">
                      <div className="font-medium w-20 flex-shrink-0">Name:</div>
                      <div className="text-left">
                        {contactInfoSource["Alternate contact: First name"] && contactInfoSource["Alternate contact: Last name"] 
                          ? `${contactInfoSource["Alternate contact: First name"]} ${contactInfoSource["Alternate contact: Last name"]}`
                          : "Not specified"}
                      </div>
                    </div>
                    <div className="flex items-center">
                      <div className="font-medium w-20 flex-shrink-0">Email:</div>
                      <div className="flex items-center gap-2 flex-1 min-w-0">
                        <span className="truncate">
                          {contactInfoSource["Alternate contact: Email"] || "Not specified"}
                        </span>
                        {contactInfoSource["Alternate contact: Email"] && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 flex-shrink-0 hover:bg-transparent"
                            onClick={() => copyToClipboard(contactInfoSource["Alternate contact: Email"], 'alt')}
                          >
                            {copiedAltEmail ? (
                              <Check className="h-3 w-3 text-amber-500" />
                            ) : (
                              <Copy className="h-3 w-3 text-muted-foreground hover:text-amber-500" />
                            )}
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-end mt-6">
                <Button 
                  variant="outline" 
                  onClick={() => setContactInfoSource(null)}
                >
                  Close
                </Button>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
