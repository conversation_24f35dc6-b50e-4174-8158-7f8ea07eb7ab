import { NextRequest, NextResponse } from 'next/server';
import { XMLParser } from 'fast-xml-parser';

export async function GET(request: NextRequest) {
  try {
    // Get the URL from the query parameters
    const url = request.nextUrl.searchParams.get('url');
    
    if (!url) {
      return NextResponse.json(
        { error: 'URL parameter is required' },
        { status: 400 }
      );
    }

    // Fetch the RSS feed
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Evicenter RSS Reader/1.0',
      },
      next: { revalidate: 3600 } // Cache for 1 hour
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: 'Failed to fetch RSS feed' },
        { status: response.status }
      );
    }

    // Get the XML content
    const xml = await response.text();
    
    // Parse XML to JSON with more options to handle EMA's feed format
    const parser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: "_",
      isArray: (name) => ['item'].includes(name), // Force 'item' to always be an array
      parseAttributeValue: true,
      trimValues: true,
    });
    
    const result = parser.parse(xml);
    
    // Extract the items from the feed with better error handling
    const channel = result.rss?.channel;
    
    if (!channel) {
      return NextResponse.json(
        { error: 'Invalid RSS feed format' },
        { status: 500 }
      );
    }
    
    // Ensure items is always an array
    let items = channel.item || [];
    if (!Array.isArray(items)) {
      items = [items];
    }
    
    // Format the response with proper string handling for guid
    const formattedResponse = {
      title: channel.title || '',
      description: channel.description || '',
      link: channel.link || '',
      items: items.map((item: any, index: number) => {
        // Handle guid properly - it might be an object or a string
        let guid = '';
        if (typeof item.guid === 'string') {
          guid = item.guid;
        } else if (item.guid && item.guid._text) {
          guid = item.guid._text;
        } else if (item.link) {
          guid = item.link;
        } else {
          guid = `item-${index}`;
        }
        
        return {
          title: item.title || '',
          link: item.link || '',
          pubDate: item.pubDate || '',
          description: item.description || '',
          guid: guid,
        };
      }),
    };

    return NextResponse.json(formattedResponse);
  } catch (error) {
    console.error('Error processing RSS feed:', error);
    return NextResponse.json(
      { error: 'Failed to process RSS feed', details: String(error) },
      { status: 500 }
    );
  }
}
