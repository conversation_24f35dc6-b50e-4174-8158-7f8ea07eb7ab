import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';

// Define an interface for the publication document
interface PublicationDocument {
  vendor?: string;
  [key: string]: any;
}

export async function GET(request: NextRequest) {
  try {
    // Get search parameters
    const searchParams = request.nextUrl.searchParams;
    const title = searchParams.get('title') || '';
    const abstract = searchParams.get('abstract') || '';
    const therapeuticArea = searchParams.get('therapeuticArea') || '';
    const region = searchParams.get('region') || '';
    const products = searchParams.get('products') || '';
    const studyType = searchParams.get('studyType') || '';

    // Connect to MongoDB
    const { db } = await connectToDatabase();
    
    // Build query
    const query: any = {};
    
    if (title) query.Title = new RegExp(title, 'i');
    if (abstract) query.Abstract = new RegExp(abstract, 'i');
    if (therapeuticArea) query.therapeutic_area = new RegExp(therapeuticArea, 'i');
    if (region) query.region = new RegExp(region, 'i');
    if (products) query.products = new RegExp(products, 'i');
    if (studyType) query.study_type = new RegExp(studyType, 'i');
    
    // Execute query
    const publications = await db.collection('vendor_articles').find(query).limit(100).toArray() as PublicationDocument[];
    
    // Extract unique vendor names
    const vendorNames = [...new Set(publications.map(pub => pub.vendor))];
    
    return NextResponse.json({ 
      success: true, 
      vendorNames,
      count: vendorNames.length
    });
  } catch (error) {
    console.error('Error searching publications:', error);
    return NextResponse.json(
      { error: 'Failed to search publications' },
      { status: 500 }
    );
  }
}
