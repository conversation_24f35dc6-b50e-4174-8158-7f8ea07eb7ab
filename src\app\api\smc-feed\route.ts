import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import { XMLParser } from 'fast-xml-parser';

const execAsync = promisify(exec);

export async function GET(request: NextRequest) {
  try {
    // Path to the script and output directory
    const scriptPath = path.join(process.cwd(), 'web_search_and_scraping/smc_scraper.py');
    const outputDir = path.join(process.cwd(), 'web_search_and_scraping/output');
    
    // Check if we need to refresh the feed (older than 6 hours)
    const needsRefresh = await shouldRefreshFeed(outputDir);
    
    if (needsRefresh) {
      // Run the scraper script
      await execAsync(`python ${scriptPath}`);
    }
    
    // Get the latest feed file
    const latestFeed = await getLatestFeedFile(outputDir);
    
    if (!latestFeed) {
      return NextResponse.json(
        { error: 'No SMC feed file found' },
        { status: 500 }
      );
    }
    
    // Read the XML content
    const xml = await fs.promises.readFile(latestFeed, 'utf-8');
    
    // Parse XML to JSON
    const parser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: "_",
      isArray: (name) => ['item'].includes(name),
      parseAttributeValue: true,
      trimValues: true,
    });
    
    const result = parser.parse(xml);
    
    // Extract the items from the feed
    const channel = result.rss?.channel;
    
    if (!channel) {
      return NextResponse.json(
        { error: 'Invalid RSS feed format' },
        { status: 500 }
      );
    }
    
    // Ensure items is always an array
    let items = channel.item || [];
    if (!Array.isArray(items)) {
      items = [items];
    }
    
    // Return the items in the same format as the rss-proxy endpoint
    return NextResponse.json({
      title: channel.title,
      description: channel.description,
      link: channel.link,
      items: items
    });
  } catch (error) {
    console.error('Error serving SMC feed:', error);
    return NextResponse.json(
      { error: 'Failed to serve SMC feed', details: String(error) },
      { status: 500 }
    );
  }
}

// Check if we need to refresh the feed (older than 6 hours)
async function shouldRefreshFeed(outputDir: string): Promise<boolean> {
  try {
    const files = await fs.promises.readdir(outputDir);
    const feedFiles = files.filter(file => file.startsWith('smc_updates') && file.endsWith('.xml'));
    
    if (feedFiles.length === 0) return true;
    
    // Get the latest file
    const latestFile = feedFiles.sort().pop();
    if (!latestFile) return true;
    
    const filePath = path.join(outputDir, latestFile);
    const stats = await fs.promises.stat(filePath);
    
    // Check if file is older than 6 hours
    const sixHoursAgo = new Date(Date.now() - 6 * 60 * 60 * 1000);
    return stats.mtime < sixHoursAgo;
  } catch (error) {
    console.error('Error checking feed freshness:', error);
    return true; // Refresh on error
  }
}

// Get the latest feed file
async function getLatestFeedFile(outputDir: string): Promise<string | null> {
  try {
    const files = await fs.promises.readdir(outputDir);
    const feedFiles = files.filter(file => file.startsWith('smc_updates') && file.endsWith('.xml'));
    
    if (feedFiles.length === 0) return null;
    
    // Get the latest file
    const latestFile = feedFiles.sort().pop();
    if (!latestFile) return null;
    
    return path.join(outputDir, latestFile);
  } catch (error) {
    console.error('Error getting latest feed file:', error);
    return null;
  }
}
