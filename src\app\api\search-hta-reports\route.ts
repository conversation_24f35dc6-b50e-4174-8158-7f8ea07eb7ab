import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { getPresignedUrl } from '@/lib/s3';
import OpenAI from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Define a more complete type for the search result
interface SearchResult {
  filename: string;
  text_representation: string;
  page_number: number;
  bbox: number[];
  score: number;
  presignedUrl?: string | null;
  properties?: {
    page_number: number;
    [key: string]: any;
  };
}

export async function GET(request: NextRequest) {
  try {
    // Get search query
    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get('query') || '';
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    
    if (!query) {
      return NextResponse.json({ 
        error: 'Query parameter is required' 
      }, { status: 400 });
    }

    console.log(`Generating embedding for query: "${query}"`);
    
    // Generate embedding for the query
    const embeddingResponse = await openai.embeddings.create({
      model: "text-embedding-3-large",
      input: query,
      dimensions: 3072,
    });
    
    const queryEmbedding = embeddingResponse.data[0].embedding;
    console.log(`Generated embedding with ${queryEmbedding.length} dimensions`);
    
    // Connect to MongoDB
    const { db } = await connectToDatabase();
    console.log(`Connected to MongoDB database: ${process.env.MONGODB_DB}`);
    
    // Perform vector search
    const searchResults = await db.collection('nice_reports').aggregate([
      {
        $vectorSearch: {
          index: "vector_index",
          path: "text_embeddings",
          queryVector: queryEmbedding,
          numCandidates: 100,
          limit: limit
        }
      },
      {
        $project: {
          _id: 0,
          filename: 1,
          text_representation: 1,
          page_number: "$properties.page_number", // Access page_number from properties
          bbox: 1,
          score: { $meta: "vectorSearchScore" }
        }
      }
    ]).toArray() as SearchResult[];

    console.log('Search results with page numbers:', searchResults.map(result => ({
      filename: result.filename,
      page_number: result.page_number,
      bbox: result.bbox
    })));
    
    // Generate presigned URLs for each result
    const resultsWithUrls = await Promise.all(
      searchResults.map(async (result: SearchResult) => {
        try {
          // Ensure page_number is a number
          const pageNumber = typeof result.page_number === 'number' 
            ? result.page_number 
            : parseInt(String(result.page_number), 10) || 1;
            
          const pdfFilename = `${result.filename}.pdf`;
          const presignedUrl = await getPresignedUrl(pdfFilename);
          return {
            ...result,
            page_number: pageNumber,
            presignedUrl
          };
        } catch (error) {
          console.error(`Error generating presigned URL for ${result.filename}:`, error);
          return {
            ...result,
            presignedUrl: null
          };
        }
      })
    );
    
    return NextResponse.json({ 
      success: true, 
      results: resultsWithUrls
    });
  } catch (error) {
    console.error('Search error:', error);
    return NextResponse.json({ 
      error: 'Failed to perform search',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
