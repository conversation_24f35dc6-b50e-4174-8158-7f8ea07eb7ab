"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar, ExternalLink, AlertCircle, RefreshCw } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface RssItem {
  title: string;
  link: string;
  pubDate: string;
  description: string;
  guid: string;
}

interface EmaRssFeedProps {
  url: string;
  title: string;
  description?: string;
}

export function EmaRssFeed({ url, title, description = "New information from the European Medicines Agency" }: EmaRssFeedProps) {
  const [items, setItems] = useState<RssItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchRssFeed = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // We need to use a proxy to avoid CORS issues
      const response = await fetch(`/api/rss-proxy?url=${encodeURIComponent(url)}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch RSS feed');
      }
      
      const data = await response.json();
      
      if (!data.items || !Array.isArray(data.items)) {
        throw new Error('Invalid feed format: items not found or not an array');
      }
      
      setItems(data.items);
    } catch (err) {
      console.error('Error fetching RSS feed:', err);
      setError(`Failed to load feed: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRssFeed();
  }, [url]);

  // Format date to a more readable format
  const formatDate = (dateString: string) => {
    if (!dateString) return 'No date available';
    
    try {
      const date = new Date(dateString);
      // Check if date is valid
      if (isNaN(date.getTime())) return dateString;
      
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (e) {
      return dateString;
    }
  };

  // Truncate description to a certain length
  const truncateDescription = (description: string, maxLength = 200) => {
    if (!description) return 'No description available';
    if (description.length <= maxLength) return description;
    return description.substring(0, maxLength) + '...';
  };

  // Clean HTML from description
  const cleanDescription = (html: string) => {
    if (!html) return '';
    
    try {
      // Create a temporary div to hold the HTML
      const temp = document.createElement('div');
      temp.innerHTML = html;
      // Return the text content
      return temp.textContent || temp.innerText || '';
    } catch (e) {
      return html;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">{title}</h2>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={fetchRssFeed} 
          disabled={loading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>
      
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      {loading ? (
        // Loading skeletons
        Array(3).fill(0).map((_, i) => (
          <Card key={i} className="overflow-hidden">
            <CardHeader className="pb-4">
              <div className="flex items-center text-sm text-muted-foreground mb-1">
                <Calendar className="mr-2 h-4 w-4" />
                <Skeleton className="h-4 w-24" />
              </div>
              <Skeleton className="h-6 w-full mb-2" />
              <Skeleton className="h-4 w-3/4" />
            </CardHeader>
            <CardContent className="pt-4">
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-2/3" />
            </CardContent>
          </Card>
        ))
      ) : (
        items.length > 0 ? (
          items.slice(0, 5).map((item, index) => (
            <Card key={`feed-item-${index}`} className="overflow-hidden">
              <CardHeader className="bg-amber-50 pb-4">
                <div className="flex items-center text-sm text-muted-foreground mb-1">
                  <Calendar className="mr-2 h-4 w-4" />
                  {formatDate(item.pubDate)}
                </div>
                <CardTitle className="text-lg">{item.title || 'No title'}</CardTitle>
                <CardDescription className="text-base">
                  {description}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-4">
                <p>{truncateDescription(cleanDescription(item.description))}</p>
                {item.link && (
                  <Button 
                    variant="link" 
                    className="p-0 h-auto mt-2 text-amber-600 hover:text-amber-800"
                    onClick={() => window.open(item.link, '_blank')}
                  >
                    Read more <ExternalLink className="ml-1 h-3 w-3" />
                  </Button>
                )}
              </CardContent>
            </Card>
          ))
        ) : (
          <p className="text-center text-muted-foreground py-8">
            No updates available at this time.
          </p>
        )
      )}
    </div>
  );
}
