export type NodeFunction = (input: string) => Promise<string>;

interface Node {
  fn: NodeFunction;
  next: string[];
}

export class LangGraph {
  private nodes: Record<string, Node> = {};
  private entry: string | null = null;

  addNode(name: string, fn: NodeFunction) {
    this.nodes[name] = { fn, next: [] };
  }

  addEdge(from: string, to: string) {
    if (!this.nodes[from]) {
      throw new Error(`Node '${from}' does not exist`);
    }
    this.nodes[from].next.push(to);
  }

  setEntry(name: string) {
    this.entry = name;
  }

  async run(initialInput: string): Promise<Record<string, string>> {
    if (!this.entry) {
      throw new Error('Entry node not set');
    }

    const outputs: Record<string, string> = {};
    const queue: { name: string; input: string }[] = [
      { name: this.entry, input: initialInput },
    ];

    while (queue.length > 0) {
      const { name, input } = queue.shift()!;
      const node = this.nodes[name];
      const out = await node.fn(input);
      outputs[name] = out;
      for (const next of node.next) {
        queue.push({ name: next, input: out });
      }
    }

    return outputs;
  }
}
