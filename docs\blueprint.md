# **App Name**: <PERSON>vicenter

## Core Features:

- Vendor Selection: Allow users to select multiple vendors from a list.
- Message Composer: Enable users to compose a message within the app using a text input area.
- Outlook Integration: Generate a pre-filled Outlook email with selected vendor emails and composed message. Launch the Outlook app with the new message ready to send.

## Style Guidelines:

- Primary color: Light blue (#E3F2FD) for a clean and professional look.
- Secondary color: White (#FFFFFF) for backgrounds to ensure readability.
- Accent: Teal (#008080) for interactive elements and highlights.
- Clean, card-based layout for displaying vendors.
- Use clear and recognizable icons for vendor categories and actions.

## Original User Request:
Create an app that will serve as a marketplace for users to find and contact vendors. Unlike other marketplaces, this one will allow the user to first create a message, then open the user's own Outlook (so that the website isn't hosting any sensitive information), and then paste the created message into the opened Outlook message popup. The vendor or multiple vendors previously-selected by the user will also have their corresponding email addresses pasted into the same Outlook message popup.
  