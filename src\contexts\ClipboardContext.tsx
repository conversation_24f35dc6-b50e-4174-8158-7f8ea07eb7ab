"use client";
import React, { createContext, useContext, useMemo } from "react";
import { useLocalStorage } from "@/hooks/use-local-storage";

export interface Note {
  id: string;
  text: string;
  source?: string;
  reference?: string;
  image?: string;
  caption?: string;
  color?: string;
  tags?: string[];
  editedFrom?: string;
  createdAt: number;
}

interface ClipboardContextType {
  notes: Note[];
  addNote: (
    text: string,
    source?: string,
    editedFrom?: string,
    reference?: string,
    color?: string,
    image?: string,
    caption?: string,
    tags?: string[],
  ) => void;
  editNote: (id: string, text: string, reference?: string, caption?: string, tags?: string[]) => void;
  removeNote: (id: string) => void;
  setColor: (id: string, color: string) => void;
  setTags: (id: string, tags: string[]) => void;
}

const ClipboardContext = createContext<ClipboardContextType | undefined>(undefined);

export function ClipboardProvider({
  children,
  storageKey = "drugDocsNotes",
}: {
  children: React.ReactNode;
  storageKey?: string;
}) {
  const [storedNotes, setStoredNotes] = useLocalStorage<Note[]>(storageKey, []);

  const notes = useMemo<Note[]>(
    () =>
      storedNotes.map((n) => ({
        ...n,
        tags: (n.tags ?? []).map((t: any) => (typeof t === "string" ? t : t?.label ?? "")),
      })),
    [storedNotes],
  );

  const addNote = (
    text: string,
    source?: string,
    editedFrom?: string,
    reference?: string,
    color?: string,
    image?: string,
    caption?: string,
    tags: string[] = [],
  ) => {
    const newNote: Note = {
      id: `${Date.now()}-${Math.random().toString(36).slice(2, 9)}`,
      text,
      source,
      reference,
      color,
      image,
      caption,
      tags,
      editedFrom,
      createdAt: Date.now(),
    };
    setStoredNotes([...notes, newNote]);
  };

  const editNote = (id: string, text: string, reference?: string, caption?: string, tags?: string[]) => {
    setStoredNotes(
      notes.map((n) =>
        n.id === id ? { ...n, text, reference, caption, tags: tags ?? n.tags } : n,
      ),
    );
  };

  const setColor = (id: string, color: string) => {
    setStoredNotes(notes.map((n) => (n.id === id ? { ...n, color } : n)));
  };

  const removeNote = (id: string) => {
    setStoredNotes(notes.filter((n) => n.id !== id));
  };

  const setTags = (id: string, tags: string[]) => {
    setStoredNotes(notes.map((n) => (n.id === id ? { ...n, tags } : n)));
  };

  return (
    <ClipboardContext.Provider
      value={{ notes, addNote, editNote, removeNote, setColor, setTags }}
    >
      {children}
    </ClipboardContext.Provider>
  );
}

export function useClipboard() {
  const ctx = useContext(ClipboardContext);
  if (!ctx) throw new Error("useClipboard must be within ClipboardProvider");
  return ctx;
}
