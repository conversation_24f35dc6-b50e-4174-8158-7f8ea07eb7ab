import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const drug = request.nextUrl.searchParams.get('drug');
  if (!drug) {
    return NextResponse.json({ error: 'Missing drug parameter' }, { status: 400 });
  }

  const params = new URLSearchParams({
    search: `openfda.generic_name:"${drug}"`,
    limit: '1',
    sort: 'effective_time:desc',
  });

  const url = `https://api.fda.gov/drug/label.json?${params.toString()}`;

  try {
    const res = await fetch(url);
    if (!res.ok) {
      return NextResponse.json({ error: 'Failed to fetch label' }, { status: res.status });
    }
    const data = await res.json();
    const entry = data.results?.[0];
    if (!entry) {
      return NextResponse.json({ error: 'No label found' }, { status: 404 });
    }
    const { openfda, ...rest } = entry;
    const label = { ...rest, ...(openfda || {}) };
    return NextResponse.json({ label });
  } catch (err) {
    console.error('Label fetch error', err);
    return NextResponse.json({ error: 'Failed to fetch label' }, { status: 500 });
  }
}
