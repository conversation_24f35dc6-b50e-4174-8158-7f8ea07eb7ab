export function toTitleCase(str: string): string {
  const prepositions = [
    'of', 'in', 'on', 'at', 'for', 'to', 'by', 'with', 'about', 'against',
    'between', 'into', 'through', 'during', 'before', 'after', 'above', 'below',
    'from', 'up', 'down', 'over', 'under', 'again', 'further', 'then', 'once',
    'out', 'off', 'near', 'around'
  ];
  return str
    .toLowerCase()
    .split(/\s+/)
    .map((word, idx) => {
      if (idx > 0 && prepositions.includes(word)) {
        return word;
      }
      return word.charAt(0).toUpperCase() + word.slice(1);
    })
    .join(' ');
}
