import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const doi = request.nextUrl.searchParams.get('doi');
  const title = request.nextUrl.searchParams.get('title');
  
  if (!doi && !title) {
    return NextResponse.json({ error: 'Missing doi or title parameter' }, { status: 400 });
  }
  
  try {
    const fields = 'citationCount,influentialCitationCount,fieldsOfStudy,tldr,url,openAccessPdf';
    let url: string;
    
    if (doi) {
      url = `https://api.semanticscholar.org/graph/v1/paper/DOI:${encodeURIComponent(doi)}?fields=${fields}`;
    } else {
      // Search by title as fallback
      url = `https://api.semanticscholar.org/graph/v1/paper/search?query=${encodeURIComponent(title!)}&fields=${fields}&limit=1`;
    }
    
    const res = await fetch(url);
    if (!res.ok) throw new Error('Semantic Scholar request failed');
    const data = await res.json();
    
    // Handle search results vs direct paper lookup
    const paper = title ? data.data?.[0] : data;
    
    if (!paper) {
      return NextResponse.json(
        { citationCount: null, fieldsOfStudy: [], tldr: null, url: null, openAccessPdf: null },
        { headers: { 'Cache-Control': 'no-store' } }
      );
    }
    
    return NextResponse.json(paper, { headers: { 'Cache-Control': 'no-store' } });
  } catch (err) {
    console.error('Semantic Scholar fetch error', err);
    return NextResponse.json(
      { citationCount: null, fieldsOfStudy: [], tldr: null, url: null, openAccessPdf: null },
      { headers: { 'Cache-Control': 'no-store' } }
    );
  }
}
