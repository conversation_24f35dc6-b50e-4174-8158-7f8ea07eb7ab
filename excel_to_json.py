import pandas as pd
import json

def excel_to_json(input_path: str, output_path: str):
    # Read the first sheet, using the first row as column headers
    df = pd.read_excel(input_path, header=0)
    
    # Replace NaN values with "To be updated"
    df = df.fillna("To be updated")

    # Convert DataFrame to list of dicts (one dict per row)
    records = df.to_dict(orient='records')

    # Write out to JSON with pretty‐printing
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(records, f, ensure_ascii=False, indent=4)

if __name__ == "__main__":
    # Update these paths as needed
    input_excel = "medicines_output_medicines_en_fixed.xlsx"
    output_json = "medicines_output_medicines_en_fixed.json"

    excel_to_json(input_excel, output_json)
    print(f"Converted '{input_excel}' → '{output_json}'")
