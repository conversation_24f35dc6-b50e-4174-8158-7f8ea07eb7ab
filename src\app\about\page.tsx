"use client";

import React from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from 'next/link';

export default function AboutPage() {
  return (
    <div className="container max-w-3xl mx-auto py-8 px-4">
      <Link href="/">
        <Button variant="ghost" className="mb-6 pl-0 hover:bg-transparent">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Evicenter
        </Button>
      </Link>
      
      <h1 className="text-3xl font-bold mb-6">About Evicenter</h1>
      
      <div className="prose max-w-none">
        <p className="text-lg mb-4">
          Evicenter is the premier platform connecting life sciences 
          companies with agencies specializing in health economics and outcomes research (HEOR).
        </p>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">Our Mission</h2>
        <p>
          Our mission is to streamline the agency discovery, selection, and contacting process, making it easier for life sciene companies 
          to find the right partners for HEOR projects while helping agencies showcase 
          their expertise to potential clients.
        </p>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">What We Offer</h2>
        <ul className="list-disc pl-6 space-y-2">
          <li>Database of HEOR agencies</li>
          <li>Detailed services descriptions</li>
          <li>Streamlined outreach tool</li>
          <li>Template-based outreach</li>
          <li>Agency comparison</li>
        </ul>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">Our Team</h2>
        <p>
          Evicenter was founded by a team of industry experts who recognized the need for
          a central place to connect life sciences companies with HEOR agencies. We understand the unique challenges faced by both clients and agencies in this specialized field, and we are committed to providing a solution that simplifies the process and fosters meaningful connections.
        </p>
        
        <div className="bg-amber-50 p-6 rounded-lg mt-8 border border-amber-200">
          <h3 className="text-xl font-semibold mb-2">Get in Touch</h3>
          <p>
            Have questions about Evicenter? We'd love to hear from you! Visit our 
            <Link href="/help-contact" className="text-amber-600 hover:text-amber-800 mx-1">
              Help & Contact
            </Link>
            page to reach out to our team.
          </p>
        </div>
      </div>
    </div>
  );
}