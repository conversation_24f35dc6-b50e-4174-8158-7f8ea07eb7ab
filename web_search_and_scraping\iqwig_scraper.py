#!/usr/bin/env python3
"""
IQWiG Press Releases Scraper — Updated for iqwig-search__results
Renders https://www.iqwig.de/en/presse/press-releases/…,
waits for the new .iqwig-search__results container, then
extracts every .iqwig-search__result-item into an RSS feed.
"""

import time, re, sys
from datetime import datetime, timedelta
from urllib.parse import urljoin
import xml.etree.ElementTree as ET

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class IQWiGScraper:
    def __init__(self):
        self.base_url   = "https://www.iqwig.de"
        self.target_url = (
            "https://www.iqwig.de/en/presse/press-releases/"
            "#searchQuery=query=*&page=1&rows=10"
            "&sortBy=pub_date&sortOrder=desc"
            "&facet.filter.language=en"
        )
        opts = Options()
        opts.add_argument("--headless=new")
        opts.add_argument("--no-sandbox")
        opts.add_argument("--disable-dev-shm-usage")
        self.driver = webdriver.Chrome(options=opts)

    def clean(self, txt):
        return re.sub(r"\s+", " ", txt).strip()

    def parse_date(self, s):
        # IQWiG uses DD.MM.YYYY
        try:
            date_str = s.strip().split("|")[0].strip()
            # Try the primary format first
            parsed_date = datetime.strptime(date_str, "%d.%m.%Y")
            # Add one day to fix the date discrepancy
            return parsed_date + timedelta(days=1)
        except:
            # Try alternative formats if the primary format fails
            try:
                # Try common alternative formats
                for fmt in ("%Y-%m-%d", "%d/%m/%Y", "%m/%d/%Y", "%B %d, %Y", "%b %d, %Y"):
                    try:
                        parsed_date = datetime.strptime(date_str, fmt)
                        # Add one day to fix the date discrepancy
                        return parsed_date + timedelta(days=1)
                    except:
                        continue
                
                # If all parsing attempts fail, log and return current date
                print(f"[WARN] Could not parse date: '{s}'")
                return datetime.now()
            except:
                return datetime.now()

    def scrape(self):
        self.driver.get(self.target_url)

        # 1) Wait for the new results container
        WebDriverWait(self.driver, 15).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "div.iqwig-search__results"))
        )

        # 2) Scroll and pause to allow full render
        self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(2)

        # 3) Collect every result-item
        items = self.driver.find_elements(By.CSS_SELECTOR, "div.iqwig-search__result-item")
        updates = []
        for it in items:
            try:
                # date
                date_txt = it.find_element(By.CSS_SELECTOR, ".result-item__subtitle").text
                pub_dt = self.parse_date(date_txt)
                
                # Print debug info for date parsing
                print(f"[DEBUG] Original date text: '{date_txt}', Parsed date: {pub_dt.strftime('%Y-%m-%d')}")

                # title + link
                a = it.find_element(By.CSS_SELECTOR, "a[data-auto-event-observed]")
                title = self.clean(a.find_element(By.CSS_SELECTOR, ".result-item__title").text)
                href = a.get_attribute("href").strip()
                link = urljoin(self.base_url, href)

                # excerpt
                try:
                    desc = self.clean(it.find_element(By.CSS_SELECTOR, ".result-item__content").text)
                except:
                    desc = title

                updates.append({
                    "title": title,
                    "link": link,
                    "description": desc,
                    "pub_dt": pub_dt
                })
            except Exception as e:
                print(f"[WARN] Error processing item: {e}")
                continue

        self.driver.quit()
        # sort newest first
        return sorted(updates, key=lambda u: u["pub_dt"], reverse=True)

    def create_rss(self, updates):
        rss = ET.Element("rss", version="2.0", **{"xmlns:atom":"http://www.w3.org/2005/Atom"})
        ch  = ET.SubElement(rss, "channel")
        for tag, txt in [
            ("title",       "IQWiG | Press Releases"),
            ("link",        self.target_url),
            ("description", "Press Releases from IQWiG"),
            ("language",    "en-us"),
            ("copyright",   f"© IQWiG {datetime.now():%Y}")
        ]:
            e = ET.SubElement(ch, tag); e.text = txt

        for u in updates:
            item = ET.SubElement(ch, "item")
            ET.SubElement(item, "title")      .text = u["title"]
            ET.SubElement(item, "link")       .text = u["link"]
            ET.SubElement(item, "guid")       .text = u["link"]
            ET.SubElement(item, "description").text = f"<p>{u['description']}</p>"
            # Set time to noon UTC to prevent date shift in different timezones
            pub = u["pub_dt"].strftime("%a, %d %b %Y 12:00:00 +0000")
            ET.SubElement(item, "pubDate")    .text = pub

        return rss

def main():
    fmt = "xml"
    if len(sys.argv)>1 and sys.argv[1].lower() in ("text","txt"):
        fmt = "text"

    scraper = IQWiGScraper()
    # scrape returns a list of dicts for text mode or RSS XML string for xml mode
    if fmt == "text":
        out = "\n".join(
            f"{i}. {u['pub_dt'].date()} — {u['title']}\n   {u['link']}"
            for i,u in enumerate(scraper.scrape(), start=1)
        )
    else:
        rss = scraper.create_rss(scraper.scrape())
        raw = ET.tostring(rss, "utf-8")
        from xml.dom import minidom
        pretty = minidom.parseString(raw).toprettyxml(indent="  ")
        # drop XML declaration
        out = "\n".join(pretty.splitlines()[1:])

    if out:
        print(out)
        fname = f"web_search_and_scraping/output/iqwig_press_releases.{fmt}"
        with open(fname, "w", encoding="utf-8") as f:
            if fmt == "xml":
                f.write('<?xml version="1.0" encoding="utf-8"?>\n')
            f.write(out)
        print(f"[INFO] Saved to {fname}")
    else:
        print("[ERROR] Scrape returned no data")

if __name__=="__main__":
    main()
