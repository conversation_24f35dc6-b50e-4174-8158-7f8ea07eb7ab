"use client";

import React, { useEffect, useRef, useState } from "react";
import { SubpageHeader } from "@/components/SubpageHeader";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

interface AgentNode {
  id: string;
  name: string;
  prompt: string;
  x: number;
  y: number;
  model: string;
  input?: string;
  output?: string;
  tokens?: number;
  context?: string;
  showOutput?: boolean;
}

interface Edge {
  id: string;
  source: string;
  target: string;
}

export default function WorkflowDesignerPage() {
  const [nodes, setNodes] = useState<AgentNode[]>([]);
  const [edges, setEdges] = useState<Edge[]>([]);
  const [initialInput, setInitialInput] = useState("");
  const [dragId, setDragId] = useState<string | null>(null);
  const dragOffset = useRef({ x: 0, y: 0 });

  useEffect(() => {
    const saved = localStorage.getItem('workflow_designer');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        setNodes(parsed.nodes || []);
        setEdges(parsed.edges || []);
        setInitialInput(parsed.initialInput || '');
      } catch {
        /* ignore */
      }
    } else {
      setNodes([
        {
          id: `agent-${Date.now()}`,
          name: 'Agent 1',
          prompt: '',
          model: 'gpt-4.1-mini',
          x: 50,
          y: 50,
          context: '',
          showOutput: true,
        },
      ]);
    }
  }, []);

  const startDrag = (id: string, e: React.MouseEvent) => {
    const node = nodes.find((n) => n.id === id);
    if (!node) return;
    setDragId(id);
    dragOffset.current = { x: e.clientX - node.x, y: e.clientY - node.y };
  };

  const handleMove = (e: React.MouseEvent) => {
    if (!dragId) return;
    setNodes((prev) =>
      prev.map((n) =>
        n.id === dragId
          ? { ...n, x: e.clientX - dragOffset.current.x, y: e.clientY - dragOffset.current.y }
          : n
      )
    );
  };

  const stopDrag = () => setDragId(null);

  const addNode = () => {
    setNodes((prev) => [
      ...prev,
      {
        id: `agent-${Date.now()}`,
        name: `Agent ${prev.length + 1}`,
        prompt: "",
        model: "gpt-4.1-mini",
        x: 100,
        y: 100,
      },
    ]);
  };

  const addEdge = (source: string, target: string) => {
    setEdges((prev) => [...prev, { id: `edge-${Date.now()}`, source, target }]);
  };

  const removeEdge = (source: string, target: string) => {
    setEdges((prev) => prev.filter((e) => !(e.source === source && e.target === target)));
  };

  const toggleEdge = (source: string, target: string, checked: boolean) => {
    if (checked) {
      if (!edges.find((e) => e.source === source && e.target === target)) {
        addEdge(source, target);
      }
    } else {
      removeEdge(source, target);
    }
  };

  const removeNode = (id: string) => {
    setNodes((prev) => prev.filter((n) => n.id !== id));
    setEdges((prev) => prev.filter((e) => e.source !== id && e.target !== id));
  };

  const saveWorkflow = () => {
    localStorage.setItem(
      'workflow_designer',
      JSON.stringify({ nodes, edges, initialInput })
    );
  };

  const runWorkflow = async () => {
    const res = await fetch('/api/run-workflow', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        agents: nodes.map(({ id, name, prompt, model, context }) => ({ id, name, prompt, model, context })),
        edges,
        initial_input: initialInput,
      }),
    });
    if (!res.body) return;
    const reader = res.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    setNodes((prev) => prev.map((n) => ({ ...n, input: undefined, output: '', tokens: undefined })));
    while (true) {
      const { value, done } = await reader.read();
      if (done) break;
      buffer += decoder.decode(value, { stream: true });
      let lines = buffer.split('\n');
      buffer = lines.pop() || '';
      for (const line of lines) {
        if (!line) continue;
        const msg = JSON.parse(line);
        if (msg.event === 'chunk') {
          setNodes((prev) =>
            prev.map((n) =>
              n.id === msg.id ? { ...n, output: (n.output || '') + msg.content } : n
            )
          );
        } else if (msg.event === 'done') {
          setNodes((prev) =>
            prev.map((n) =>
              n.id === msg.id
                ? { ...n, input: msg.input, tokens: msg.tokens }
                : n
            )
          );
        } else if (msg.event === 'results') {
          // ignore final
        }
      }
    }
  };

  const renderEdges = () => (
    <svg className="absolute inset-0 w-full h-full pointer-events-none">
      {edges.map((e) => {
        const from = nodes.find((n) => n.id === e.source);
        const to = nodes.find((n) => n.id === e.target);
        if (!from || !to) return null;
        return (
          <line
            key={e.id}
            x1={from.x + 72}
            y1={from.y + 40}
            x2={to.x + 72}
            y2={to.y + 40}
            stroke="black"
          />
        );
      })}
    </svg>
  );

  return (
    <div className="container mx-auto py-8">
      <SubpageHeader current="workflow-designer" />
      <h1 className="text-3xl font-bold mb-4">Workflow Designer</h1>
      <Button variant="secondary" onClick={addNode} className="mb-4">
        Add Agent
      </Button>
      <div
        className="relative border h-[600px]"
        onMouseMove={handleMove}
        onMouseUp={stopDrag}
        onMouseLeave={stopDrag}
      >
        {renderEdges()}
        {nodes.map((node) => (
          <div
            key={node.id}
            className="absolute w-40 bg-white border rounded-md shadow p-2 space-y-2 cursor-move"
            style={{ left: node.x, top: node.y }}
            onMouseDown={(e) => startDrag(node.id, e)}
          >
            <div className="flex justify-between items-center gap-1">
              <Input
                value={node.name}
                onChange={(e) =>
                  setNodes((prev) =>
                    prev.map((n) => (n.id === node.id ? { ...n, name: e.target.value } : n))
                  )
                }
                placeholder="Agent name"
                className="flex-1"
              />
              <Button size="icon" variant="outline" onClick={() => removeNode(node.id)}>
                ×
              </Button>
            </div>
            <Textarea
              value={node.prompt}
              onChange={(e) =>
                setNodes((prev) =>
                  prev.map((n) => (n.id === node.id ? { ...n, prompt: e.target.value } : n))
                )
              }
              placeholder="Prompt"
            />
            <select
              value={node.model}
              onChange={(e) =>
                setNodes((prev) =>
                  prev.map((n) => (n.id === node.id ? { ...n, model: e.target.value } : n))
                )
              }
              className="border rounded px-1 text-sm w-full"
            >
              <option value="gpt-4.1-mini">gpt-4.1-mini</option>
              <option value="gpt-4.0-mini">gpt-4.0-mini</option>
            </select>
            <Textarea
              value={node.context || ''}
              onChange={(e) =>
                setNodes((prev) =>
                  prev.map((n) =>
                    n.id === node.id ? { ...n, context: e.target.value } : n
                  )
                )
              }
              placeholder="Context to append"
            />
            {node.input && (
              <div className="text-xs space-y-1">
                <div className="font-medium">Input:</div>
                <pre className="whitespace-pre-wrap">{node.input}</pre>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() =>
                    setNodes((prev) =>
                      prev.map((n) =>
                        n.id === node.id
                          ? { ...n, showOutput: !n.showOutput }
                          : n
                      )
                    )
                  }
                >
                  {node.showOutput ? 'Hide' : 'Show'} Output
                </Button>
                {node.showOutput && (
                  <>
                    <div className="font-medium">Output ({node.tokens} tokens):</div>
                    <pre className="whitespace-pre-wrap">{node.output}</pre>
                  </>
                )}
              </div>
            )}
            <div className="text-sm space-y-1">
              {nodes
                .filter((n) => n.id !== node.id)
                .map((n) => (
                  <label key={n.id} className="flex items-center gap-1">
                    <input
                      type="checkbox"
                      checked={!!edges.find((e) => e.source === node.id && e.target === n.id)}
                      onChange={(e) => toggleEdge(node.id, n.id, e.target.checked)}
                    />
                    {n.name}
                  </label>
                ))}
            </div>
          </div>
        ))}
      </div>
      <div className="space-y-2 mt-4">
        <Textarea
          value={initialInput}
          onChange={(e) => setInitialInput(e.target.value)}
          placeholder="Initial input"
        />
        <div className="flex gap-2">
          <Button onClick={runWorkflow}>Run Workflow</Button>
          <Button variant="secondary" onClick={saveWorkflow}>Save</Button>
        </div>
      </div>
    </div>
  );
}
