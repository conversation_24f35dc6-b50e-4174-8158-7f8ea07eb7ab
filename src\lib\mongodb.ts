import { MongoClient } from 'mongodb';

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017';
const MONGODB_DB = process.env.MONGODB_DB || 'evicenter';

// CEA Publications MongoDB connection
const MONGODB_CEA_URI = process.env.MONGODB_CEA_URI || process.env.MONGODB_URI || 'mongodb://localhost:27017';
const MONGODB_CEA_DB = process.env.MONGODB_CEA_DB || 'Evicenter';

// Check if MongoDB URI is defined
if (!MONGODB_URI) {
  throw new Error('Please define the MONGODB_URI environment variable');
}

// Check if MongoDB DB is defined
if (!MONGODB_DB) {
  throw new Error('Please define the MONGODB_DB environment variable');
}

// Check if CEA MongoDB URI is defined
if (!MONGODB_CEA_URI) {
  throw new Error('Please define the MONGODB_CEA_URI environment variable');
}

// Check if CEA MongoDB DB is defined
if (!MONGODB_CEA_DB) {
  throw new Error('Please define the MONGODB_CEA_DB environment variable');
}

let cachedClient: MongoClient | null = null;
let cachedDb: any = null;

// CEA Publications connection cache
let cachedCeaClient: MongoClient | null = null;
let cachedCeaDb: any = null;

export async function connectToDatabase() {
  // If we have a cached connection, use it
  if (cachedClient && cachedDb) {
    return { client: cachedClient, db: cachedDb };
  }

  // Create a new MongoDB client
  const client = new MongoClient(MONGODB_URI);
  await client.connect();
  const db = client.db(MONGODB_DB);

  // Cache the client and db connections
  cachedClient = client;
  cachedDb = db;

  return { client, db };
}

export async function connectToCeaDatabase() {
  // If we have a cached CEA connection, use it
  if (cachedCeaClient && cachedCeaDb) {
    return { client: cachedCeaClient, db: cachedCeaDb };
  }

  // Create a new MongoDB client for CEA
  const client = new MongoClient(MONGODB_CEA_URI);
  await client.connect();
  const db = client.db(MONGODB_CEA_DB);

  // Cache the client and db connections
  cachedCeaClient = client;
  cachedCeaDb = db;

  return { client, db };
}
