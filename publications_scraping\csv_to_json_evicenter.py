import pandas as pd
import json
import ast

def parse_string_list(value):
    if pd.isna(value) or value == '' or value == '[]':
        return []
    
    value = str(value).strip()
    
    if value.startswith('[') and value.endswith(']'):
        try:
            return ast.literal_eval(value)
        except (ValueError, SyntaxError):
            pass
    
    value = value.strip('[]')
    if not value:
        return []
    
    items = [item.strip().strip('\'"') for item in value.split(',')]
    return [item for item in items if item]

# Read CSV file
df = pd.read_csv('publications_scraping/vendor_article_flags_combined.csv')  # Replace with your CSV file name

# Parse list columns
list_columns = ['therapeutic_area', 'region', 'products', 'study_type']
for col in list_columns:
    if col in df.columns:
        df[col] = df[col].apply(parse_string_list)

# Convert to JSON and save
data = df.to_dict('records')
with open('publications_scraping/vendor_article_flags_combined.json', 'w', encoding='utf-8') as f:
    json.dump(data, f, indent=2, ensure_ascii=False)

print(f"Converted {len(data)} records to vendor_article_flags_combined.json")