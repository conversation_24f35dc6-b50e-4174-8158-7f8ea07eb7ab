#!/usr/bin/env python3
"""
Scottish Medicines Consortium Website Scraper - Enhanced Version
Scrapes https://scottishmedicines.org.uk/about-us/latest-update/
and fetches detailed information from individual article pages
"""

import requests
from bs4 import BeautifulSoup
from datetime import datetime
import xml.etree.ElementTree as ET
from urllib.parse import urljoin
import re
import sys
import time

class SMCScraper:
    def __init__(self):
        self.base_url = "https://scottishmedicines.org.uk"
        self.target_url = urljoin(self.base_url, "/about-us/latest-update/")
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) '
                          'AppleWebKit/537.36 (KHTML, like Gecko) '
                          'Chrome/91.0.4472.124 Safari/537.36'
        })
        self.max_pages = 3  # max pagination depth

    def fetch_page(self, url):
        """Fetch webpage content with retry logic."""
        for attempt in range(3):
            try:
                r = self.session.get(url, timeout=15)
                r.raise_for_status()
                return r.text
            except requests.RequestException as e:
                print(f"[WARN] Attempt {attempt+1} failed for {url}: {e}")
                time.sleep(2)
        return None

    def parse_date(self, date_str):
        """Try multiple date formats, fallback to now."""
        for fmt in ("%d %B %Y", "%d %b %Y", "%Y-%m-%d", "%d/%m/%Y"):
            try:
                return datetime.strptime(date_str.strip(), fmt)
            except ValueError:
                pass
        return datetime.now()

    def clean_text(self, text):
        """Normalize whitespace and strip UI cruft."""
        if not text:
            return ""
        txt = re.sub(r'\s+', ' ', text).strip()
        # drop common nav terms or skip patterns
        for pat in [r'\b(Show All|Next|Last|Register|About us|Contact us|Home)\b',
                    r'skip to (navigation|content)',
                    r'©.*']:
            txt = re.sub(pat, "", txt, flags=re.IGNORECASE)
        return txt.strip()

    def clean_title(self, title):
        """Extra cleanup for titles (alias to clean_text)."""
        return self.clean_text(title)

    def extract_updates_targeted(self, html_content):
        """
        Grab every link under /about-us/latest-update/ (except the listing page itself),
        pull the date prefix out of the link text, and build your updates list.
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        main = soup.find('main') or soup.find(id='main') or soup.body

        updates = []
        seen = set()

        for a in main.find_all('a', href=True):
            href = a['href'].strip()
            # only article pages under latest-update, not the listing itself
            if not href.startswith('/about-us/latest-update/') or href.rstrip('/') == '/about-us/latest-update':
                continue

            full_url = urljoin(self.base_url, href)
            if full_url in seen:
                continue
            seen.add(full_url)

            raw = self.clean_text(a.get_text())
            # expect "12 May 2025 May 2025 decisions news release SMC advice…"
            m = re.match(r'^(\d{1,2}\s+\w+\s+\d{4})\s+(.*)$', raw)
            if m:
                date_str, title = m.group(1), m.group(2)
            else:
                date_str, title = '', raw

            updates.append({
                'date':        date_str,
                'title':       title,
                'link':        full_url,
                'description': title
            })

        # optionally sort newest-first:
        updates.sort(key=lambda u: self.parse_date(u['date']), reverse=True)
        return updates


    def extract_updates_fallback(self, html_content):
        """Fallback: find any element with a date + link."""
        soup = BeautifulSoup(html_content, 'html.parser')
        date_re = re.compile(r'\d{1,2}\s+\w+\s+\d{4}')
        updates = []
        seen = set()

        for tag in soup.find_all(['p','div','span','h2','h3']):
            txt = tag.get_text().strip()
            m = date_re.search(txt)
            if not m:
                continue
            date_str = m.group(0)
            # look for a link in this tag or its parent
            parent = tag.parent or tag
            a = parent.find('a', href=True)
            if a:
                title = self.clean_text(a.get_text())
                link = urljoin(self.base_url, a['href'])
                key = (date_str, title)
                if title and key not in seen:
                    seen.add(key)
                    updates.append({
                        'date': date_str,
                        'title': title,
                        'link': link,
                        'description': title
                    })
        return updates

    def create_rss_xml(self, updates):
        """Build a simple RSS feed from updates."""
        rss = ET.Element('rss', version="2.0", **{'xmlns:atom':"http://www.w3.org/2005/Atom"})
        ch = ET.SubElement(rss, 'channel')
        # channel metadata
        for tag, txt in [
            ('title', "Scottish Medicines Consortium | Latest updates"),
            ('link', self.target_url),
            ('description', "Latest updates and decisions"),
            ('language', "en"),
            ('copyright', f"© Scottish Medicines Consortium {datetime.now().year}")
        ]:
            e = ET.SubElement(ch, tag); e.text = txt

        # items
        for u in updates:
            item = ET.SubElement(ch, 'item')
            ET.SubElement(item, 'title').text = u['title']
            if u['link']:
                ET.SubElement(item, 'link').text = u['link']
                ET.SubElement(item, 'guid').text = u['link']
            ET.SubElement(item, 'description').text = f"<p>{u['description']}</p>"
            # Set time to noon UTC to prevent date shift in different timezones
            pd = self.parse_date(u['date']).strftime("%a, %d %b %Y 12:00:00 +0000")
            ET.SubElement(item, 'pubDate').text = pd

        return rss

    def format_as_text(self, updates):
        lines = [
            "SCOTTISH MEDICINES CONSORTIUM - LATEST UPDATES",
            "="*50,
            f"Scraped from: {self.target_url}",
            f"On: {datetime.now():%Y-%m-%d %H:%M:%S}",
            ""
        ]
        for i,u in enumerate(updates, start=1):
            lines += [
                f"{i}. {u['date']}",
                f"   Title: {u['title']}",
                f"   Link: {u['link'] or '[none]'}",
                ""
            ]
        return "\n".join(lines)

    def scrape(self, output_format='xml'):
        print(f"[INFO] Fetching {self.target_url}")
        html = self.fetch_page(self.target_url)
        if not html:
            print("[ERROR] Could not fetch main page")
            return None

        updates = self.extract_updates_targeted(html)
        if len(updates) < 3:
            print("[INFO] Fallback extraction")
            fb = self.extract_updates_fallback(html)
            if len(fb) > len(updates):
                updates = fb

        if not updates:
            print("[WARN] No updates found")
            return None

        # optional pagination could go here...

        if output_format.lower() == 'xml':
            rss = self.create_rss_xml(updates)
            from xml.dom import minidom
            raw = ET.tostring(rss, 'utf-8')
            pretty = minidom.parseString(raw).toprettyxml(indent="  ")
            # strip XML declaration if you like:
            return "\n".join(pretty.splitlines()[1:])
        else:
            return self.format_as_text(updates)


def main():
    scraper = SMCScraper()
    fmt = 'xml'
    if len(sys.argv) > 1 and sys.argv[1].lower() in ('txt','text'):
        fmt = 'text'

    out = scraper.scrape(fmt)
    if out:
        print(out)
        fname = f"web_search_and_scraping/output/smc_updates.{fmt}"
        with open(fname, 'w', encoding='utf-8') as f:
            if fmt == 'xml':
                f.write('<?xml version="1.0" encoding="utf-8"?>\n')
            f.write(out)
        print(f"[INFO] Saved to {fname}")
    else:
        print("[ERROR] Scrape returned no data")


if __name__ == "__main__":
    main()
