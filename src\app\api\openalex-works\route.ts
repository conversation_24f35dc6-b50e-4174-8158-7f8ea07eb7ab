import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const ids = request.nextUrl.searchParams.get('ids');
  if (!ids) {
    return NextResponse.json({ error: 'ids parameter required' }, { status: 400 });
  }
  try {
    const url = `https://api.openalex.org/works?filter=ids:${ids}&mailto=<EMAIL>`;
    const res = await fetch(url);
    if (!res.ok) throw new Error('OpenAlex request failed');
    const data = await res.json();
    return NextResponse.json(data, { headers: { 'Cache-Control': 'no-store' } });
  } catch (err) {
    console.error('OpenAlex works fetch error', err);
    return NextResponse.json({ error: 'Failed to fetch OpenAlex works' }, { status: 500 });
  }
}
