import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const id = request.nextUrl.searchParams.get('id');
  const perPage = request.nextUrl.searchParams.get('per_page') || '5';
  if (!id) {
    return NextResponse.json({ error: 'id parameter required' }, { status: 400 });
  }
  try {
    const url = `https://api.openalex.org/works/${id}/cited_by?per-page=${perPage}&mailto=<EMAIL>`;
    const res = await fetch(url);
    if (!res.ok) throw new Error('OpenAlex request failed');
    const data = await res.json();
    return NextResponse.json(data, { headers: { 'Cache-Control': 'no-store' } });
  } catch (err) {
    console.error('OpenAlex cited_by fetch error', err);
    return NextResponse.json({ error: 'Failed to fetch OpenAlex cited_by' }, { status: 500 });
  }
}
