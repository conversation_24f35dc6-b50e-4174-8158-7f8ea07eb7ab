"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogAction,
  AlertDialogCancel,
} from "@/components/ui/alert-dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ThemeToggle } from "@/components/ThemeToggle";
import { SUBPAGES } from "@/lib/subpages";
import { clearPageLocalStorage } from "@/lib/localStorageKeys";

function handleClear(current: string) {
  clearPageLocalStorage(current);
  window.location.reload();
}

interface SubpageHeaderProps {
  current: string;
}

export function SubpageHeader({ current }: SubpageHeaderProps) {
  const params = useSearchParams();
  const embedded = params.get("embedded") === "1";
  const [sidePage, setSidePage] = useState<string>("");
  const [openClicked, setOpenClicked] = useState(false);

  useEffect(() => {
    setOpenClicked(false);
  }, [sidePage]);

  return (
    <div className="flex justify-between items-center mb-6">
      {embedded ? (
        <Button
          variant="ghost"
          disabled
          className="pl-0 opacity-50 cursor-not-allowed"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Evicenter
        </Button>
      ) : (
        <Link href="/">
          <Button
            variant="ghost"
            className="pl-0 hover:bg-transparent hover:text-amber-800 dark:hover:text-amber-300"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Evicenter
          </Button>
        </Link>
      )}
      <div className="flex items-center gap-2">
        <Select value={sidePage} onValueChange={setSidePage}>
          <SelectTrigger className="w-40">
            <SelectValue placeholder="Select page" />
          </SelectTrigger>
          <SelectContent>
            {SUBPAGES.filter((p) => p.value !== current).map((p) => (
              <SelectItem key={p.value} value={p.value}>
                {p.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {sidePage ? (
          <Button
            asChild
            variant="default"
            onClick={() => setOpenClicked(true)}
            className={openClicked ? "opacity-70" : undefined}
          >
            <Link href={`/side-by-side?left=${current}&right=${sidePage}`}>
              {openClicked ? "Opening..." : "Open"}
            </Link>
          </Button>
        ) : (
          <Button variant="secondary" disabled>
            Open
          </Button>
        )}
        <ThemeToggle />
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button variant="outline">Clear Data</Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Clear saved data?</AlertDialogTitle>
              <AlertDialogDescription>
                This will remove all saved data for this page.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={() => handleClear(current)}>
                Clear
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
}

// Add "cea-publications" to the pages array
const pages = [
  { id: "pubmed-explorer", name: "PubMed Explorer", href: "/pubmed-explorer" },
  { id: "pubmed-sheets", name: "PubMed Sheets", href: "/pubmed-sheets" },
  { id: "europe-pmc-explorer", name: "Europe PMC Explorer", href: "/europe-pmc-explorer" },
  { id: "clinical-trials", name: "Clinical Trials", href: "/clinical-trials" },
  { id: "drug-docs", name: "Drug Docs", href: "/drug-docs" },
  { id: "ema-medicines-explorer", name: "EMA Medicines Explorer", href: "/ema-medicines-explorer" },
  { id: "ema-rwd-catalogue", name: "EMA RWD Catalogue", href: "/ema-rwd-catalogue" },
  { id: "cea-publications", name: "CEA Publications", href: "/cea-publications" },
];

