"use client";

import { useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter, usePathname } from "next/navigation";

export default function ProtectedRoute({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user, loading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (!loading && !user && pathname !== "/login" && pathname !== "/signup") {
      router.replace("/login");
    }
  }, [loading, user, pathname, router]);

  if (loading || (!user && pathname !== "/login" && pathname !== "/signup")) {
    return null;
  }

  return <>{children}</>;
}
