import sys

try:
    import pymupdf4llm
except Exception as e:  # pragma: no cover - runtime dependency check
    sys.stderr.write(f"pymupdf4llm not installed: {e}\n")
    sys.exit(1)

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("", end="")
    else:
        path = sys.argv[1]
        try:
            text = pymupdf4llm.to_markdown(path)
        except Exception as e:  # pragma: no cover - conversion failure
            sys.stderr.write(f"conversion error: {e}\n")
            sys.exit(1)
        print(text)
