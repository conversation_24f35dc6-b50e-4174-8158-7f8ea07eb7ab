<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg viewBox="0 0 1400 140" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#000000;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#000000;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF9900;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Main logo text -->
  <text x="30" y="80" font-family="Arial Rounded MT Bold, Verdana, Geneva, sans-serif" font-size="44" font-weight="bold" letter-spacing="0" fill="url(#textGradient)">vendor/hub</text>
  
  <!-- Thin pipe separator with more spacing -->
  <line x1="285" y1="50" x2="285" y2="90" stroke="url(#textGradient)" stroke-width="2"/>
  
  <!-- Tagline text with cursive style in lighter grey -->
  <text x="320" y="80" font-family="Brush Script MT, Brush Script Std, cursive" font-size="36" font-style="italic" font-weight="100" letter-spacing="0" fill="#999999">find and contact HEOR and market access vendors with ease</text>
</svg>