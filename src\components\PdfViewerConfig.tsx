"use client";

import React from 'react';
import { RPConfig } from '@pdf-viewer/react';
import { useTheme } from "@/contexts/ThemeContext";

export default function PdfViewerConfig({ children }: { children: React.ReactNode }) {
  const licenseKey = "eyJkYXRhIjoiZXlKMElqb2liM0puWVc1cGVtRjBhVzl1SWl3aVlYWjFJam94TnpjeU5qWTROems1TENKa2JTSTZJbXh2WTJGc2FHOXpkQ0lzSW00aU9pSmxPRFpoTnpZNU1USXhPR05pWkdJeElpd2laWGh3SWpveE56VTVNamMyTnprNUxDSmtiWFFpT2lKM2FXeGtZMkZ5WkNKOSIsInNpZ25hdHVyZSI6IlpFRjZwakFDYjRYa09Wd0E0RHEvRHdRVFE3cWVZNTJhVFVoSDViNkxxclNOczQ0Zk43OXVTZys5VDlFWUpPK0wwVzdFYzh2bklSdUlaZVNwYVRMWXY0czhnRUFEanNxVGRQVDIxcnJWWGU4ZjhOdktkcGt0aVFBL0NMcmlrR1BlUkZVM3BpTlJJMlZYd0JVMmhZbUNJMGNXMmFPOEdibUFGb1grMGwyb3JyRDhFT1BJTlNUVlVVblJlMTY3cW1KT3V2elhhOW5PV1JjVTU1blRQczJYdmpIczAzcTlHNk1iam9YQkxMcTlNQ1Ziam4xS2UxcTBIajVySG00THRsWnF3T0tPVTRXYys5dEF1MGhNWW0zdFNNaysvSnIyM3cyNEtwNWJrQWc1V0dvWGZ6eVR1VnpBV0hFbTZkNGlhMHlUUFFWSjkyVUlmZUJoaGtQKzNYaDFhQT09In0";

  // RPConfig is only for license key, not for darkMode
  return (
    <RPConfig licenseKey={licenseKey}>
      {children}
    </RPConfig>
  );
}
