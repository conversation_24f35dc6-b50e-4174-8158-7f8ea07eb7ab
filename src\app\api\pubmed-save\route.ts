import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';

export async function POST(request: NextRequest) {
  try {
    const { query, results } = await request.json();
    if (!Array.isArray(results)) {
      return NextResponse.json({ error: 'Results array required' }, { status: 400 });
    }
    const { db } = await connectToDatabase();
    const docs = results.map((r: any) => ({ ...r, query, savedAt: new Date() }));
    await db.collection('pubmed_articles').insertMany(docs);
    return NextResponse.json({ success: true, inserted: docs.length });
  } catch (error) {
    console.error('Save PubMed error', error);
    return NextResponse.json({ error: 'Failed to save results' }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const params = request.nextUrl.searchParams;
    const query = params.get('query') || '';
    const page = parseInt(params.get('page') || '1', 10);
    const pageSize = parseInt(params.get('pageSize') || '20', 10);
    const { db } = await connectToDatabase();
    const filter = query ? { query } : {};
    const docs = await db
      .collection('pubmed_articles')
      .find(filter)
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .toArray();
    const total = await db.collection('pubmed_articles').countDocuments(filter);
    return NextResponse.json({ count: total, results: docs });
  } catch (error) {
    console.error('Fetch PubMed saved error', error);
    return NextResponse.json({ error: 'Failed to fetch saved results' }, { status: 500 });
  }
}
