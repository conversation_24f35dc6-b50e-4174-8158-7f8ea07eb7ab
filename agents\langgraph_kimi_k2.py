# Context Engineering Example
# https://medium.com/data-science-collective/i-tried-langgraph-kimi-k2-context-engineering-im-really-impressed-cc1f04e7b270

import streamlit as st
import json
import re
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, TypedDict
from dataclasses import dataclass, asdict
import openai
import logging

# Add at the top after imports
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# LangGraph imports
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

# LangChain imports for RAG
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from langchain_core.messages import HumanMessage, AIMessage
from langchain_community.vectorstores import FAISS
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_community.tools import DuckDuckGoSearchRun

from langchain.globals import set_verbose
set_verbose(True)

from openai import OpenAI
import os
from dotenv import load_dotenv
load_dotenv()

moonshot_api_key = os.environ.get("MOONSHOT_API_KEY")
openai_api_key = os.environ.get("OPENAI_API_KEY")


@dataclass
class ResearchSubtask:
    id: str
    query: str
    source_type: str
    time_period: Optional[str] = None
    domain_focus: Optional[str] = None
    priority: int = 3
    start_date: Optional[str] = None
    end_date: Optional[str] = None

class ResearchState(TypedDict):
    user_query: str
    subtasks: List[ResearchSubtask]
    search_results: Dict[str, Any]
    vector_store: Optional[Any]
    final_report: str
    messages: List[Any]

class ResearchPlanner:
    def __init__(self, openai_api_key: str):
        # Moonshot/Kimi K2 client (commented out for later use)
        # self.client = openai.OpenAI(
        #     api_key=moonshot_api_key,
        #     base_url="https://api.moonshot.ai/v1"
        # )
        
        # OpenAI GPT-4 client
        self.client = openai.OpenAI(
            api_key=openai_api_key
        )
        self.embeddings = OpenAIEmbeddings(api_key=openai_api_key)
        self.search_tool = DuckDuckGoSearchRun()
        
        # Initialize LangGraph
        self.graph = self._create_graph()        
        # EXACT prompt as provided - no changes
        self.prompt_template = """You are an expert research planner. Your task is to break down a complex research query (delimited by <user_query></user_query>) into specific search subtasks, each focusing on a different aspect or source type.
        
        The current date and time is: {current_time}
        For each subtask, provide:
        1. A unique string ID for the subtask (e.g., 'subtask_1', 'news_update')
        2. A specific search query that focuses on one aspect of the main query
        3. The source type to search (web, news, academic, specialized)
        4. Time period relevance (today, last week, recent, past_year, all_time)
        5. Domain focus if applicable (technology, science, health, etc.)
        6. Priority level (1-highest to 5-lowest)
                
        All fields (id, query, source_type, time_period, domain_focus, priority) are required for each subtask, except time_period and domain_focus which can be null if not applicable.
                
        Create 2 subtasks that together will provide comprehensive coverage of the topic. Focus on different aspects, perspectives, or sources of information.
        
        IMPORTANT: Return ONLY a valid JSON array with no additional text or markdown formatting. Example format:
        [
          {{
            "id": "news_update",
            "query": "latest AI developments healthcare 2024 2025",
            "source_type": "news",
            "time_period": "recent",
            "domain_focus": "technology",
            "priority": 1
          }},
          {{
            "id": "academic_research",
            "query": "artificial intelligence medical diagnosis research papers",
            "source_type": "academic",
            "time_period": "past_year",
            "domain_focus": "science",
            "priority": 2
          }}
        ]
        
        <user_query>{user_query}</user_query>"""

    def calculate_date_range(self, time_period: str) -> tuple[Optional[str], Optional[str]]:
        """Calculate start and end dates based on time period"""
        if not time_period or time_period == "all_time":
            return None, None
        
        now = datetime.now()
        
        if time_period == "today":
            start = now.replace(hour=6, minute=0, second=0, microsecond=0)
            end = now.replace(hour=23, minute=59, second=59, microsecond=999000)
        elif time_period == "last week":
            start = now - timedelta(days=7)
            start = start.replace(hour=6, minute=0, second=0, microsecond=0)
            end = now.replace(hour=5, minute=59, second=59, microsecond=999000)
        elif time_period == "recent":
            start = now - timedelta(days=30)
            start = start.replace(hour=6, minute=0, second=0, microsecond=0)
            end = now.replace(hour=5, minute=59, second=59, microsecond=999000)
        elif time_period == "past_year":
            start = now - timedelta(days=365)
            start = start.replace(hour=6, minute=0, second=0, microsecond=0)
            end = now.replace(hour=5, minute=59, second=59, microsecond=999000)
        else:
            return None, None
        
        return start.strftime("%Y-%m-%dT%H:%M:%S.%fZ")[:-3] + "Z", end.strftime("%Y-%m-%dT%H:%M:%S.%fZ")[:-3] + "Z"

    def _create_graph(self) -> StateGraph:
        """Create LangGraph workflow"""
        workflow = StateGraph(ResearchState)
        
        workflow.add_node("plan_research", self._plan_research_node)
        workflow.add_node("execute_searches", self._execute_searches_node)
        workflow.add_node("build_rag", self._build_rag_node)
        workflow.add_node("generate_report", self._generate_report_node)
        
        workflow.set_entry_point("plan_research")
        workflow.add_edge("plan_research", "execute_searches")
        workflow.add_edge("execute_searches", "build_rag")
        workflow.add_edge("build_rag", "generate_report")
        workflow.add_edge("generate_report", END)
        
        memory = MemorySaver()
        return workflow.compile(checkpointer=memory)

    def _plan_research_node(self, state: ResearchState) -> ResearchState:
        """LangGraph node for planning research"""
        subtasks = self.generate_research_plan(state["user_query"])
        state["subtasks"] = subtasks
        state["messages"] = [HumanMessage(content=state["user_query"])]
        return state

    def _execute_searches_node(self, state: ResearchState) -> ResearchState:
        """LangGraph node for executing searches"""
        search_results = {}
        
        for subtask in state["subtasks"]:
            try:
                search_query = subtask.query
                if subtask.time_period == "today":
                    search_query += " today"
                elif subtask.time_period == "recent":
                    search_query += " 2024 2025"
                
                results = self.search_tool.run(search_query)
                search_results[subtask.id] = {
                    "subtask": asdict(subtask),
                    "results": results
                }
            except Exception as e:
                search_results[subtask.id] = {
                    "subtask": asdict(subtask),
                    "results": f"Search failed: {str(e)}"
                }
        
        state["search_results"] = search_results
        return state



    def _build_rag_node(self, state: ResearchState) -> ResearchState:
        """LangGraph node for building RAG vector store"""
        documents = []
        
        for subtask_id, result_data in state["search_results"].items():
            if isinstance(result_data["results"], str):
                doc_content = f"Subtask: {subtask_id}\n"
                doc_content += f"Query: {result_data['subtask']['query']}\n"
                doc_content += f"Results: {result_data['results']}\n"
                documents.append(doc_content)
        
        if documents:
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=1000,
                chunk_overlap=200
            )
            splits = text_splitter.create_documents(documents)
            vector_store = FAISS.from_documents(splits, self.embeddings)
            state["vector_store"] = vector_store
        
        return state

    def _generate_report_node(self, state: ResearchState) -> ResearchState:
        """LangGraph node for generating final report"""
        context = ""
        for subtask_id, result_data in state["search_results"].items():
            context += f"\n=== {subtask_id} ===\n"
            context += f"Query: {result_data['subtask']['query']}\n"
            context += f"Results: {result_data['results']}\n"
        
        report_prompt = f"""Based on the research conducted, create a comprehensive report for the query: "{state['user_query']}"

        Research Context:
        {context}

        Create a well-structured report that summarizes key findings from each research subtask and provides actionable insights.
        """
        
        response = self.client.chat.completions.create(
            model="gpt-4o",  # Changed from kimi-k2-0711-preview
            messages=[{"role": "user", "content": report_prompt}],
            temperature=0.1
        )
        
        state["final_report"] = response.choices[0].message.content
        state["messages"].append(AIMessage(content=response.choices[0].message.content))
        
        return state


    def run_langgraph_research(self, user_query: str) -> Dict[str, Any]:
        """Run complete LangGraph + RAG research pipeline"""
        initial_state = {
            "user_query": user_query,
            "subtasks": [],
            "search_results": {},
            "vector_store": None,
            "final_report": "",
            "messages": []
        }
        
        config = {"configurable": {"thread_id": "research_session"}}
        final_state = self.graph.invoke(initial_state, config)
        
        return {
            "query": user_query,
            "subtasks": [asdict(task) for task in final_state["subtasks"]],
            "search_results": final_state["search_results"],
            "report": final_state["final_report"],
            "vector_store": final_state.get("vector_store")
        }   

    def query_rag(self, query: str, vector_store, k: int = 3) -> List[str]:
        """Query the RAG vector store"""
        if vector_store:
            docs = vector_store.similarity_search(query, k=k)
            return [doc.page_content for doc in docs]
        return []

    def extract_json_from_response(self, response_text: str) -> List[Dict]:
        """Extract JSON from LLM response"""
        # Clean the response text
        response_text = response_text.strip()
        
        # Try to find JSON array in the response
        json_pattern = r'\[.*?\]'
        matches = re.findall(json_pattern, response_text, re.DOTALL)
        
        for match in matches:
            try:
                parsed = json.loads(match)
                if isinstance(parsed, list):
                    logger.info(f"Successfully parsed JSON with {len(parsed)} items")
                    return parsed
            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse JSON match: {e}")
                continue
        
        # If no JSON array found, try parsing the entire response
        try:
            parsed = json.loads(response_text)
            if isinstance(parsed, list):
                logger.info(f"Successfully parsed entire response as JSON with {len(parsed)} items")
                return parsed
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse entire response as JSON: {e}")
            logger.error(f"Response text: {response_text[:500]}...")
        
        # If all else fails, return empty list
        logger.error("Could not extract valid JSON from response")
        return []

    def generate_research_plan(self, user_query: str) -> List[ResearchSubtask]:
        """Generate research subtasks using the LLM"""
        prompt = self.prompt_template.format(
            current_time=datetime.now().isoformat(),
            user_query=user_query
        )
        
        logger.info("🤖 LLM CALL: Planning research subtasks")
        logger.info(f"Model: gpt-4o")
        logger.info(f"Prompt length: {len(prompt)} chars")
        
        response = self.client.chat.completions.create(
            model="gpt-4o",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.1
        )
        
        response_text = response.choices[0].message.content
        logger.info(f"Response length: {len(response_text)} chars")
        logger.info(f"Response preview: {response_text[:200]}...")
        
        subtasks_data = self.extract_json_from_response(response_text)
        logger.info(f"Extracted {len(subtasks_data)} subtasks")
        
        subtasks = []
        for task_data in subtasks_data:
            start_date, end_date = self.calculate_date_range(task_data.get("time_period"))
            
            subtask = ResearchSubtask(
                id=task_data.get("id", f"subtask_{len(subtasks) + 1}"),
                query=task_data.get("query", ""),
                source_type=task_data.get("source_type", "web"),
                time_period=task_data.get("time_period"),
                domain_focus=task_data.get("domain_focus"),
                priority=task_data.get("priority", 3),
                start_date=start_date,
                end_date=end_date
            )
            subtasks.append(subtask)
        
        return subtasks


# Add Streamlit interface at the end of the file
def main():
    st.title("🔬 LangGraph Research Assistant")
    st.markdown("Enter your research query and I'll break it down into subtasks, search for information, and generate a comprehensive report.")
    
    # Initialize session state
    if 'research_results' not in st.session_state:
        st.session_state.research_results = None
    
    # API key inputs
    with st.sidebar:
        st.header("Configuration")
        moonshot_key = st.text_input("Moonshot API Key", type="password", value=moonshot_api_key or "")
        openai_key = st.text_input("OpenAI API Key", type="password", value=openai_api_key or "")
        
        if not moonshot_key or not openai_key:
            st.warning("Please provide both API keys to continue.")
            return
    
    # Main interface
    user_query = st.text_area(
        "Research Query",
        placeholder="e.g., What are the latest developments in AI-powered drug discovery?",
        height=100
    )
    
    if st.button("🚀 Start Research", type="primary"):
        if not user_query.strip():
            st.error("Please enter a research query.")
            return
            
        try:
            # Initialize research planner with OpenAI key
            planner = ResearchPlanner(openai_key)  # Changed from moonshot_key
            
            # Show progress
            with st.spinner("Running research workflow..."):
                progress_bar = st.progress(0)
                status_text = st.empty()
                
                status_text.text("Planning research subtasks...")
                progress_bar.progress(25)
                
                # Run the research
                results = planner.run_langgraph_research(user_query)
                
                progress_bar.progress(100)
                status_text.text("Research complete!")
                
            st.session_state.research_results = results
            
        except Exception as e:
            st.error(f"Error during research: {str(e)}")
            return
    
    # Display results
    if st.session_state.research_results:
        results = st.session_state.research_results
        
        st.header("📊 Research Results")
        
        # Show subtasks
        st.subheader("🎯 Research Subtasks")
        for i, subtask in enumerate(results['subtasks'], 1):
            with st.expander(f"Subtask {i}: {subtask['id']}"):
                st.write(f"**Query:** {subtask['query']}")
                st.write(f"**Source Type:** {subtask['source_type']}")
                st.write(f"**Time Period:** {subtask.get('time_period', 'N/A')}")
                st.write(f"**Domain Focus:** {subtask.get('domain_focus', 'N/A')}")
                st.write(f"**Priority:** {subtask['priority']}")
        
        # Show search results
        st.subheader("🔍 Search Results")
        for subtask_id, result_data in results['search_results'].items():
            with st.expander(f"Results for {subtask_id}"):
                st.write(f"**Query:** {result_data['subtask']['query']}")
                st.text_area("Results", result_data['results'], height=200, key=f"results_{subtask_id}")
        
        # Show final report
        st.subheader("📝 Final Report")
        st.markdown(results['report'])
        
        # RAG Query Interface
        if results.get('vector_store'):
            st.subheader("💬 Ask Questions About the Research")
            rag_query = st.text_input("Ask a question about the research findings:")
            
            if st.button("Ask") and rag_query:
                planner = ResearchPlanner(moonshot_key)
                relevant_docs = planner.query_rag(rag_query, results['vector_store'])
                
                if relevant_docs:
                    st.write("**Relevant Information:**")
                    for i, doc in enumerate(relevant_docs, 1):
                        st.text_area(f"Document {i}", doc, height=100, key=f"rag_doc_{i}")


def run_terminal():
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python langgraph_kimi_k2.py 'your research query here'")
        sys.exit(1)
    
    user_query = " ".join(sys.argv[1:])
    
    # Get API key from environment
    if not openai_api_key:
        print("Error: OPENAI_API_KEY not found in environment variables")
        sys.exit(1)
    
    print(f"🔬 Starting research for: {user_query}")
    print("=" * 50)
    
    try:
        planner = ResearchPlanner(openai_api_key)
        results = planner.run_langgraph_research(user_query)
        
        print("\n📊 RESEARCH RESULTS")
        print("=" * 50)
        
        print("\n🎯 Research Subtasks:")
        for i, subtask in enumerate(results['subtasks'], 1):
            print(f"\nSubtask {i}: {subtask['id']}")
            print(f"  Query: {subtask['query']}")
            print(f"  Source: {subtask['source_type']}")
            print(f"  Time Period: {subtask.get('time_period', 'N/A')}")
        
        print("\n🔍 Search Results:")
        for subtask_id, result_data in results['search_results'].items():
            print(f"\n--- {subtask_id} ---")
            print(f"Query: {result_data['subtask']['query']}")
            print(f"Results: {result_data['results'][:500]}...")  # Truncate for readability
        
        print("\n📝 Final Report:")
        print(results['report'])
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        run_terminal()
    else:
        main()  # Run Streamlit version

# Terminal version with logging
# python agents/langgraph_kimi_k2.py "What are the latest AI developments in healthcare?"

# Streamlit version (no arguments)
# streamlit run agents/langgraph_kimi_k2.py
