import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const url = request.nextUrl.searchParams.get('url');
  
  if (!url) {
    return NextResponse.json({ error: 'Missing url parameter' }, { status: 400 });
  }
  
  try {
    console.log(`Proxying PMC PDF from: ${url}`);
    
    const res = await fetch(url, {
      headers: {
        'Accept': 'application/pdf,*/*',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://www.ncbi.nlm.nih.gov/'
      },
      redirect: 'follow',
      cache: 'no-store'
    });
    
    if (!res.ok) {
      console.error(`Failed to fetch PMC PDF: ${res.status} ${res.statusText}`);
      return NextResponse.json({ 
        error: 'Failed to fetch PMC PDF', 
        details: `Status: ${res.status} ${res.statusText}` 
      }, { status: res.status });
    }
    
    // Get the PDF data
    const buffer = await res.arrayBuffer();
    if (buffer.byteLength === 0) {
      console.error('Received empty buffer from PMC PDF URL');
      return NextResponse.json({ error: 'Received empty PDF file' }, { status: 500 });
    }
    
    const filename = url.split('/').pop() || 'pmc-document.pdf';
    
    console.log(`Successfully proxied PMC PDF: ${filename}, size: ${buffer.byteLength} bytes`);
    
    return new NextResponse(buffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `inline; filename="${filename}"`,
        'Access-Control-Allow-Origin': '*',
        'Cache-Control': 'public, max-age=3600'
      }
    });
  } catch (error) {
    console.error('Error proxying PMC PDF:', error);
    return NextResponse.json({ 
      error: 'Failed to proxy PMC PDF',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
