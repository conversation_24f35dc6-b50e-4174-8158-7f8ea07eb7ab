"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar, ExternalLink, AlertCircle, RefreshCw, ChevronDown, ChevronUp } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface RssItem {
  title: string;
  link: string;
  pubDate: string;
  description: string;
  guid: string;
}

interface RssFeedProps {
  url: string;
  title?: string;
  description?: string;
  compact?: boolean;
  maxItems?: number;
}

export function RssFeed({ 
  url, 
  title, 
  description, 
  compact = false,
  maxItems = 5
}: RssFeedProps) {
  const [items, setItems] = useState<RssItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expanded, setExpanded] = useState(false);

  const fetchRssFeed = async () => {
    try {
      setLoading(true);
      setError(null);
      
      let response;
      
      // Check if the URL is an internal API endpoint or an external URL
      if (url.startsWith('/api/') && !url.startsWith('/api/rss-proxy')) {
        // Direct API endpoint
        response = await fetch(url);
      } else {
        // External URL that needs to be proxied
        response = await fetch(`/api/rss-proxy?url=${encodeURIComponent(url)}`);
      }
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch RSS feed');
      }
      
      const data = await response.json();
      
      if (!data.items || !Array.isArray(data.items)) {
        throw new Error('Invalid feed format: items not found or not an array');
      }
      
      // Sort items by date before setting state
      setItems(sortItemsByDate(data.items));
    } catch (err) {
      console.error('Error fetching RSS feed:', err);
      setError(`Failed to load feed: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRssFeed();
  }, [url]);

  // Sort items by date (newest first)
  const sortItemsByDate = (items: RssItem[]) => {
    return [...items].sort((a, b) => {
      const dateA = new Date(a.pubDate).getTime();
      const dateB = new Date(b.pubDate).getTime();
      // Handle invalid dates by placing them at the end
      if (isNaN(dateA)) return 1;
      if (isNaN(dateB)) return -1;
      return dateB - dateA; // Sort descending (newest first)
    });
  };

  // Format date to a more readable format
  const formatDate = (dateString: string) => {
    if (!dateString) return 'No date available';
    
    try {
      const date = new Date(dateString);
      // Check if date is valid
      if (isNaN(date.getTime())) return dateString;
      
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (e) {
      return dateString;
    }
  };

  // Add a function to format dates consistently
  const formatDisplayDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      // Use UTC methods to prevent timezone shifts
      return new Date(
        Date.UTC(
          date.getUTCFullYear(),
          date.getUTCMonth(),
          date.getUTCDate()
        )
      ).toISOString().split('T')[0];
    } catch (e) {
      console.error("Error formatting date:", e);
      return dateString;
    }
  };

  // Truncate description to a certain length
  const truncateDescription = (description: string, maxLength = compact ? 100 : 200) => {
    if (!description) return 'No description available';
    if (description.length <= maxLength) return description;
    return description.substring(0, maxLength) + '...';
  };

  // Clean HTML from description
  const cleanDescription = (html: string) => {
    if (!html) return '';
    
    try {
      // Create a temporary div to hold the HTML
      const temp = document.createElement('div');
      temp.innerHTML = html;
      // Return the text content
      return temp.textContent || temp.innerText || '';
    } catch (e) {
      return html;
    }
  };

  // Determine how many items to display
  const displayItems = expanded ? items : items.slice(0, maxItems);

  if (compact) {
    return (
      <div className="space-y-3">
        {title && <h3 className="text-lg font-semibold">{title}</h3>}
        
        {error && (
          <Alert variant="destructive" className="py-2">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle className="text-sm">Error</AlertTitle>
            <AlertDescription className="text-xs">{error}</AlertDescription>
          </Alert>
        )}
        
        {loading ? (
          // Loading skeletons for compact view
          Array(3).fill(0).map((_, i) => (
            <div key={i} className="flex items-center space-x-2">
              <div className="flex-shrink-0">
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </div>
              <div className="flex-grow">
                <Skeleton className="h-4 w-full mb-1" />
                <Skeleton className="h-3 w-2/3" />
              </div>
            </div>
          ))
        ) : (
          items.length > 0 ? (
            <>
              <div className="space-y-3">
                {displayItems.map((item, index) => (
                  <div key={`feed-item-${index}`} className="border-b pb-2 last:border-0">
                    <div className="flex items-center text-xs text-muted-foreground mb-1">
                      <Calendar className="mr-1 h-3 w-3" />
                      {formatDate(item.pubDate)}
                    </div>
                    <a 
                      href={item.link} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-sm font-medium hover:underline"
                    >
                      {item.title || 'No title'}
                    </a>
                  </div>
                ))}
              </div>
              
              {items.length > maxItems && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="w-full mt-2 text-xs"
                  onClick={() => setExpanded(!expanded)}
                >
                  {expanded ? (
                    <>Show Less <ChevronUp className="ml-1 h-3 w-3" /></>
                  ) : (
                    <>Show More <ChevronDown className="ml-1 h-3 w-3" /></>
                  )}
                </Button>
              )}
            </>
          ) : (
            <p className="text-center text-muted-foreground text-sm py-4">
              No updates available at this time.
            </p>
          )
        )}
        
        <div className="flex justify-end">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={fetchRssFeed} 
            disabled={loading}
            className="text-xs p-1 h-auto"
          >
            <RefreshCw className={`h-3 w-3 mr-1 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>
    );
  }

  // Full view (non-compact)
  return (
    <div className="space-y-6">
      {title && (
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold">{title}</h2>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={fetchRssFeed} 
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      )}
      
      {description && <p className="text-muted-foreground">{description}</p>}
      
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      {loading ? (
        // Loading skeletons for full view
        Array(3).fill(0).map((_, i) => (
          <Card key={i} className="overflow-hidden">
            <CardHeader className="pb-4">
              <div className="flex items-center text-sm text-muted-foreground mb-1">
                <Calendar className="mr-2 h-4 w-4" />
                <Skeleton className="h-4 w-24" />
              </div>
              <Skeleton className="h-6 w-full mb-2" />
              <Skeleton className="h-4 w-3/4" />
            </CardHeader>
            <CardContent className="pt-4">
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-2/3" />
            </CardContent>
          </Card>
        ))
      ) : (
        items.length > 0 ? (
          <>
            <div className="space-y-4">
              {displayItems.map((item, index) => (
                <Card key={`feed-item-${index}`} className="overflow-hidden">
                  <CardHeader className="bg-slate-50 dark:bg-slate-900 pb-4">
                    <div className="flex items-center text-sm text-muted-foreground mb-1">
                      <Calendar className="mr-2 h-4 w-4" />
                      {formatDate(item.pubDate)}
                    </div>
                    <CardTitle className="text-lg">{item.title || 'No title'}</CardTitle>
                  </CardHeader>
                  <CardContent className="pt-4">
                    <p>{truncateDescription(cleanDescription(item.description))}</p>
                    {item.link && (
                      <Button 
                        variant="outline" 
                        size="sm"
                        className="mt-4"
                        onClick={() => window.open(item.link, '_blank')}
                      >
                        <ExternalLink className="mr-2 h-4 w-4" />
                        Read More
                      </Button>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
            
            {items.length > maxItems && (
              <Button 
                variant="outline" 
                className="w-full"
                onClick={() => setExpanded(!expanded)}
              >
                {expanded ? (
                  <>Show Less <ChevronUp className="ml-2 h-4 w-4" /></>
                ) : (
                  <>Show More <ChevronDown className="ml-2 h-4 w-4" /></>
                )}
              </Button>
            )}
          </>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            <p>No updates available at this time.</p>
          </div>
        )
      )}
    </div>
  );
}
