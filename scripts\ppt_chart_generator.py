import os
import sys
import base64
import re
import pandas as pd
from openai import OpenAI
import numpy as np
from typing import List, Dict, Any, Optional
import json
from datetime import datetime
import argparse
from pptx import Presentation
from pptx.chart.data import CategoryChartData, XyChartData
from pptx.enum.chart import XL_CHART_TYPE
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
from pptx.enum.chart import XL_LEGEND_POSITION
from pptx.enum.shapes import MSO_AUTO_SHAPE_TYPE

# Directory for saving PowerPoint files (provided via env)
PPTX_DIR = os.environ.get("PPTX_DIR", os.path.join(os.getcwd(), "pptx_outputs"))
os.makedirs(PPTX_DIR, exist_ok=True)

# Initialize OpenAI client using environment variable
client = OpenAI(api_key=os.environ.get('OPENAI_API_KEY'))

class PowerPointVisualizationAgent:
    def __init__(self):
        self.plotting_functions = {
            'plot_bar_chart': self.plot_bar_chart,
            'plot_horizontal_bar_chart': self.plot_horizontal_bar_chart,
            'plot_line_chart': self.plot_line_chart,
            'plot_scatter_plot': self.plot_scatter_plot,
            'plot_pie_chart': self.plot_pie_chart,
            'plot_histogram': self.plot_histogram,
            'plot_box_plot': self.plot_box_plot,
            'plot_error_bar_chart': self.plot_error_bar_chart,
            'plot_stacked_bar_chart': self.plot_stacked_bar_chart,
            'plot_grouped_bar_chart': self.plot_grouped_bar_chart,
            'plot_heatmap': self.plot_heatmap,
            'plot_violin_plot': self.plot_violin_plot
        }
        self.tools = self._create_tools()
        self.plot_counter = 0
        self.current_presentation = None
        self.last_chart_info = None

    def _parse_rgb(self, color: str) -> Optional[RGBColor]:
        if not color:
            return None
        color = color.strip().lower()
        if color.startswith("#"):
            color = color.lstrip("#")
            if len(color) == 6:
                try:
                    r = int(color[0:2], 16)
                    g = int(color[2:4], 16)
                    b = int(color[4:6], 16)
                    return RGBColor(r, g, b)
                except ValueError:
                    return None
        basic = {
            "red": (255, 0, 0),
            "green": (0, 128, 0),
            "blue": (0, 0, 255),
            "orange": (255, 165, 0),
            "purple": (128, 0, 128),
            "cyan": (0, 255, 255),
            "magenta": (255, 0, 255),
            "yellow": (255, 255, 0),
            "black": (0, 0, 0),
            "gray": (128, 128, 128),
            "grey": (128, 128, 128),
            "white": (255, 255, 255),
        }
        if color in basic:
            r, g, b = basic[color]
            return RGBColor(r, g, b)
        return None

    def _create_new_presentation(self):
        self.current_presentation = Presentation()
        if len(self.current_presentation.slides) > 0:
            slide_id = self.current_presentation.slides._sldIdLst[0]
            self.current_presentation.part.drop_rel(slide_id.rId)
            del self.current_presentation.slides._sldIdLst[0]

    def _add_slide_with_chart(
        self,
        title: str,
        chart_type: str,
        title_left: float = 0.5,
        title_top: float = 0.3,
        title_width: float = 9,
        title_height: float = 1,
        shapes: Optional[List[Dict[str, Any]]] = None,
        background_image: Optional[str] = None,
    ):
        if self.current_presentation is None:
            self._create_new_presentation()
        slide_layout = self.current_presentation.slide_layouts[5]
        slide = self.current_presentation.slides.add_slide(slide_layout)
        if background_image:
            try:
                from io import BytesIO
                img_data = base64.b64decode(background_image)
                slide.background.fill.user_picture(BytesIO(img_data))
            except Exception:
                pass
        title_shape = slide.shapes.add_textbox(
            Inches(title_left),
            Inches(title_top),
            Inches(title_width),
            Inches(title_height),
        )
        title_frame = title_shape.text_frame
        title_frame.text = title
        title_paragraph = title_frame.paragraphs[0]
        title_paragraph.alignment = PP_ALIGN.CENTER
        title_font = title_paragraph.font
        title_font.size = Pt(24)
        title_font.bold = True
        title_font.color.rgb = RGBColor(0, 51, 102)
        if shapes:
            for sh in shapes:
                s_left = Inches(sh.get("left", 0))
                s_top = Inches(sh.get("top", 0))
                s_width = Inches(sh.get("width", 1))
                s_height = Inches(sh.get("height", 1))
                rgb = self._parse_rgb(sh.get("color"))
                border_rgb = self._parse_rgb(sh.get("border_color"))
                if sh.get("type") == "text":
                    tb = slide.shapes.add_textbox(s_left, s_top, s_width, s_height)
                    frame = tb.text_frame
                    text = sh.get("text", "")
                    if sh.get("bullet"):
                        text = f"\u2022 {text}"
                    frame.text = text
                    para = frame.paragraphs[0]
                    align = str(sh.get("align", "left")).lower()
                    para.alignment = {
                        "center": PP_ALIGN.CENTER,
                        "right": PP_ALIGN.RIGHT,
                    }.get(align, PP_ALIGN.LEFT)
                    font = para.font
                    font.size = Pt(sh.get("font_size", 14))
                    font.bold = bool(sh.get("bold"))
                    if rgb:
                        font.color.rgb = rgb
                    if border_rgb:
                        tb.line.color.rgb = border_rgb
                else:
                    enum = (
                        MSO_AUTO_SHAPE_TYPE.OVAL
                        if sh.get("type") == "ellipse"
                        else MSO_AUTO_SHAPE_TYPE.RECTANGLE
                    )
                    shp = slide.shapes.add_shape(
                        enum, s_left, s_top, s_width, s_height
                    )
                    fill = shp.fill
                    fill.solid()
                    if rgb:
                        fill.fore_color.rgb = rgb
                    if border_rgb:
                        shp.line.color.rgb = border_rgb
        return slide

    def _save_presentation(self, title: str, chart_type: str):
        if self.current_presentation is None:
            return None
        self.plot_counter += 1
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        clean_title = re.sub(r'[^\w\s-]', '', title).strip()
        clean_title = re.sub(r'[-\s]+', '_', clean_title)
        filename = f"{self.plot_counter:02d}_{chart_type}_{clean_title}_{timestamp}.pptx"
        filepath = os.path.join(PPTX_DIR, filename)
        self.current_presentation.save(filepath)
        return filepath

    def plot_bar_chart(
        self,
        data: List[Dict],
        x: str,
        y: str,
        title: str = "Bar Chart",
        color: str = None,
        colors: List[str] | None = None,
        xlabel: str = None,
        ylabel: str = None,
        left: float = 1,
        top: float = 1.5,
        width: float = 8,
        height: float = 5.5,
        title_left: float = 0.5,
        title_top: float = 0.3,
        title_width: float = 9,
        title_height: float = 1,
        shapes: List[Dict[str, Any]] | None = None,
        background_image: str | None = None,
    ):
        slide = self._add_slide_with_chart(
            title,
            "bar_chart",
            title_left,
            title_top,
            title_width,
            title_height,
            shapes,
            background_image,
        )
        chart_data = CategoryChartData()
        chart_data.categories = [str(item[x]) for item in data]
        chart_data.add_series(ylabel or y, [item[y] for item in data])
        chart_frame = slide.shapes.add_chart(
            XL_CHART_TYPE.COLUMN_CLUSTERED,
            Inches(left), Inches(top), Inches(width), Inches(height),
            chart_data
        )
        chart = chart_frame.chart
        chart.has_legend = False
        rgb = self._parse_rgb(color)
        if colors:
            for idx, pt in enumerate(chart.series[0].points):
                rgb_pt = self._parse_rgb(colors[idx]) if idx < len(colors) else rgb
                if rgb_pt:
                    fill = pt.format.fill
                    fill.solid()
                    fill.fore_color.rgb = rgb_pt
        elif rgb:
            for series in chart.series:
                fill = series.format.fill
                fill.solid()
                fill.fore_color.rgb = rgb
        if chart.value_axis:
            chart.value_axis.has_title = True
            chart.value_axis.axis_title.text_frame.text = ylabel or y
        if chart.category_axis:
            chart.category_axis.has_title = True
            chart.category_axis.axis_title.text_frame.text = xlabel or x
        return self._save_presentation(title, "bar_chart")

    def plot_horizontal_bar_chart(
        self,
        data: List[Dict],
        x: str,
        y: str,
        title: str = "Horizontal Bar Chart",
        color: str = None,
        colors: List[str] | None = None,
        xlabel: str = None,
        ylabel: str = None,
        left: float = 1,
        top: float = 1.5,
        width: float = 8,
        height: float = 5.5,
        title_left: float = 0.5,
        title_top: float = 0.3,
        title_width: float = 9,
        title_height: float = 1,
        shapes: List[Dict[str, Any]] | None = None,
        background_image: str | None = None,
    ):
        slide = self._add_slide_with_chart(
            title,
            "horizontal_bar_chart",
            title_left,
            title_top,
            title_width,
            title_height,
            shapes,
            background_image,
        )
        chart_data = CategoryChartData()
        chart_data.categories = [str(item[x]) for item in data]
        chart_data.add_series(xlabel or y, [item[y] for item in data])
        chart_frame = slide.shapes.add_chart(
            XL_CHART_TYPE.BAR_CLUSTERED,
            Inches(left), Inches(top), Inches(width), Inches(height),
            chart_data
        )
        chart = chart_frame.chart
        chart.has_legend = False
        rgb = self._parse_rgb(color)
        if colors:
            for idx, pt in enumerate(chart.series[0].points):
                rgb_pt = self._parse_rgb(colors[idx]) if idx < len(colors) else rgb
                if rgb_pt:
                    fill = pt.format.fill
                    fill.solid()
                    fill.fore_color.rgb = rgb_pt
        elif rgb:
            for series in chart.series:
                fill = series.format.fill
                fill.solid()
                fill.fore_color.rgb = rgb
        return self._save_presentation(title, "horizontal_bar_chart")

    def plot_line_chart(
        self,
        data: List[Dict],
        x: str,
        y: str,
        title: str = "Line Chart",
        color: str = None,
        colors: List[str] | None = None,
        xlabel: str = None,
        ylabel: str = None,
        group_by: str = None,
        left: float = 1,
        top: float = 1.5,
        width: float = 8,
        height: float = 5.5,
        title_left: float = 0.5,
        title_top: float = 0.3,
        title_width: float = 9,
        title_height: float = 1,
        shapes: List[Dict[str, Any]] | None = None,
        background_image: str | None = None,
    ):
        slide = self._add_slide_with_chart(
            title,
            "line_chart",
            title_left,
            title_top,
            title_width,
            title_height,
            shapes,
            background_image,
        )
        if group_by and group_by in data[0]:
            df = pd.DataFrame(data)
            chart_data = CategoryChartData()
            chart_data.categories = [str(val) for val in df[x].unique()]
            for group in df[group_by].unique():
                group_data = df[df[group_by] == group].sort_values(x)
                series_values = []
                for cat in chart_data.categories:
                    matching_rows = group_data[group_data[x].astype(str) == cat]
                    if len(matching_rows) > 0:
                        series_values.append(matching_rows[y].iloc[0])
                    else:
                        series_values.append(0)
                chart_data.add_series(str(group), series_values)
        else:
            chart_data = CategoryChartData()
            chart_data.categories = [str(item[x]) for item in data]
            chart_data.add_series(ylabel or y, [item[y] for item in data])
        chart_frame = slide.shapes.add_chart(
            XL_CHART_TYPE.LINE,
            Inches(left), Inches(top), Inches(width), Inches(height),
            chart_data
        )
        chart = chart_frame.chart
        if group_by:
            chart.has_legend = True
            chart.legend.position = XL_LEGEND_POSITION.RIGHT
        else:
            chart.has_legend = False
        rgb = self._parse_rgb(color)
        if colors:
            for idx, series in enumerate(chart.series):
                rgb_series = self._parse_rgb(colors[idx]) if idx < len(colors) else rgb
                if rgb_series:
                    line = series.format.line
                    line.color.rgb = rgb_series
        elif rgb:
            for series in chart.series:
                line = series.format.line
                line.color.rgb = rgb
        return self._save_presentation(title, "line_chart")

    def plot_scatter_plot(
        self,
        data: List[Dict],
        x: str,
        y: str,
        title: str = "Scatter Plot",
        color: str = None,
        colors: List[str] | None = None,
        xlabel: str = None,
        ylabel: str = None,
        size_by: str = None,
        color_by: str = None,
        left: float = 1,
        top: float = 1.5,
        width: float = 8,
        height: float = 5.5,
        title_left: float = 0.5,
        title_top: float = 0.3,
        title_width: float = 9,
        title_height: float = 1,
        shapes: List[Dict[str, Any]] | None = None,
        background_image: str | None = None,
    ):
        slide = self._add_slide_with_chart(
            title,
            "scatter_plot",
            title_left,
            title_top,
            title_width,
            title_height,
            shapes,
            background_image,
        )
        chart_data = XyChartData()
        series = chart_data.add_series(ylabel or y)
        for item in data:
            series.add_data_point(item[x], item[y])
        chart_frame = slide.shapes.add_chart(
            XL_CHART_TYPE.XY_SCATTER,
            Inches(left), Inches(top), Inches(width), Inches(height),
            chart_data
        )
        chart = chart_frame.chart
        chart.has_legend = False
        rgb = self._parse_rgb(color)
        if colors:
            for series in chart.series:
                for idx, pt in enumerate(series.points):
                    rgb_pt = self._parse_rgb(colors[idx]) if idx < len(colors) else rgb
                    if rgb_pt:
                        fill = pt.format.fill
                        fill.solid()
                        fill.fore_color.rgb = rgb_pt
        elif rgb:
            for series in chart.series:
                for pt in series.points:
                    fill = pt.format.fill
                    fill.solid()
                    fill.fore_color.rgb = rgb
        return self._save_presentation(title, "scatter_plot")

    def plot_pie_chart(
        self,
        data: List[Dict],
        category: str,
        value: str,
        title: str = "Pie Chart",
        color: str = None,
        colors: List[str] | None = None,
        left: float = 2,
        top: float = 1.5,
        width: float = 6,
        height: float = 5.5,
        title_left: float = 0.5,
        title_top: float = 0.3,
        title_width: float = 9,
        title_height: float = 1,
        shapes: List[Dict[str, Any]] | None = None,
        background_image: str | None = None,
    ):
        slide = self._add_slide_with_chart(
            title,
            "pie_chart",
            title_left,
            title_top,
            title_width,
            title_height,
            shapes,
            background_image,
        )
        chart_data = CategoryChartData()
        chart_data.categories = [str(item[category]) for item in data]
        chart_data.add_series('Values', [item[value] for item in data])
        chart_frame = slide.shapes.add_chart(
            XL_CHART_TYPE.PIE,
            Inches(left), Inches(top), Inches(width), Inches(height),
            chart_data
        )
        chart = chart_frame.chart
        chart.has_legend = True
        chart.legend.position = XL_LEGEND_POSITION.RIGHT
        rgb = self._parse_rgb(color)
        if colors:
            for idx, pt in enumerate(chart.series[0].points):
                rgb_pt = self._parse_rgb(colors[idx]) if idx < len(colors) else rgb
                if rgb_pt:
                    fill = pt.format.fill
                    fill.solid()
                    fill.fore_color.rgb = rgb_pt
        elif rgb:
            for series in chart.series:
                for pt in series.points:
                    fill = pt.format.fill
                    fill.solid()
                    fill.fore_color.rgb = rgb
        return self._save_presentation(title, "pie_chart")

    def plot_histogram(
        self,
        data: List[float],
        bins: int = 20,
        title: str = "Histogram",
        xlabel: str = "Value",
        ylabel: str = "Frequency",
        color: str = None,
        colors: List[str] | None = None,
        left: float = 1,
        top: float = 1.5,
        width: float = 8,
        height: float = 5.5,
        title_left: float = 0.5,
        title_top: float = 0.3,
        title_width: float = 9,
        title_height: float = 1,
        shapes: List[Dict[str, Any]] | None = None,
        background_image: str | None = None,
    ):
        slide = self._add_slide_with_chart(
            title,
            "histogram",
            title_left,
            title_top,
            title_width,
            title_height,
            shapes,
            background_image,
        )
        hist_data, bin_edges = np.histogram(data, bins=bins)
        bin_labels = [f"{bin_edges[i]:.1f}-{bin_edges[i+1]:.1f}" for i in range(len(hist_data))]
        chart_data = CategoryChartData()
        chart_data.categories = bin_labels
        chart_data.add_series(ylabel, hist_data.tolist())
        chart_frame = slide.shapes.add_chart(
            XL_CHART_TYPE.COLUMN_CLUSTERED,
            Inches(left), Inches(top), Inches(width), Inches(height),
            chart_data
        )
        chart = chart_frame.chart
        chart.has_legend = False
        rgb = self._parse_rgb(color)
        if colors:
            for idx, pt in enumerate(chart.series[0].points):
                rgb_pt = self._parse_rgb(colors[idx]) if idx < len(colors) else rgb
                if rgb_pt:
                    fill = pt.format.fill
                    fill.solid()
                    fill.fore_color.rgb = rgb_pt
        elif rgb:
            for series in chart.series:
                fill = series.format.fill
                fill.solid()
                fill.fore_color.rgb = rgb
        return self._save_presentation(title, "histogram")

    def plot_box_plot(
        self,
        data: List[Dict],
        category: str,
        values: str,
        title: str = "Box Plot",
        left: float = 1,
        top: float = 1.5,
        width: float = 8,
        height: float = 5.5,
        title_left: float = 0.5,
        title_top: float = 0.3,
        title_width: float = 9,
        title_height: float = 1,
        shapes: List[Dict[str, Any]] | None = None,
        background_image: str | None = None,
    ):
        slide = self._add_slide_with_chart(
            title,
            "box_plot",
            title_left,
            title_top,
            title_width,
            title_height,
            shapes,
            background_image,
        )
        df = pd.DataFrame(data)
        categories = df[category].unique()
        medians = []
        q1s = []
        q3s = []
        for cat in categories:
            cat_data = df[df[category] == cat][values]
            medians.append(cat_data.median())
            q1s.append(cat_data.quantile(0.25))
            q3s.append(cat_data.quantile(0.75))
        chart_data = CategoryChartData()
        chart_data.categories = [str(cat) for cat in categories]
        chart_data.add_series('Median', medians)
        chart_frame = slide.shapes.add_chart(
            XL_CHART_TYPE.LINE_MARKERS,
            Inches(left), Inches(top), Inches(width), Inches(height),
            chart_data
        )
        chart = chart_frame.chart
        chart.has_legend = False
        return self._save_presentation(title, "box_plot")

    def plot_error_bar_chart(self, data: List[Dict], x: str, y: str, y_lower: str, y_upper: str,
                            title: str = "Error Bar Chart", xlabel: str = None, ylabel: str = None,
                            left: float = 1, top: float = 1.5, width: float = 8, height: float = 5.5,
                            title_left: float = 0.5, title_top: float = 0.3, title_width: float = 9,
                            title_height: float = 1, shapes: List[Dict[str, Any]] | None = None,
                            background_image: str | None = None):
        slide = self._add_slide_with_chart(
            title,
            "error_bar_chart",
            title_left,
            title_top,
            title_width,
            title_height,
            shapes,
            background_image,
        )
        chart_data = CategoryChartData()
        chart_data.categories = [str(item[x]) for item in data]
        chart_data.add_series(ylabel or y, [item[y] for item in data])
        chart_data.add_series('Lower Bound', [item[y_lower] for item in data])
        chart_data.add_series('Upper Bound', [item[y_upper] for item in data])
        chart_frame = slide.shapes.add_chart(
            XL_CHART_TYPE.LINE_MARKERS,
            Inches(left), Inches(top), Inches(width), Inches(height),
            chart_data
        )
        chart = chart_frame.chart
        chart.has_legend = True
        chart.legend.position = XL_LEGEND_POSITION.RIGHT
        return self._save_presentation(title, "error_bar_chart")

    def plot_stacked_bar_chart(self, data: List[Dict], x: str, categories: List[str],
                              title: str = "Stacked Bar Chart", xlabel: str = None, ylabel: str = "Value",
                              left: float = 1, top: float = 1.5, width: float = 8, height: float = 5.5,
                              title_left: float = 0.5, title_top: float = 0.3, title_width: float = 9,
                              title_height: float = 1, shapes: List[Dict[str, Any]] | None = None,
                              background_image: str | None = None):
        slide = self._add_slide_with_chart(
            title,
            "stacked_bar_chart",
            title_left,
            title_top,
            title_width,
            title_height,
            shapes,
            background_image,
        )
        chart_data = CategoryChartData()
        chart_data.categories = [str(item[x]) for item in data]
        for category in categories:
            if all(category in item for item in data):
                chart_data.add_series(category, [item[category] for item in data])
        chart_frame = slide.shapes.add_chart(
            XL_CHART_TYPE.COLUMN_STACKED,
            Inches(left), Inches(top), Inches(width), Inches(height),
            chart_data
        )
        chart = chart_frame.chart
        chart.has_legend = True
        chart.legend.position = XL_LEGEND_POSITION.RIGHT
        return self._save_presentation(title, "stacked_bar_chart")

    def plot_grouped_bar_chart(self, data: List[Dict], x: str, categories: List[str],
                              title: str = "Grouped Bar Chart", xlabel: str = None, ylabel: str = "Value",
                              left: float = 1, top: float = 1.5, width: float = 8, height: float = 5.5,
                              title_left: float = 0.5, title_top: float = 0.3, title_width: float = 9,
                              title_height: float = 1, shapes: List[Dict[str, Any]] | None = None,
                              background_image: str | None = None):
        slide = self._add_slide_with_chart(
            title,
            "grouped_bar_chart",
            title_left,
            title_top,
            title_width,
            title_height,
            shapes,
            background_image,
        )
        chart_data = CategoryChartData()
        chart_data.categories = [str(item[x]) for item in data]
        for category in categories:
            if all(category in item for item in data):
                chart_data.add_series(category, [item[category] for item in data])
        chart_frame = slide.shapes.add_chart(
            XL_CHART_TYPE.COLUMN_CLUSTERED,
            Inches(left), Inches(top), Inches(width), Inches(height),
            chart_data
        )
        chart = chart_frame.chart
        chart.has_legend = True
        chart.legend.position = XL_LEGEND_POSITION.RIGHT
        return self._save_presentation(title, "grouped_bar_chart")

    def plot_heatmap(self, data: List[Dict], x: str, y: str, value: str,
                    title: str = "Heatmap", cmap: str = "viridis",
                    left: float = 1, top: float = 2, width: float = 8, height: float = 4,
                    title_left: float = 0.5, title_top: float = 0.3, title_width: float = 9,
                    title_height: float = 1, shapes: List[Dict[str, Any]] | None = None,
                    background_image: str | None = None):
        slide = self._add_slide_with_chart(
            title,
            "heatmap",
            title_left,
            title_top,
            title_width,
            title_height,
            shapes,
            background_image,
        )
        df = pd.DataFrame(data)
        pivot_df = df.pivot(index=y, columns=x, values=value)
        rows, cols = pivot_df.shape
        table_shape = slide.shapes.add_table(
            rows + 1, cols + 1,
            Inches(left), Inches(top), Inches(width), Inches(height)
        )
        table = table_shape.table
        table.cell(0, 0).text = f"{y}\\{x}"
        for i, col in enumerate(pivot_df.columns):
            table.cell(0, i + 1).text = str(col)
        for i, row in enumerate(pivot_df.index):
            table.cell(i + 1, 0).text = str(row)
            for j, col in enumerate(pivot_df.columns):
                value_cell = pivot_df.loc[row, col]
                if pd.notna(value_cell):
                    table.cell(i + 1, j + 1).text = f"{value_cell:.1f}"
        return self._save_presentation(title, "heatmap")

    def plot_violin_plot(
        self,
        data: List[Dict],
        category: str,
        values: str,
        title: str = "Violin Plot",
        left: float = 1,
        top: float = 1.5,
        width: float = 8,
        height: float = 5.5,
        title_left: float = 0.5,
        title_top: float = 0.3,
        title_width: float = 9,
        title_height: float = 1,
        shapes: List[Dict[str, Any]] | None = None,
        background_image: str | None = None,
    ):
        return self.plot_box_plot(
            data,
            category,
            values,
            title.replace("Violin", "Box"),
            left=left,
            top=top,
            width=width,
            height=height,
            title_left=title_left,
            title_top=title_top,
            title_width=title_width,
            title_height=title_height,
            shapes=shapes,
            background_image=background_image,
        )

    def _create_tools(self):
        return [
            {
                "type": "function",
                "function": {
                    "name": "plot_bar_chart",
                    "description": "Create a vertical bar chart for categorical data with numeric values",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "data": {"type": "array", "items": {"type": "object"}, "description": "List of records with category and value"},
                            "x": {"type": "string", "description": "Column name for categories (x-axis)"},
                            "y": {"type": "string", "description": "Column name for values (y-axis)"},
                            "title": {"type": "string", "description": "Chart title"},
                            "color": {"type": "string", "description": "Bar color"},
                            "colors": {"type": "array", "items": {"type": "string"}, "description": "Colors per bar"},
                            "xlabel": {"type": "string", "description": "X-axis label"},
                            "ylabel": {"type": "string", "description": "Y-axis label"}
                        },
                        "required": ["data", "x", "y"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "plot_horizontal_bar_chart",
                    "description": "Create a horizontal bar chart for categorical data, useful when category names are long",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "data": {"type": "array", "items": {"type": "object"}, "description": "List of records with category and value"},
                            "x": {"type": "string", "description": "Column name for categories"},
                            "y": {"type": "string", "description": "Column name for values"},
                            "title": {"type": "string", "description": "Chart title"},
                            "color": {"type": "string", "description": "Bar color"},
                            "colors": {"type": "array", "items": {"type": "string"}, "description": "Colors per bar"},
                            "xlabel": {"type": "string", "description": "X-axis label"},
                            "ylabel": {"type": "string", "description": "Y-axis label"}
                        },
                        "required": ["data", "x", "y"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "plot_line_chart",
                    "description": "Create a line chart for time series data or continuous numeric data",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "data": {"type": "array", "items": {"type": "object"}, "description": "List of records with x and y values"},
                            "x": {"type": "string", "description": "Column name for x-axis (often time or sequential data)"},
                            "y": {"type": "string", "description": "Column name for y-axis values"},
                            "title": {"type": "string", "description": "Chart title"},
                            "color": {"type": "string", "description": "Line color"},
                            "colors": {"type": "array", "items": {"type": "string"}, "description": "Colors per series"},
                            "xlabel": {"type": "string", "description": "X-axis label"},
                            "ylabel": {"type": "string", "description": "Y-axis label"},
                            "group_by": {"type": "string", "description": "Column to group by for multiple lines"}
                        },
                        "required": ["data", "x", "y"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "plot_scatter_plot",
                    "description": "Create a scatter plot to show relationship between two numeric variables",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "data": {"type": "array", "items": {"type": "object"}, "description": "List of records with x and y values"},
                            "x": {"type": "string", "description": "Column name for x-axis"},
                            "y": {"type": "string", "description": "Column name for y-axis"},
                            "title": {"type": "string", "description": "Chart title"},
                            "color": {"type": "string", "description": "Point color"},
                            "colors": {"type": "array", "items": {"type": "string"}, "description": "Colors per point"},
                            "xlabel": {"type": "string", "description": "X-axis label"},
                            "ylabel": {"type": "string", "description": "Y-axis label"},
                            "size_by": {"type": "string", "description": "Column to control point sizes"},
                            "color_by": {"type": "string", "description": "Column to control point colors"}
                        },
                        "required": ["data", "x", "y"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "plot_pie_chart",
                    "description": "Create a pie chart for showing proportions of categories",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "data": {"type": "array", "items": {"type": "object"}, "description": "List of records with category and value"},
                            "category": {"type": "string", "description": "Column name for categories"},
                            "value": {"type": "string", "description": "Column name for values"},
                            "title": {"type": "string", "description": "Chart title"},
                            "color": {"type": "string", "description": "Slice color"},
                            "colors": {"type": "array", "items": {"type": "string"}, "description": "Colors per slice"}
                        },
                        "required": ["data", "category", "value"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "plot_histogram",
                    "description": "Create a histogram for showing distribution of numeric data",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "data": {"type": "array", "items": {"type": "number"}, "description": "List of numeric values"},
                            "bins": {"type": "integer", "description": "Number of histogram bins"},
                            "title": {"type": "string", "description": "Chart title"},
                            "xlabel": {"type": "string", "description": "X-axis label"},
                            "ylabel": {"type": "string", "description": "Y-axis label"},
                            "color": {"type": "string", "description": "Bar color"},
                            "colors": {"type": "array", "items": {"type": "string"}, "description": "Colors per bin"}
                        },
                        "required": ["data"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "plot_box_plot",
                    "description": "Create a box plot for comparing distributions across categories",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "data": {"type": "array", "items": {"type": "object"}, "description": "List of records with category and numeric values"},
                            "category": {"type": "string", "description": "Column name for categories"},
                            "values": {"type": "string", "description": "Column name for numeric values"},
                            "title": {"type": "string", "description": "Chart title"}
                        },
                        "required": ["data", "category", "values"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "plot_error_bar_chart",
                    "description": "Create an error bar chart for data with confidence intervals or error ranges",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "data": {"type": "array", "items": {"type": "object"}, "description": "List of records with x, y, and error bounds"},
                            "x": {"type": "string", "description": "Column name for x-axis"},
                            "y": {"type": "string", "description": "Column name for y-axis values"},
                            "y_lower": {"type": "string", "description": "Column name for lower error bounds"},
                            "y_upper": {"type": "string", "description": "Column name for upper error bounds"},
                            "title": {"type": "string", "description": "Chart title"},
                            "xlabel": {"type": "string", "description": "X-axis label"},
                            "ylabel": {"type": "string", "description": "Y-axis label"}
                        },
                        "required": ["data", "x", "y", "y_lower", "y_upper"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "plot_stacked_bar_chart",
                    "description": "Create a stacked bar chart for showing composition of categories",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "data": {"type": "array", "items": {"type": "object"}, "description": "List of records with categories and multiple values"},
                            "x": {"type": "string", "description": "Column name for x-axis categories"},
                            "categories": {"type": "array", "items": {"type": "string"}, "description": "List of column names to stack"},
                            "title": {"type": "string", "description": "Chart title"},
                            "xlabel": {"type": "string", "description": "X-axis label"},
                            "ylabel": {"type": "string", "description": "Y-axis label"}
                        },
                        "required": ["data", "x", "categories"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "plot_grouped_bar_chart",
                    "description": "Create a grouped bar chart for comparing multiple categories side by side",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "data": {"type": "array", "items": {"type": "object"}, "description": "List of records with categories and multiple values"},
                            "x": {"type": "string", "description": "Column name for x-axis categories"},
                            "categories": {"type": "array", "items": {"type": "string"}, "description": "List of column names to group"},
                            "title": {"type": "string", "description": "Chart title"},
                            "xlabel": {"type": "string", "description": "X-axis label"},
                            "ylabel": {"type": "string", "description": "Y-axis label"}
                        },
                        "required": ["data", "x", "categories"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "plot_heatmap",
                    "description": "Create a heatmap for showing correlations or relationships in 2D data",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "data": {"type": "array", "items": {"type": "object"}, "description": "List of records with x, y, and value"},
                            "x": {"type": "string", "description": "Column name for x-axis"},
                            "y": {"type": "string", "description": "Column name for y-axis"},
                            "value": {"type": "string", "description": "Column name for values to map to colors"},
                            "title": {"type": "string", "description": "Chart title"},
                            "cmap": {"type": "string", "description": "Color map name"}
                        },
                        "required": ["data", "x", "y", "value"]
                    }
                }
            }
        ]

    def extract_and_visualize(self, text: str, max_retries: int = 5):
        enhanced_prompt = f"""
        Analyze the following text and extract any numerical data that can be visualized:

        TEXT: {text}

        Your task:
        1. Identify if there is data suitable for visualization in the text
        2. Extract the data and structure it appropriately as a JSON array
        3. Choose the most suitable chart type based on the data

        CRITICAL REQUIREMENT: You MUST include the complete data array in your function call.
        The data parameter is REQUIRED and must contain the actual extracted values.

        Example of proper function call structure:
        - For bar chart: {{"data": [{{"category": "A", "value": 150}}, {{"category": "B", "value": 230}}], "x": "category", "y": "value", "title": "Chart Title"}}
        - For pie chart: {{"data": [{{"label": "X", "value": 35}}, {{"label": "Y", "value": 28}}], "category": "label", "value": "value", "title": "Chart Title"}}
        - For line chart: {{"data": [{{"time": "Jan", "value": 50}}, {{"time": "Feb", "value": 65}}], "x": "time", "y": "value", "title": "Chart Title"}}
        - For error bar: {{"data": [{{"treatment": "A", "improvement": 15, "lower": 10, "upper": 20}}], "x": "treatment", "y": "improvement", "y_lower": "lower", "y_upper": "upper", "title": "Chart Title"}}

        Chart type guidelines:
        - Use plot_bar_chart for categorical data with single values
        - Use plot_pie_chart for proportions (parts of a whole, percentages)
        - Use plot_line_chart for time series or sequential data
        - Use plot_error_bar_chart for data with confidence intervals or error bars
        - Use plot_scatter_plot for relationships between two numeric variables

        If no suitable data for visualization is found, respond with a message explaining that no plottable data was detected.

        REMEMBER: Always include the complete "data" array with actual extracted values in your function call!
        """
        for attempt in range(1, max_retries + 1):
            try:
                if attempt > 1:
                    print(f"Retry attempt {attempt}/{max_retries} for chart generation...", file=sys.stderr)
                response = client.chat.completions.create(
                    model="gpt-4.1",
                    messages=[{"role": "user", "content": enhanced_prompt}],
                    tools=self.tools,
                    tool_choice="auto"
                )
                message = response.choices[0].message
                if message.tool_calls:
                    for tool_call in message.tool_calls:
                        function_name = tool_call.function.name
                        try:
                            function_args = json.loads(tool_call.function.arguments)
                            if 'data' in function_args and not function_args['data']:
                                continue
                            if function_name in self.plotting_functions:
                                filepath = self._create_chart_with_retry(function_name, function_args, max_retries)
                                if filepath:
                                    self.last_chart_info = {"type": function_name, "args": function_args}
                                    return filepath, self.last_chart_info
                        except Exception as e:
                            if attempt == max_retries:
                                raise e
                            continue
                    if attempt < max_retries:
                        continue
                    else:
                        return None, None
                else:
                    response_text = (message.content or '').lower()
                    if any(phrase in response_text for phrase in ['no data', 'cannot', 'unable', 'not found', 'no suitable']):
                        return None, None
                    if attempt < max_retries:
                        continue
                    else:
                        return None, None
            except Exception:
                if attempt == max_retries:
                    raise
                continue
        return None, None

    def _create_chart_with_retry(self, function_name: str, function_args: dict, max_retries: int = 5) -> Optional[str]:
        for attempt in range(1, max_retries + 1):
            try:
                if attempt > 1:
                    print(f"Chart rendering retry {attempt}/{max_retries}...", file=sys.stderr)
                self.current_presentation = None
                filepath = self.plotting_functions[function_name](**function_args)
                if filepath and os.path.exists(filepath):
                    return filepath
                else:
                    raise Exception("PowerPoint file was not created or is inaccessible")
            except Exception as e:
                if attempt < max_retries:
                    import time
                    time.sleep(1)
                else:
                    return None
        return None

def create_powerpoint_visualization_agent():
    return PowerPointVisualizationAgent()

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("output_dir")
    parser.add_argument("text_b64", nargs="?")
    parser.add_argument("--chart_b64", dest="chart_b64", default=None)
    args = parser.parse_args()
    os.environ["PPTX_DIR"] = args.output_dir
    agent = create_powerpoint_visualization_agent()

    if args.chart_b64:
        chart_json = base64.b64decode(args.chart_b64).decode("utf-8")
        chart_info = json.loads(chart_json)
        result_path = agent._create_chart_with_retry(
            chart_info.get("type"), chart_info.get("args", {})
        )
        if result_path:
            print(json.dumps({"path": result_path, "chart": chart_info}))
        else:
            print("NO_DATA")
        sys.exit(0)

    if not args.text_b64:
        print("NO_DATA")
        sys.exit(0)

    text = base64.b64decode(args.text_b64).decode("utf-8")
    result_path = None
    chart_info = None
    try:
        result_path, chart_info = agent.extract_and_visualize(text)
    except Exception as e:
        sys.stderr.write(f"error: {e}\n")
        sys.exit(1)
    if result_path:
        print(json.dumps({"path": result_path, "chart": chart_info}))
    else:
        print("NO_DATA")
