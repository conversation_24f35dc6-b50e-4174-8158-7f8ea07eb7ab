import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

// Company email configuration from environment variables
const COMPANY_EMAIL = process.env.COMPANY_EMAIL;
const COMPANY_EMAIL_PASSWORD = process.env.COMPANY_EMAIL_PASSWORD;
const COMPANY_EMAIL_SERVER = process.env.COMPANY_EMAIL_SERVER || 'smtp.gmail.com';
const COMPANY_EMAIL_PORT = process.env.COMPANY_EMAIL_PORT || '587';

// Create transporter with environment variable credentials
const transporter = nodemailer.createTransport({
  host: COMPANY_EMAIL_SERVER,
  port: parseInt(COMPANY_EMAIL_PORT, 10),
  secure: parseInt(COMPANY_EMAIL_PORT, 10) === 465, // true for 465, false for other ports
  auth: {
    user: COMPANY_EMAIL,
    pass: COMPANY_EMAIL_PASSWORD,
  },
  // Add Gmail-specific configuration
  service: 'gmail',
  tls: {
    rejectUnauthorized: false
  }
});

// Define types for the results
interface EmailResult {
  successful: string[];
  failed: Array<{ email: string; error: string }>;
}

export async function POST(request: NextRequest) {
  try {
    // Check if company email credentials are configured
    if (!COMPANY_EMAIL || !COMPANY_EMAIL_PASSWORD) {
      return NextResponse.json(
        { error: 'Company email not configured on the server' },
        { status: 500 }
      );
    }

    const { 
      recipients, 
      subject, 
      message, 
      userEmail,
      userName
    } = await request.json();

    // Validate required fields
    if (!recipients?.length || !subject || !message) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Track successful and failed emails with proper typing
    const results: EmailResult = {
      successful: [],
      failed: [],
    };

    // Format the sender name to include the user's name if provided
    const fromField = userName 
      ? `"${userName} via Evicenter" <${COMPANY_EMAIL}>`
      : `"Evicenter" <${COMPANY_EMAIL}>`;

    // Add signature to the message
    const enhancedMessage = `${message}

---
This email was sent on behalf of ${userName || 'a user'} via Evicenter.
${userEmail ? `You can reply directly to ${userEmail}.` : ''}`;

    // Send individual emails to each recipient
    for (const recipient of recipients) {
      try {
        // Prepare email data
        const mailOptions = {
          from: fromField,
          to: recipient,
          cc: userEmail || undefined, // Copy the user if email provided
          replyTo: userEmail || undefined, // Set reply-to as the user's email if provided
          subject: subject,
          text: enhancedMessage,
          html: enhancedMessage.replace(/\n/g, '<br>'), // Basic conversion of newlines to HTML breaks
        };

        // Send the email
        await transporter.sendMail(mailOptions);
        results.successful.push(recipient);
      } catch (error) {
        console.error(`Failed to send email to ${recipient}:`, error);
        results.failed.push({ email: recipient, error: (error as Error).message });
      }
    }

    // Return results
    return NextResponse.json({
      message: 'Email sending process completed',
      results: results,
    });
  } catch (error) {
    console.error('Error in send-email API route:', error);
    return NextResponse.json(
      { error: 'Failed to process email sending request', details: (error as Error).message },
      { status: 500 }
    );
  }
}
