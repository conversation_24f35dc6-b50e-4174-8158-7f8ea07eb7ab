# Firebase Studio

This is a NextJS starter in Firebase Studio.

To get started, take a look at src/app/page.tsx.

# To run the project locally
npm run dev
## PowerPoint Chart Generator
This tool extracts charts from text and regenerates them from JSON. Each chart accepts a `color` or `colors` array for customizing element colors.

# PDF to Markdown conversion
Some features require converting PDF documents to text using the
`pymupdf4llm` Python package. Install it locally with:

```bash
pip install -U pymupdf4llm
```

# Repository of country flag svg's
https://flagicons.lipis.dev/


https://mailchi.mp/nice/reminder-nice-news-for-life-sciences-7861007?e=[UNIQID]
https://www.g-ba.de/letzte-aenderungen/?rss=1
https://www.has-sante.fr/feed/Rss2.jsp?id=p_3375329
https://www.fda.gov/about-fda/contact-fda/stay-informed/rss-feeds/press-releases/rss.xml
https://www.fda.gov/about-fda/contact-fda/stay-informed/rss-feeds/drugs/rss.xml
https://www.fda.gov/about-fda/contact-fda/stay-informed/rss-feeds/biologics/rss.xml
https://us8.campaign-archive.com/feed?u=7864f766b10b8edd18f19aa56&id=5c592fb207


https://health.ec.europa.eu/node/13009/rss_en



# Scheduling tasks on Heroku
3. Heroku (if you're using Heroku)
For Heroku, use the Heroku Scheduler add-on:

Install the Heroku Scheduler add-on for your app
In the Heroku dashboard, go to your app → Resources → Heroku Scheduler
Add a new job with the command:
Set the frequency to "Daily" and choose a time

# Configuring Evicenter to send emails on behalf of users
Gmail --> Manage your Google Account (upper righthand corner) --> Security (left panel) --> 2-Step Verification ("How you sign in to Google")--> Enter password --> App Passwords --> Create a new app password for Evicenter

# React-PDF
## Localhost
eyJkYXRhIjoiZXlKMElqb2liM0puWVc1cGVtRjBhVzl1SWl3aVlYWjFJam94TnpjeU5qWTROems1TENKa2JTSTZJbXh2WTJGc2FHOXpkQ0lzSW00aU9pSmxPRFpoTnpZNU1USXhPR05pWkdJeElpd2laWGh3SWpveE56VTVNamMyTnprNUxDSmtiWFFpT2lKM2FXeGtZMkZ5WkNKOSIsInNpZ25hdHVyZSI6IlpFRjZwakFDYjRYa09Wd0E0RHEvRHdRVFE3cWVZNTJhVFVoSDViNkxxclNOczQ0Zk43OXVTZys5VDlFWUpPK0wwVzdFYzh2bklSdUlaZVNwYVRMWXY0czhnRUFEanNxVGRQVDIxcnJWWGU4ZjhOdktkcGt0aVFBL0NMcmlrR1BlUkZVM3BpTlJJMlZYd0JVMmhZbUNJMGNXMmFPOEdibUFGb1grMGwyb3JyRDhFT1BJTlNUVlVVblJlMTY3cW1KT3V2elhhOW5PV1JjVTU1blRQczJYdmpIczAzcTlHNk1iam9YQkxMcTlNQ1Ziam4xS2UxcTBIajVySG00THRsWnF3T0tPVTRXYys5dEF1MGhNWW0zdFNNaysvSnIyM3cyNEtwNWJrQWc1V0dvWGZ6eVR1VnpBV0hFbTZkNGlhMHlUUFFWSjkyVUlmZUJoaGtQKzNYaDFhQT09In0
## Evicenter
eyJkYXRhIjoiZXlKMElqb2liM0puWVc1cGVtRjBhVzl1SWl3aVlYWjFJam94TnpjM09UTTVNVGs1TENKa2JTSTZJbVYyYVdObGJuUmxjaTAxTW1Rd1kyWm1abVk0WkRFdWFHVnliMnQxWVhCd0xtTnZiU0lzSW00aU9pSmpPV0l3WkRrMU5HUXdZakl6Wm1RNUlpd2laWGh3SWpveE56WTFNekkwTnprNUxDSmtiWFFpT2lKM2FXeGtZMkZ5WkNKOSIsInNpZ25hdHVyZSI6IkwxMEpIUnQ5eGtCVUFVbjZQSnhpRXJTcEdKbGRNZVk3VE9lU0k2a1Y4VUIxU2VtMFBjeUdsRjhlWTl6RTlUUmpGSDI2UHJRK0RhK0tQMUV1bmhUR2hMUkR4TkZiN210aFVDVk02b2U0Ni94RFlGWnpTLzJtWGtWeGxnMFNrVUtBV0VIc01kdnNGNEs1ZGpwL2FLak1sd1A3Z3RuVWtWOHVZUmMyNnZLdDMvbFZPREtnbzV1MkJmaVU2YmdiM2p0RS9FYW5ubGw0OU5walhRU2x6QVdhaFVQT0o3S29sMTlIQS9ldnNkTlZiZDNhSCtXQlZUeXZkSjN4M1lrN2pCR2VIcDBOcGtPajdoU3VUcnpMb2N1dVpreEIydW55azFtYkVlOGVhaGdLSnJHMjY5QjBlcjlFUkkzUmlhcEZic3ZLeDJ2c3JRblI0MFNKeHhodGVjKzMzUT09In0

# MongoDB


# Deploy on heroku
git add .
git commit -m "codex"
git push heroku main --force


git reset --hard a71166adc5290fe5dcf5ba407c70a97311c91be1

## Workflow Designer
A drag-and-drop interface for building multi-agent workflows. Start the Python
runner with `python scripts/workflow_runner.py` and visit `/workflow-designer`.
Workflows can be saved to local storage from the page.
